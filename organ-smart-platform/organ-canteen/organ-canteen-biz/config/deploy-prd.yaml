apiVersion: apps/v1
kind: Deployment
metadata:
  name: organ-canteen #项目名称
  namespace: docker_namespace
spec:
  selector:
    matchLabels:
      app: organ-canteen #项目名称
  template:
    metadata:
      labels:
        app: organ-canteen #项目名称
    spec:
      imagePullSecrets:
      - name: harboreg-pre
      containers:
      - name: organ-canteen #项目名称
        image: image_build_name
        env:
        - name: CUR_ENV
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: profiles.active
        - name: EUREKA_DEFAULT_ZONE #映射到application中的值
          valueFrom:
            configMapKeyRef:
              name: common-env #configMap配置
              key: eureka.defaultzone #配置中的key
        - name: dingYytjToken
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: dingding.yysj.token
        - name: dingZhglToken
          valueFrom:
            configMapKeyRef:
              name: third-config
              key: dingding.zhgl.token
        - name: EUREKA_REGISTER_MYSELF #映射到application中的值
          valueFrom:
            configMapKeyRef:
              name: common-env #configMap配置
              key: eureka.register.myself #配置中的key
        - name: DB_NAME
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: mysql.name
        - name: DB_PASSWORD
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: mysql.password
        - name: DB_URL
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: mysql.url
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: redis.host
        - name: REDIS_PORT
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: redis.port
        - name: REDIS_PASSWORD
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: redis.password
        - name: REDIS_NODES
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: redis.nodes
        - name: SW_AGENT_NAME
          value: organ-canteen
        - name: SW_AGENT_COLLECTOR_BACKEND_SERVICES
          valueFrom:
            configMapKeyRef:
              key: sw.agent.backend_service
              name: skywalking-config
        volumeMounts:
        - mountPath: /home/<USER>/organ-canteen/logs/
          name: log
        ports:
        - containerPort: 80
      volumes:
      - name: log
        hostPath:
          path: /home/<USER>/organ-canteen/logs/
          type: ''
