spring:
  application:
    name: organ-canteen

  profiles:
    active: ${CUR_ENV:dev}

  main:
    allow-circular-references: true # 允许循环依赖

  # Servlet 配置
  servlet:
    # 文件上传相关配置项
    multipart:
      max-file-size: 16MB # 单个文件大小
      max-request-size: 32MB # 设置总上传的文件大小
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER # 解决 SpringFox 与 SpringBoot 2.6.x 不兼容的问题

  # Jackson 配置项
  jackson:
    serialization:
      write-dates-as-timestamps: true # 设置 Date 的格式，使用时间戳
      write-date-timestamps-as-nanoseconds: false # 设置不使用 nanoseconds 的格式。例如说 1611460870.401，而是直接 1611460870401
      write-durations-as-timestamps: true # 设置 Duration 的格式，使用时间戳
      fail-on-empty-beans: false # 允许序列化无属性的 Bean


# MyBatis Plus 的配置项
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
  global-config:
    db-config:
#      id-type: NONE # “智能”模式，基于 IdTypeEnvironmentPostProcessor + 数据源的类型，自动适配成 AUTO、INPUT 模式。
#      id-type: AUTO # 自增 ID，适合 MySQL 等直接自增的数据库
#      id-type: INPUT # 用户输入 ID，适合 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库
      id-type: ASSIGN_ID # 分配 ID，默认使用雪花算法。注意，Oracle、PostgreSQL、Kingbase、DB2、H2 数据库时，需要去除实体类上的 @KeySequence 注解
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
      select-strategy: not_empty
      insert-strategy: not_empty
  type-aliases-package: com.organ.module.*.entity


hainancrc:
  info:
    version: 1.0.0
    base-package: com.hainancrc
  web:
    admin-ui:
      url: http://127.0.0.1
  swagger:
    title: 海易信C端用户接口
    description: 海易信C端用户接口
    version: 1.0
    base-package: com.hainancrc
    enable: true

AuthCode:
  actionCode: person001
  qrcodeUrl: https://sit-app.hainancrc.com/multi#/pages/personalAuth/signedValidate/signedValidate
