package com.organ.module.cms.entity;

import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;

/**
 * 数据目录类型 DO
 *
 * <AUTHOR>
 */
@TableName("data_catalog_type")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataCatalogTypeDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 数据目录类型key
     */
    private String catalogTypeKey;
    /**
     * 数据目录父级id
     */
    private Long parentId;
    /**
     * 数据目录类型名称
     */
    private String name;
    /**
     * 排序字段
     */
    private Integer sort;
    /**
     * 更新人
     */
    private String updator;

}
