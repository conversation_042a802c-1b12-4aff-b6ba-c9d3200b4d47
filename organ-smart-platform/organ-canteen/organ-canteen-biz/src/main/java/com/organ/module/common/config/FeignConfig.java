package com.organ.module.common.config;

import com.hainancrc.framework.redis.service.RedisService;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

@Configuration
@Slf4j
public class FeignConfig implements RequestInterceptor {
    // @Autowired
    // private RedisService redisService;
    @Override
    public void apply(RequestTemplate template) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (Objects.isNull(attributes)) {
            return;
        }
        HttpServletRequest request = attributes.getRequest();
        log.info(request.getHeader("Authorization"));
        // header添加token
        template.header("Authorization", request.getHeader("Authorization"));
    }
}
