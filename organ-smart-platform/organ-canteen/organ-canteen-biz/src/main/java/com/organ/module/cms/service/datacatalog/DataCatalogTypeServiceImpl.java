package com.organ.module.cms.service.datacatalog;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.organ.module.cms.api.datacatalog.dto.*;
import com.organ.module.cms.api.datacatalog.vo.*;
import com.organ.module.cms.entity.DataCatalogTypeDO;
import com.hainancrc.framework.common.pojo.PageResult;

import com.organ.module.cms.convert.datacatalog.DataCatalogTypeConvert;
import com.organ.module.cms.mapper.datacatalog.DataCatalogTypeMapper;

import static com.hainancrc.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.organ.module.cms.api.enums.ErrorCodeConstants.*;
/**
 * 数据目录类型 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DataCatalogTypeServiceImpl implements DataCatalogTypeService {

    @Resource
    private DataCatalogTypeMapper dataCatalogTypeMapper;

    @Override
    public Long createDataCatalogType(DataCatalogTypeCreateDTO createDTO) {
        // 插入
        DataCatalogTypeDO dataCatalogType = DataCatalogTypeConvert.INSTANCE.convert(createDTO);
        dataCatalogTypeMapper.insert(dataCatalogType);
        // 返回
        return dataCatalogType.getId();
    }

    @Override
    public void updateDataCatalogType(DataCatalogTypeUpdateDTO updateDTO) {
        // 校验存在
        this.validateDataCatalogTypeExists(updateDTO.getId());
        // 更新
        DataCatalogTypeDO updateObj = DataCatalogTypeConvert.INSTANCE.convert(updateDTO);
        dataCatalogTypeMapper.updateById(updateObj);
    }

    @Override
    public void deleteDataCatalogType(Long id) {
        // 校验存在
        this.validateDataCatalogTypeExists(id);
        // 删除
        dataCatalogTypeMapper.deleteById(id);
    }

    private void validateDataCatalogTypeExists(Long id) {
        if (dataCatalogTypeMapper.selectById(id) == null) {
            throw exception(DATA_CATALOG_TYPE_NOT_EXISTS);
        }
    }

    @Override
    public DataCatalogTypeDO getDataCatalogType(Long id) {
        return dataCatalogTypeMapper.selectById(id);
    }

    @Override
    public List<DataCatalogTypeDO> getDataCatalogTypeList(Collection<Long> ids) {
        return dataCatalogTypeMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<DataCatalogTypeDO> getDataCatalogTypePage(DataCatalogTypePageDTO pageDTO) {
        return dataCatalogTypeMapper.selectPage(pageDTO);
    }

    @Override
    public List<DataCatalogTypeDO> getDataCatalogTypeList(DataCatalogTypeExportListDTO exportListDTO) {
        return dataCatalogTypeMapper.selectList(exportListDTO);
    }

}
