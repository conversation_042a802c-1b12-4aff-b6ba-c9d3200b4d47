package com.organ.module.cms.convert.datacatalog;

import java.util.*;

import com.hainancrc.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.organ.module.cms.api.datacatalog.dto.*;
import com.organ.module.cms.api.datacatalog.vo.*;
import com.organ.module.cms.entity.DataCatalogTypeDO;

/**
 * 数据目录类型 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface DataCatalogTypeConvert {

    DataCatalogTypeConvert INSTANCE = Mappers.getMapper(DataCatalogTypeConvert.class);

    DataCatalogTypeDO convert(DataCatalogTypeCreateDTO bean);

    DataCatalogTypeDO convert(DataCatalogTypeUpdateDTO bean);

    DataCatalogTypeRespVO convert(DataCatalogTypeDO bean);

    List<DataCatalogTypeRespVO> convertList(List<DataCatalogTypeDO> list);

    PageResult<DataCatalogTypeRespVO> convertPage(PageResult<DataCatalogTypeDO> page);


}
