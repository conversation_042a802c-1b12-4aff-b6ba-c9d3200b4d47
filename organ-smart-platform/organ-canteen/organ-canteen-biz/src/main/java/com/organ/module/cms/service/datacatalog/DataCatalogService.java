package com.organ.module.cms.service.datacatalog;

import java.util.*;
import javax.validation.*;
import com.organ.module.cms.api.datacatalog.dto.*;
import com.organ.module.cms.api.datacatalog.vo.*;
import com.organ.module.cms.entity.DataCatalogDO;
import com.hainancrc.framework.common.pojo.PageResult;

/**
 * 数据目录 Service 接口
 *
 * <AUTHOR>
 */
public interface DataCatalogService {

    /**
     * 创建数据目录
     *
     * @param createDTO 创建信息
     * @return 编号
     */
    Long createDataCatalog(@Valid DataCatalogCreateDTO createDTO);

    /**
     * 更新数据目录
     *
     * @param updateDTO 更新信息
     */
    void updateDataCatalog(@Valid DataCatalogUpdateDTO updateDTO);

    /**
     * 删除数据目录
     *
     * @param id 编号
     */
    void deleteDataCatalog(Long id);

    /**
     * 获得数据目录
     *
     * @param id 编号
     * @return 数据目录
     */
    DataCatalogDO getDataCatalog(Long id);

    /**
     * 获得数据目录列表
     *
     * @param ids 编号
     * @return 数据目录列表
     */
    List<DataCatalogDO> getDataCatalogList(Collection<Long> ids);

    /**
     * 获得数据目录分页
     *
     * @param pageDTO 分页查询
     * @return 数据目录分页
     */
    PageResult<DataCatalogDO> getDataCatalogPage(DataCatalogPageDTO pageDTO);

    /**
     * 获得数据目录列表
     *
     * @param exportListDTO 查询条件
     * @return 数据目录列表
     */
    List<DataCatalogDO> getDataCatalogList(DataCatalogExportListDTO exportListDTO);

}
