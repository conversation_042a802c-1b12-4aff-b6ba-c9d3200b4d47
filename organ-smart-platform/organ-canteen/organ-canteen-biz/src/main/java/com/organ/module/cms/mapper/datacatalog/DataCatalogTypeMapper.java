package com.organ.module.cms.mapper.datacatalog;

import java.util.*;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.organ.module.cms.api.datacatalog.dto.DataCatalogTypeExportListDTO;
import com.organ.module.cms.api.datacatalog.dto.DataCatalogTypePageDTO;
import com.organ.module.cms.entity.DataCatalogTypeDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 数据目录类型 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DataCatalogTypeMapper extends BaseMapperX<DataCatalogTypeDO> {

    default PageResult<DataCatalogTypeDO> selectPage(DataCatalogTypePageDTO reqDTO) {
        return selectPage(reqDTO, new LambdaQueryWrapperX<DataCatalogTypeDO>()
                .eqIfPresent(DataCatalogTypeDO::getCatalogTypeKey, reqDTO.getCatalogTypeKey())
                .eqIfPresent(DataCatalogTypeDO::getParentId, reqDTO.getParentId())
                .likeIfPresent(DataCatalogTypeDO::getName, reqDTO.getName())
                .eqIfPresent(DataCatalogTypeDO::getSort, reqDTO.getSort())
                .eqIfPresent(DataCatalogTypeDO::getUpdator, reqDTO.getUpdator())
                .orderByDesc(DataCatalogTypeDO::getId));
    }

    default List<DataCatalogTypeDO> selectList(DataCatalogTypeExportListDTO reqDTO) {
        return selectList(new LambdaQueryWrapperX<DataCatalogTypeDO>()
                .eqIfPresent(DataCatalogTypeDO::getCatalogTypeKey, reqDTO.getCatalogTypeKey())
                .eqIfPresent(DataCatalogTypeDO::getParentId, reqDTO.getParentId())
                .likeIfPresent(DataCatalogTypeDO::getName, reqDTO.getName())
                .eqIfPresent(DataCatalogTypeDO::getSort, reqDTO.getSort())
                .eqIfPresent(DataCatalogTypeDO::getUpdator, reqDTO.getUpdator())
                .orderByDesc(DataCatalogTypeDO::getId));
    }

}
