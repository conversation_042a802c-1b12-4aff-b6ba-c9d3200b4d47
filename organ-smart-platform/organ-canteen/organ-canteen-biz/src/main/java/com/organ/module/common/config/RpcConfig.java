package com.organ.module.common.config;

import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

import com.hainancrc.module.log.api.operatelog.OperateLogApi;
import com.hainancrc.module.sysuser.api.permission.PermissionApi;

@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = { OperateLogApi.class, PermissionApi.class })
public class RpcConfig {
}
