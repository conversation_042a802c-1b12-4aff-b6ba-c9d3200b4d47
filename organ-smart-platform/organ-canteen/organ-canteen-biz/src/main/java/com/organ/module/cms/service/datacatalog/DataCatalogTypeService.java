package com.organ.module.cms.service.datacatalog;

import java.util.*;
import javax.validation.*;
import com.organ.module.cms.api.datacatalog.dto.*;
import com.organ.module.cms.api.datacatalog.vo.*;
import com.organ.module.cms.entity.DataCatalogTypeDO;
import com.hainancrc.framework.common.pojo.PageResult;

/**
 * 数据目录类型 Service 接口
 *
 * <AUTHOR>
 */
public interface DataCatalogTypeService {

    /**
     * 创建数据目录类型
     *
     * @param createDTO 创建信息
     * @return 编号
     */
    Long createDataCatalogType(@Valid DataCatalogTypeCreateDTO createDTO);

    /**
     * 更新数据目录类型
     *
     * @param updateDTO 更新信息
     */
    void updateDataCatalogType(@Valid DataCatalogTypeUpdateDTO updateDTO);

    /**
     * 删除数据目录类型
     *
     * @param id 编号
     */
    void deleteDataCatalogType(Long id);

    /**
     * 获得数据目录类型
     *
     * @param id 编号
     * @return 数据目录类型
     */
    DataCatalogTypeDO getDataCatalogType(Long id);

    /**
     * 获得数据目录类型列表
     *
     * @param ids 编号
     * @return 数据目录类型列表
     */
    List<DataCatalogTypeDO> getDataCatalogTypeList(Collection<Long> ids);

    /**
     * 获得数据目录类型分页
     *
     * @param pageDTO 分页查询
     * @return 数据目录类型分页
     */
    PageResult<DataCatalogTypeDO> getDataCatalogTypePage(DataCatalogTypePageDTO pageDTO);

    /**
     * 获得数据目录类型列表
     *
     * @param exportListDTO 查询条件
     * @return 数据目录类型列表
     */
    List<DataCatalogTypeDO> getDataCatalogTypeList(DataCatalogTypeExportListDTO exportListDTO);

}
