package com.organ.module.cms.service.datacatalog;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.organ.module.cms.api.datacatalog.dto.*;
import com.organ.module.cms.api.datacatalog.vo.*;
import com.organ.module.cms.entity.DataCatalogDO;
import com.hainancrc.framework.common.pojo.PageResult;

import com.organ.module.cms.convert.datacatalog.DataCatalogConvert;
import com.organ.module.cms.mapper.datacatalog.DataCatalogMapper;

import static com.hainancrc.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.organ.module.cms.api.enums.ErrorCodeConstants.*;
/**
 * 数据目录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DataCatalogServiceImpl implements DataCatalogService {

    @Resource
    private DataCatalogMapper dataCatalogMapper;

    @Override
    public Long createDataCatalog(DataCatalogCreateDTO createDTO) {
        // 插入
        DataCatalogDO dataCatalog = DataCatalogConvert.INSTANCE.convert(createDTO);
        dataCatalogMapper.insert(dataCatalog);
        // 返回
        return dataCatalog.getId();
    }

    @Override
    public void updateDataCatalog(DataCatalogUpdateDTO updateDTO) {
        // 校验存在
        this.validateDataCatalogExists(updateDTO.getId());
        // 更新
        DataCatalogDO updateObj = DataCatalogConvert.INSTANCE.convert(updateDTO);
        dataCatalogMapper.updateById(updateObj);
    }

    @Override
    public void deleteDataCatalog(Long id) {
        // 校验存在
        this.validateDataCatalogExists(id);
        // 删除
        dataCatalogMapper.deleteById(id);
    }

    private void validateDataCatalogExists(Long id) {
        if (dataCatalogMapper.selectById(id) == null) {
            throw exception(DATA_CATALOG_NOT_EXISTS);
        }
    }

    @Override
    public DataCatalogDO getDataCatalog(Long id) {
        return dataCatalogMapper.selectById(id);
    }

    @Override
    public List<DataCatalogDO> getDataCatalogList(Collection<Long> ids) {
        return dataCatalogMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<DataCatalogDO> getDataCatalogPage(DataCatalogPageDTO pageDTO) {
        return dataCatalogMapper.selectPage(pageDTO);
    }

    @Override
    public List<DataCatalogDO> getDataCatalogList(DataCatalogExportListDTO exportListDTO) {
        return dataCatalogMapper.selectList(exportListDTO);
    }

}
