package com.organ.module.cms.controller.admin.datacatalog;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import io.swagger.annotations.*;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.common.pojo.CommonResult;
import static com.hainancrc.framework.common.pojo.CommonResult.success;


import com.organ.module.cms.api.datacatalog.dto.*;
import com.organ.module.cms.api.datacatalog.vo.*;
import com.organ.module.cms.entity.DataCatalogDO;
import com.organ.module.cms.convert.datacatalog.DataCatalogConvert;
import com.organ.module.cms.service.datacatalog.DataCatalogService;

@Api(tags = "数据目录")
@RestController
@RequestMapping("/cms/data-catalog")
@Validated
public class DataCatalogController {

    @Resource
    private DataCatalogService dataCatalogService;

    @PostMapping("/create")
    @ApiOperation("创建数据目录")
    public CommonResult<Long> createDataCatalog(@Valid @RequestBody DataCatalogCreateDTO createDTO) {
        return success(dataCatalogService.createDataCatalog(createDTO));
    }

    @PutMapping("/update")
    @ApiOperation("更新数据目录")
    public CommonResult<Boolean> updateDataCatalog(@Valid @RequestBody DataCatalogUpdateDTO updateDTO) {
        dataCatalogService.updateDataCatalog(updateDTO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @ApiOperation("删除数据目录")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Long.class)
    public CommonResult<Boolean> deleteDataCatalog(@RequestParam("id") Long id) {
        dataCatalogService.deleteDataCatalog(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得数据目录")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<DataCatalogRespVO> getDataCatalog(@RequestParam("id") Long id) {
        DataCatalogDO dataCatalog = dataCatalogService.getDataCatalog(id);
        return success(DataCatalogConvert.INSTANCE.convert(dataCatalog));
    }

    @GetMapping("/list")
    @ApiOperation("获得数据目录列表")
    @ApiImplicitParam(name = "ids", value = "编号列表", required = true, example = "1024,2048", dataTypeClass = List.class)
    public CommonResult<List<DataCatalogRespVO>> getDataCatalogList(@RequestParam("ids") Collection<Long> ids) {
        List<DataCatalogDO> list = dataCatalogService.getDataCatalogList(ids);
        return success(DataCatalogConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得数据目录分页")
    public CommonResult<PageResult<DataCatalogRespVO>> getDataCatalogPage(@Valid DataCatalogPageDTO pageDTO) {
        PageResult<DataCatalogDO> pageResult = dataCatalogService.getDataCatalogPage(pageDTO);
        return success(DataCatalogConvert.INSTANCE.convertPage(pageResult));
    }



}
