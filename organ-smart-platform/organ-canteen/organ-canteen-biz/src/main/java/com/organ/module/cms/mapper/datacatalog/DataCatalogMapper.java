package com.organ.module.cms.mapper.datacatalog;

import java.util.*;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.organ.module.cms.entity.DataCatalogDO;
import org.apache.ibatis.annotations.Mapper;
import com.organ.module.cms.api.datacatalog.dto.*;
import com.organ.module.cms.api.datacatalog.vo.*;

/**
 * 数据目录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DataCatalogMapper extends BaseMapperX<DataCatalogDO> {

    default PageResult<DataCatalogDO> selectPage(DataCatalogPageDTO reqDTO) {
        return selectPage(reqDTO, new LambdaQueryWrapperX<DataCatalogDO>()
                .eqIfPresent(DataCatalogDO::getDataCatalogTypeId, reqDTO.getDataCatalogTypeId())
                .eqIfPresent(DataCatalogDO::getDataValue, reqDTO.getDataValue())
                .eqIfPresent(DataCatalogDO::getUpdator, reqDTO.getUpdator())
                .orderByDesc(DataCatalogDO::getId));
    }

    default List<DataCatalogDO> selectList(DataCatalogExportListDTO reqDTO) {
        return selectList(new LambdaQueryWrapperX<DataCatalogDO>()
                .eqIfPresent(DataCatalogDO::getDataCatalogTypeId, reqDTO.getDataCatalogTypeId())
                .eqIfPresent(DataCatalogDO::getDataValue, reqDTO.getDataValue())
                .eqIfPresent(DataCatalogDO::getUpdator, reqDTO.getUpdator())
                .orderByDesc(DataCatalogDO::getId));
    }

}
