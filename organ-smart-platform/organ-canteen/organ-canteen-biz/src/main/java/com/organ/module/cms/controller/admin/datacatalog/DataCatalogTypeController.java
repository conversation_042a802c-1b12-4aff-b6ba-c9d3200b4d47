package com.organ.module.cms.controller.admin.datacatalog;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import io.swagger.annotations.*;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.common.pojo.CommonResult;
import static com.hainancrc.framework.common.pojo.CommonResult.success;


import com.organ.module.cms.api.datacatalog.dto.*;
import com.organ.module.cms.api.datacatalog.vo.*;
import com.organ.module.cms.entity.DataCatalogTypeDO;
import com.organ.module.cms.convert.datacatalog.DataCatalogTypeConvert;
import com.organ.module.cms.service.datacatalog.DataCatalogTypeService;

@Api(tags = "数据目录类型")
@RestController
@RequestMapping("/cms/data-catalog-type")
@Validated
public class DataCatalogTypeController {

    @Resource
    private DataCatalogTypeService dataCatalogTypeService;

    @PostMapping("/create")
    @ApiOperation("创建数据目录类型")
    public CommonResult<Long> createDataCatalogType(@Valid @RequestBody DataCatalogTypeCreateDTO createDTO) {
        return success(dataCatalogTypeService.createDataCatalogType(createDTO));
    }

    @PutMapping("/update")
    @ApiOperation("更新数据目录类型")
    public CommonResult<Boolean> updateDataCatalogType(@Valid @RequestBody DataCatalogTypeUpdateDTO updateDTO) {
        dataCatalogTypeService.updateDataCatalogType(updateDTO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @ApiOperation("删除数据目录类型")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Long.class)
    public CommonResult<Boolean> deleteDataCatalogType(@RequestParam("id") Long id) {
        dataCatalogTypeService.deleteDataCatalogType(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得数据目录类型")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<DataCatalogTypeRespVO> getDataCatalogType(@RequestParam("id") Long id) {
        DataCatalogTypeDO dataCatalogType = dataCatalogTypeService.getDataCatalogType(id);
        return success(DataCatalogTypeConvert.INSTANCE.convert(dataCatalogType));
    }

    @GetMapping("/list")
    @ApiOperation("获得数据目录类型列表")
    @ApiImplicitParam(name = "ids", value = "编号列表", required = true, example = "1024,2048", dataTypeClass = List.class)
    public CommonResult<List<DataCatalogTypeRespVO>> getDataCatalogTypeList(@RequestParam("ids") Collection<Long> ids) {
        List<DataCatalogTypeDO> list = dataCatalogTypeService.getDataCatalogTypeList(ids);
        return success(DataCatalogTypeConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得数据目录类型分页")
    public CommonResult<PageResult<DataCatalogTypeRespVO>> getDataCatalogTypePage(@Valid DataCatalogTypePageDTO pageDTO) {
        PageResult<DataCatalogTypeDO> pageResult = dataCatalogTypeService.getDataCatalogTypePage(pageDTO);
        return success(DataCatalogTypeConvert.INSTANCE.convertPage(pageResult));
    }



}
