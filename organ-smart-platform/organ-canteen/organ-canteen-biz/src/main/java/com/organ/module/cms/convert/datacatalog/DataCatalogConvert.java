package com.organ.module.cms.convert.datacatalog;

import java.util.*;

import com.hainancrc.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.organ.module.cms.api.datacatalog.dto.*;
import com.organ.module.cms.api.datacatalog.vo.*;
import com.organ.module.cms.entity.DataCatalogDO;

/**
 * 数据目录 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface DataCatalogConvert {

    DataCatalogConvert INSTANCE = Mappers.getMapper(DataCatalogConvert.class);

    DataCatalogDO convert(DataCatalogCreateDTO bean);

    DataCatalogDO convert(DataCatalogUpdateDTO bean);

    DataCatalogRespVO convert(DataCatalogDO bean);

    List<DataCatalogRespVO> convertList(List<DataCatalogDO> list);

    PageResult<DataCatalogRespVO> convertPage(PageResult<DataCatalogDO> page);


}
