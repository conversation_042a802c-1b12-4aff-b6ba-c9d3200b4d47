package com.organ.module.common.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/7/20 14:35
 * 富文本处理工具类
 */
public class RichTextUtils {

    /**
     * 编码
     * @param text 待处理的文本
     * @return 处理后的文本
     */
    public static String encode(String text) {
        return charPlus(text, 1);
    }

    /**
     * 解码
     * @param text 待处理的文本
     * @return 处理后的文本
     */
    public static String decode(String text) {
        return charPlus(text, -1);
    }

    /**
     * 每位字符ASCII码加 plus
     * @param text 待处理的文本
     * @param plus 迁移数
     * @return 处理后的文本
     */
    private static String charPlus(String text,int plus) {
        if (StringUtils.isEmpty(text)) {
            return "";
        }
        char[] chars = text.toCharArray();
        for (int i = 0; i < chars.length; i++) {
            int numericValue = chars[i];
            chars[i] = ((char) (numericValue + plus));
        }
        return String.valueOf(chars);
    }
}
