package com.organ.module.cms.entity;

import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;

/**
 * 数据目录 DO
 *
 * <AUTHOR>
 */
@TableName("data_catalog")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataCatalogDO extends BaseDO {

    /**
     * 
     */
    @TableId
    private Long id;
    /**
     * 数据目录类型id
     */
    private Long dataCatalogTypeId;
    /**
     * 数据值
     */
    private String dataValue;
    /**
     * 更新人
     */
    private String updator;

}
