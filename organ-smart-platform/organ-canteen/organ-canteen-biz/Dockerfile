FROM harbor.ffcs.cn/hn-credit/openjdk/skywalking-agent:8u252-jre

ENV LANG en_US.UTF-8
ENV LC_ALL en_US.UTF-8
ENV DEBIAN_FRONTEND noninteractive
ENV TZ=Asia/Shanghai
ENV JAVA_OPTS="-Xms512m -Xmx512m"
ENV WORK_PATH="/app"

# 就是你打包好的jar包名的名称
COPY target/organ-canteen-biz-1.0-SNAPSHOT.jar $WORK_PATH/

WORKDIR $WORK_PATH

# 声明需要暴露的端口（就是你分布式中的端口号，约定端口）
EXPOSE 80
# 配置容器启动后执行的命令

ENTRYPOINT ["java", "-javaagent:/data/skywalking-agent/skywalking-agent.jar", "-Djava.security.egd=file:/dev/./urandom", "-Dfile.encoding=UTF-8", "-jar", "/app/organ-canteen-biz-1.0-SNAPSHOT.jar"]
