-- 智慧食堂系统 - King<PERSON> v8r6版本建表语句

-- 用户信息表
DROP TABLE IF EXISTS canteen_user;
CREATE TABLE canteen_user (
    id bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    username varchar(100) NOT NULL,
    mobile varchar(20),
    dept varchar(200),
    canteen_id bigint NOT NULL,
    canteen_code varchar(50) NOT NULL,
    balance decimal(10,2) DEFAULT 0.00,
    card_type varchar(20) NOT NULL,
    user_type varchar(20) NOT NULL,
    card_expire_time timestamp,
    cross_region_flag smallint DEFAULT 1,
    dining_region varchar(30) NOT NULL,
    status varchar(20) NOT NULL DEFAULT 'ACTIVE',
    creator varchar(50),
    create_time timestamp DEFAULT CURRENT_TIMESTAMP,
    updater varchar(50),
    update_time timestamp DEFAULT CURRENT_TIMESTAMP,
    deleted smallint DEFAULT 0
);
COMMENT ON TABLE canteen_user IS '用户信息表';
COMMENT ON COLUMN canteen_user.id IS '用户id';
COMMENT ON COLUMN canteen_user.username IS '用户姓名';
COMMENT ON COLUMN canteen_user.mobile IS '用户手机号';
COMMENT ON COLUMN canteen_user.dept IS '用户所属单位';
COMMENT ON COLUMN canteen_user.canteen_id IS '就餐食堂id（原开卡食堂，关联食堂信息表）';
COMMENT ON COLUMN canteen_user.canteen_code IS '就餐食堂编码（原开卡食堂，关联食堂信息表）';
COMMENT ON COLUMN canteen_user.balance IS '用户卡里余额（预留）';
COMMENT ON COLUMN canteen_user.card_type IS '用户持卡类别（1-一类 2-二类 3-三类）';
COMMENT ON COLUMN canteen_user.user_type IS '用户类型（REGULAR-编内 CONTRACT-编外）';
COMMENT ON COLUMN canteen_user.card_expire_time IS '卡过期时间（不设置时就是永久有效）';
COMMENT ON COLUMN canteen_user.cross_region_flag IS '是否允许跨区域用餐（0-不允许 1-允许）';
COMMENT ON COLUMN canteen_user.dining_region IS '就餐区域（关联字典表 字典 dining_region）';
COMMENT ON COLUMN canteen_user.status IS '用户状态（ACTIVE-正常 DISABLED-禁用）';
COMMENT ON COLUMN canteen_user.creator IS '数据创建人';
COMMENT ON COLUMN canteen_user.create_time IS '用户数据创建时间';
COMMENT ON COLUMN canteen_user.updater IS '数据更新人';
COMMENT ON COLUMN canteen_user.update_time IS '用户数据更新时间';
COMMENT ON COLUMN canteen_user.deleted IS '用户删除标识（0-未删除 1-已删除）';

-- 食堂信息维护表
DROP TABLE IF EXISTS canteen_info;
CREATE TABLE canteen_info (
    id bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    canteen_code varchar(50) NOT NULL,
    canteen_name varchar(200) NOT NULL,
    canteen_address varchar(500),
    canteen_region varchar(30) NOT NULL,
    status varchar(20) NOT NULL DEFAULT 'ACTIVE',
    business_hours varchar(200),
    canteen_manager varchar(100),
    canteen_contact varchar(50),
    canteen_introduction text,
    creator varchar(50),
    create_time timestamp DEFAULT CURRENT_TIMESTAMP,
    updater varchar(50),
    update_time timestamp DEFAULT CURRENT_TIMESTAMP,
    deleted smallint DEFAULT 0
);
COMMENT ON TABLE canteen_info IS '食堂信息维护表';
COMMENT ON COLUMN canteen_info.id IS '食堂id';
COMMENT ON COLUMN canteen_info.canteen_code IS '食堂编码（唯一）';
COMMENT ON COLUMN canteen_info.canteen_name IS '食堂名称';
COMMENT ON COLUMN canteen_info.canteen_address IS '食堂地址';
COMMENT ON COLUMN canteen_info.canteen_region IS '食堂区域（关联字典表 字典 dining_region）';
COMMENT ON COLUMN canteen_info.status IS '食堂状态（ACTIVE-正常 DISABLED-禁用）';
COMMENT ON COLUMN canteen_info.business_hours IS '食堂营业时间';
COMMENT ON COLUMN canteen_info.canteen_manager IS '主管人';
COMMENT ON COLUMN canteen_info.canteen_contact IS '联系方式';
COMMENT ON COLUMN canteen_info.canteen_introduction IS '食堂简介';
COMMENT ON COLUMN canteen_info.creator IS '数据创建人';
COMMENT ON COLUMN canteen_info.create_time IS '食堂数据创建时间';
COMMENT ON COLUMN canteen_info.updater IS '数据更新人';
COMMENT ON COLUMN canteen_info.update_time IS '食堂数据更新时间';
COMMENT ON COLUMN canteen_info.deleted IS '食堂删除标识（0-未删除 1-已删除）';

-- 食堂餐次信息维护表
DROP TABLE IF EXISTS canteen_meal_period;
CREATE TABLE canteen_meal_period (
    id bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    canteen_id bigint NOT NULL,
    meal_name varchar(100) NOT NULL,
    start_time time NOT NULL,
    end_time time NOT NULL,
    advance_booking_open_time time NOT NULL,
    advance_booking_close_time time NOT NULL,
    advance_cancel_close_time time NOT NULL,
    advance_booking_seats integer NOT NULL DEFAULT -1,
    temp_booking_open_time time NOT NULL,
    temp_booking_close_time time NOT NULL,
    temp_cancel_close_time time NOT NULL,
    temp_booking_seats integer NOT NULL,
    status varchar(20) NOT NULL DEFAULT 'AVAILABLE',
    booking_available_status varchar(20) NOT NULL DEFAULT 'OPEN',
    creator varchar(50),
    create_time timestamp DEFAULT CURRENT_TIMESTAMP,
    updater varchar(50),
    update_time timestamp DEFAULT CURRENT_TIMESTAMP,
    deleted smallint DEFAULT 0
);
COMMENT ON TABLE canteen_meal_period IS '食堂餐次信息维护表';
COMMENT ON COLUMN canteen_meal_period.id IS '餐次id';
COMMENT ON COLUMN canteen_meal_period.canteen_id IS '食堂id（关联食堂信息表）';
COMMENT ON COLUMN canteen_meal_period.meal_name IS '餐次名称';
COMMENT ON COLUMN canteen_meal_period.start_time IS '餐次开始时间';
COMMENT ON COLUMN canteen_meal_period.end_time IS '餐次结束时间';
COMMENT ON COLUMN canteen_meal_period.advance_booking_open_time IS '提前预约开放时间';
COMMENT ON COLUMN canteen_meal_period.advance_booking_close_time IS '提前预约截止时间';
COMMENT ON COLUMN canteen_meal_period.advance_cancel_close_time IS '提前预约取消截止时间';
COMMENT ON COLUMN canteen_meal_period.advance_booking_seats IS '提前预约席位数（-1表示无限制）';
COMMENT ON COLUMN canteen_meal_period.temp_booking_open_time IS '临时预约开放时间';
COMMENT ON COLUMN canteen_meal_period.temp_booking_close_time IS '临时预约截止时间';
COMMENT ON COLUMN canteen_meal_period.temp_cancel_close_time IS '临时预约取消截止时间';
COMMENT ON COLUMN canteen_meal_period.temp_booking_seats IS '临时预约席位数（临时预约基础额度）';
COMMENT ON COLUMN canteen_meal_period.status IS '餐次营业状态(AVAILABLE-营业 UNAVAILABLEE-未营业）';
COMMENT ON COLUMN canteen_meal_period.booking_available_status IS '预约可用状态（OPEN-开放预约 CLOSED-关闭预约）';
COMMENT ON COLUMN canteen_meal_period.creator IS '数据创建人';
COMMENT ON COLUMN canteen_meal_period.create_time IS '餐次数据创建时间';
COMMENT ON COLUMN canteen_meal_period.updater IS '数据更新人';
COMMENT ON COLUMN canteen_meal_period.update_time IS '餐次数据更新时间';
COMMENT ON COLUMN canteen_meal_period.deleted IS '餐次删除标识（0-未删除 1-已删除）';

-- 食堂餐次额度管理表
DROP TABLE IF EXISTS canteen_meal_quota;
CREATE TABLE canteen_meal_quota (
    id bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    meal_period_id bigint NOT NULL,
    quota_date date NOT NULL,
    quota_count integer NOT NULL,
    queta_left integer NOT NULL,
    publish_status varchar(20) NOT NULL DEFAULT 'UNPUBLISHED',
    publisher varchar(50),
    publish_time timestamp,
    creator varchar(50),
    create_time timestamp DEFAULT CURRENT_TIMESTAMP,
    updater varchar(50),
    update_time timestamp DEFAULT CURRENT_TIMESTAMP,
    deleted smallint DEFAULT 0
);
COMMENT ON TABLE canteen_meal_quota IS '食堂餐次额度管理表';
COMMENT ON COLUMN canteen_meal_quota.id IS '数据id';
COMMENT ON COLUMN canteen_meal_quota.meal_period_id IS '餐次id';
COMMENT ON COLUMN canteen_meal_quota.quota_date IS '日期';
COMMENT ON COLUMN canteen_meal_quota.quota_count IS '当天临时预约总额度';
COMMENT ON COLUMN canteen_meal_quota.queta_left IS '当天临时预约剩余额度';
COMMENT ON COLUMN canteen_meal_quota.publish_status IS '数据状态（UNPUBLISHED-未发布 PUBLISHED-已发布）';
COMMENT ON COLUMN canteen_meal_quota.publisher IS '发布人';
COMMENT ON COLUMN canteen_meal_quota.publish_time IS '发布时间';
COMMENT ON COLUMN canteen_meal_quota.creator IS '数据创建人';
COMMENT ON COLUMN canteen_meal_quota.create_time IS '额度数据创建时间';
COMMENT ON COLUMN canteen_meal_quota.updater IS '数据更新人';
COMMENT ON COLUMN canteen_meal_quota.update_time IS '额度数据更新时间';
COMMENT ON COLUMN canteen_meal_quota.deleted IS '额度删除标识（0-未删除 1-已删除）';

-- 催发请求信息维护表
DROP TABLE IF EXISTS canteen_urge_request;
CREATE TABLE canteen_urge_request (
    id bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    meal_quota_id bigint NOT NULL,
    status varchar(20) NOT NULL,
    rejected_reason varchar(200),
    creator varchar(50),
    create_time timestamp DEFAULT CURRENT_TIMESTAMP,
    updater varchar(50),
    update_time timestamp DEFAULT CURRENT_TIMESTAMP,
    deleted smallint DEFAULT 0
);
COMMENT ON TABLE canteen_urge_request IS '催发请求信息维护表';
COMMENT ON COLUMN canteen_urge_request.id IS '催发请求id';
COMMENT ON COLUMN canteen_urge_request.meal_quota_id IS '餐次额度id（关联食堂餐次额度管理表）';
COMMENT ON COLUMN canteen_urge_request.status IS '数据状态（SUBMITTED-待处理 APPROVED-已通过 REJECTED-已驳回）';
COMMENT ON COLUMN canteen_urge_request.rejected_reason IS '驳回原因';
COMMENT ON COLUMN canteen_urge_request.creator IS '数据创建人id（关联用户表id）';
COMMENT ON COLUMN canteen_urge_request.create_time IS '数据创建时间';
COMMENT ON COLUMN canteen_urge_request.updater IS '数据更新操作人';
COMMENT ON COLUMN canteen_urge_request.update_time IS '数据更新时间';
COMMENT ON COLUMN canteen_urge_request.deleted IS '催发请求删除标识（0-未删除 1-已删除）';

-- 预约记录表
DROP TABLE IF EXISTS canteen_booking;
CREATE TABLE canteen_booking (
    id bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    meal_period_id bigint NOT NULL,
    user_id bigint NOT NULL,
    booking_type varchar(20) NOT NULL,
    booking_status varchar(20) NOT NULL DEFAULT 'BOOKED',
    meal_date date NOT NULL,
    booking_code varchar(50),
    creator varchar(50),
    create_time timestamp DEFAULT CURRENT_TIMESTAMP,
    updater varchar(50),
    update_time timestamp DEFAULT CURRENT_TIMESTAMP,
    deleted smallint DEFAULT 0
);
COMMENT ON TABLE canteen_booking IS '预约记录表';
COMMENT ON COLUMN canteen_booking.id IS '预约记录id';
COMMENT ON COLUMN canteen_booking.meal_period_id IS '餐次id';
COMMENT ON COLUMN canteen_booking.user_id IS '用户id';
COMMENT ON COLUMN canteen_booking.booking_type IS '预约类型（ADVANCED-提前预约 TEMPORARY-临时预约）';
COMMENT ON COLUMN canteen_booking.booking_status IS '预约状态（BOOKED-待使用 CANCELLED-已取消 VERIFIED-已使用 EXPIRED-已失效）';
COMMENT ON COLUMN canteen_booking.meal_date IS '预约用餐日期';
COMMENT ON COLUMN canteen_booking.booking_code IS '预约码Code';
COMMENT ON COLUMN canteen_booking.creator IS '数据创建人';
COMMENT ON COLUMN canteen_booking.create_time IS '预约数据创建时间';
COMMENT ON COLUMN canteen_booking.updater IS '数据更新人';
COMMENT ON COLUMN canteen_booking.update_time IS '预约数据更新时间';
COMMENT ON COLUMN canteen_booking.deleted IS '预约删除标识（0-未删除 1-已删除）';

-- 操作日志表
DROP TABLE IF EXISTS canteen_operation_log;
CREATE TABLE canteen_operation_log (
    id bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    log_module varchar(100) NOT NULL,
    operation_content varchar(500) NOT NULL,
    before_data text,
    after_data text,
    operator varchar(50) NOT NULL,
    operation_time timestamp NOT NULL,
    creator varchar(50),
    create_time timestamp DEFAULT CURRENT_TIMESTAMP,
    updater varchar(50),
    update_time timestamp DEFAULT CURRENT_TIMESTAMP,
    deleted smallint DEFAULT 0
);
COMMENT ON TABLE canteen_operation_log IS '操作日志表';
COMMENT ON COLUMN canteen_operation_log.id IS '日志记录id';
COMMENT ON COLUMN canteen_operation_log.log_module IS '日志类型（所属业务模块）';
COMMENT ON COLUMN canteen_operation_log.operation_content IS '操作内容（如：额度调整/手动核销/处罚用户）';
COMMENT ON COLUMN canteen_operation_log.before_data IS '操作前数据';
COMMENT ON COLUMN canteen_operation_log.after_data IS '操作后数据';
COMMENT ON COLUMN canteen_operation_log.operator IS '操作人';
COMMENT ON COLUMN canteen_operation_log.operation_time IS '操作时间';
COMMENT ON COLUMN canteen_operation_log.creator IS '创建人';
COMMENT ON COLUMN canteen_operation_log.create_time IS '创建时间';
COMMENT ON COLUMN canteen_operation_log.updater IS '更新人';
COMMENT ON COLUMN canteen_operation_log.update_time IS '更新时间';
COMMENT ON COLUMN canteen_operation_log.deleted IS '日志删除标识（0-未删除 1-已删除）';

-- 核销记录表
DROP TABLE IF EXISTS checkin_record;
CREATE TABLE checkin_record (
  id BIGINT NOT NULL GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  booking_id BIGINT NOT NULL,
  checkin_type VARCHAR(15) DEFAULT 'NORMAL',
  checkin_time TIMESTAMP NOT NULL,
  checkin_canteen_id BIGINT,
  checkin_canteen_code VARCHAR(50),
  checkin_person VARCHAR(50),
  remark TEXT,
  source VARCHAR(10) DEFAULT 'SYSTEM',
  creator VARCHAR(50) DEFAULT '',
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updater VARCHAR(50) DEFAULT '',
  update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  deleted SMALLINT DEFAULT 0
);
COMMENT ON TABLE checkin_record IS '核销记录表';
COMMENT ON COLUMN checkin_record.id IS '主键ID';
COMMENT ON COLUMN checkin_record.booking_id IS '预约信息ID';
COMMENT ON COLUMN checkin_record.checkin_type IS '核销类型（NORMAL-正常核销, SPECIAL-特殊核销, REPLACEMENT-补录核销）';
COMMENT ON COLUMN checkin_record.checkin_time IS '核销时间';
COMMENT ON COLUMN checkin_record.checkin_canteen_id IS '核销食堂ID (关联食堂表)';
COMMENT ON COLUMN checkin_record.checkin_canteen_code IS '核销食堂编码（关联食堂表）';
COMMENT ON COLUMN checkin_record.checkin_person IS '核销员';
COMMENT ON COLUMN checkin_record.remark IS '备注说明';
COMMENT ON COLUMN checkin_record.source IS '数据来源（SYSTEM-系统自动, MANUAL-手工录入）';
COMMENT ON COLUMN checkin_record.creator IS '创建人';
COMMENT ON COLUMN checkin_record.create_time IS '创建时间';
COMMENT ON COLUMN checkin_record.updater IS '更新人';
COMMENT ON COLUMN checkin_record.update_time IS '更新时间';
COMMENT ON COLUMN checkin_record.deleted IS '逻辑删除标识（0-未删除 1-已删除）';

-- 消息通知表
DROP TABLE IF EXISTS canteen_notification;
CREATE TABLE canteen_notification (
    id bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    title varchar(200) NOT NULL,
    content text NOT NULL,
    notification_type varchar(20) NOT NULL,
    target_user_ids text NOT NULL,
    target_channels varchar(200) NOT NULL DEFAULT 'APP',
    send_status varchar(20) NOT NULL DEFAULT 'PENDING',
    send_time timestamp,
    external_send_status text,
    external_send_time timestamp,
    creator varchar(50),
    create_time timestamp DEFAULT CURRENT_TIMESTAMP,
    updater varchar(50),
    update_time timestamp DEFAULT CURRENT_TIMESTAMP,
    deleted smallint DEFAULT 0
);
COMMENT ON TABLE canteen_notification IS '消息通知表';
COMMENT ON COLUMN canteen_notification.id IS '通知id';
COMMENT ON COLUMN canteen_notification.title IS '通知标题';
COMMENT ON COLUMN canteen_notification.content IS '通知内容';
COMMENT ON COLUMN canteen_notification.notification_type IS '通知类型（ANNOUNCEMENT-公告 SYSTEM_MSG-系统消息）';
COMMENT ON COLUMN canteen_notification.target_user_ids IS '消息接收用户ID集合';
COMMENT ON COLUMN canteen_notification.target_channels IS '消息接收渠道集合(比如海政通、内部消息等，多个渠道使用英文逗号分隔开如： HZT,INTERNAL_MSG)';
COMMENT ON COLUMN canteen_notification.send_status IS '发送状态（PENDING-待发送 SENT-已发送 FAILED-发送失败）';
COMMENT ON COLUMN canteen_notification.send_time IS '发送时间';
COMMENT ON COLUMN canteen_notification.external_send_status IS '外部渠道发送状态（存储JSON对象文本格式如： {"HZT": "PENDING", "INTERNAL_MSG": "FAILED"}）';
COMMENT ON COLUMN canteen_notification.external_send_time IS '外部渠道发送时间';
COMMENT ON COLUMN canteen_notification.creator IS '创建人';
COMMENT ON COLUMN canteen_notification.create_time IS '创建时间';
COMMENT ON COLUMN canteen_notification.updater IS '更新人';
COMMENT ON COLUMN canteen_notification.update_time IS '更新时间';
COMMENT ON COLUMN canteen_notification.deleted IS '通知删除标识（0-未删除 1-已删除）';

-- 消息通知接收记录表
DROP TABLE IF EXISTS canteen_notification_reception;
CREATE TABLE canteen_notification_reception (
    id bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    notification_id bigint NOT NULL,
    user_id bigint NOT NULL,
    read_status varchar(20) NOT NULL DEFAULT 'UNREAD',
    read_time timestamp NOT NULL,
    creator varchar(50),
    create_time timestamp DEFAULT CURRENT_TIMESTAMP,
    updater varchar(50),
    update_time timestamp DEFAULT CURRENT_TIMESTAMP,
    deleted smallint DEFAULT 0
);
COMMENT ON TABLE canteen_notification_reception IS '消息通知已读记录表';
COMMENT ON COLUMN canteen_notification_reception.id IS '接收记录id';
COMMENT ON COLUMN canteen_notification_reception.notification_id IS '通知id';
COMMENT ON COLUMN canteen_notification_reception.user_id IS '用户id';
COMMENT ON COLUMN canteen_notification_reception.read_status IS '已读状态（UNREAD-未读 READ-已读）';
COMMENT ON COLUMN canteen_notification_reception.read_time IS '已读时间';
COMMENT ON COLUMN canteen_notification_reception.creator IS '创建人';
COMMENT ON COLUMN canteen_notification_reception.create_time IS '创建时间';
COMMENT ON COLUMN canteen_notification_reception.updater IS '更新人';
COMMENT ON COLUMN canteen_notification_reception.update_time IS '更新时间';
COMMENT ON COLUMN canteen_notification_reception.deleted IS '已读记录删除标识（0-未删除 1-已删除）';

-- 消息通知发送记录表
DROP TABLE IF EXISTS canteen_notification_send_record;
CREATE TABLE canteen_notification_send_record (
    id bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    notification_id bigint NOT NULL,
    channel varchar(50) NOT NULL,
    send_status varchar(20) NOT NULL DEFAULT 'PENDING',
    send_time timestamp NOT NULL,
    external_send_status text,
    external_send_time timestamp,
    creator varchar(50),
    create_time timestamp DEFAULT CURRENT_TIMESTAMP,
    updater varchar(50),
    update_time timestamp DEFAULT CURRENT_TIMESTAMP,
    deleted smallint DEFAULT 0
);
COMMENT ON TABLE canteen_notification_send_record IS '消息通知发送记录表';
COMMENT ON COLUMN canteen_notification_send_record.id IS '发送记录id';
COMMENT ON COLUMN canteen_notification_send_record.notification_id IS '通知id';
COMMENT ON COLUMN canteen_notification_send_record.channel IS '发送渠道';
COMMENT ON COLUMN canteen_notification_send_record.send_status IS '发送状态（PENDING-待发送 SENT-已发送 FAILED-发送失败）';
COMMENT ON COLUMN canteen_notification_send_record.send_time IS '发送时间';
COMMENT ON COLUMN canteen_notification_send_record.external_send_status IS '外部渠道发送状态';
COMMENT ON COLUMN canteen_notification_send_record.external_send_time IS '外部渠道发送时间';
COMMENT ON COLUMN canteen_notification_send_record.creator IS '创建人';
COMMENT ON COLUMN canteen_notification_send_record.create_time IS '创建时间';
COMMENT ON COLUMN canteen_notification_send_record.updater IS '更新人';
COMMENT ON COLUMN canteen_notification_send_record.update_time IS '更新时间';
COMMENT ON COLUMN canteen_notification_send_record.deleted IS '发送记录删除标识（0-未删除 1-已删除）';
