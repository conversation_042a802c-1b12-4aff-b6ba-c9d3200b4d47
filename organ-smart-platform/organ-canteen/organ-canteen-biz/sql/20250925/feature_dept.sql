-- 组织架构表（部门表）
DROP TABLE IF EXISTS user_dept;
CREATE TABLE user_dept (
    id BIGINT NOT NULL GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    dept_name VARCHAR(100) NOT NULL,
    short_name VARCHAR(50),
    dept_code VARCHAR(100) NOT NULL,
    parent_id BIGINT DEFAULT 0,
    tree_path VARCHAR(500) NOT NULL,
    sort SMALLINT DEFAULT 1,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    creator VA<PERSON>HA<PERSON>(50),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updater VARCHAR(50),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted SMALLINT DEFAULT 0
);
COMMENT ON TABLE user_dept IS '组织架构表（部门表）';

COMMENT ON COLUMN user_dept.id IS '主键';
COMMENT ON COLUMN user_dept.dept_name IS '组织全称';
COMMENT ON COLUMN user_dept.short_name IS '组织简称';
COMMENT ON COLUMN user_dept.dept_code IS '组织编码（唯一）';
COMMENT ON COLUMN user_dept.parent_id IS '父级组织ID（0表示顶级节点）';
COMMENT ON COLUMN user_dept.tree_path IS '组织路径（格式如：0,1,2 表示从根到当前节点的路径）';
COMMENT ON COLUMN user_dept.sort IS '显示顺序';
COMMENT ON COLUMN user_dept.status IS '状态（ACTIVE-启用 DISABLED-禁用）';
COMMENT ON COLUMN user_dept.creator IS '创建人';
COMMENT ON COLUMN user_dept.create_time IS '创建时间';
COMMENT ON COLUMN user_dept.updater IS '更新人';
COMMENT ON COLUMN user_dept.update_time IS '更新时间';
COMMENT ON COLUMN user_dept.deleted IS '逻辑删除标识（0-未删除 1-已删除）';