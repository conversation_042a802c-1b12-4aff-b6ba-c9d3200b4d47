<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>hainancrc</groupId>
        <artifactId>organ-canteen</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <artifactId>organ-canteen-biz</artifactId>
    <packaging>jar</packaging>
    <name>organ-canteen-biz</name>
    <url>http://maven.apache.org</url>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>3.8.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>hainancrc</groupId>
            <artifactId>hncrc-spring-boot-starter-web</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <!-- DB 相关 -->
        <dependency>
            <groupId>hainancrc</groupId>
            <artifactId>hncrc-spring-boot-starter-mybatis</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <version>2.5.5</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>hainancrc</groupId>
            <artifactId>organ-canteen-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>hainancrc</groupId>
            <artifactId>hncrc-spring-boot-starter-operatelog</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <!-- 引入消息服务 -->
        <dependency>
            <groupId>hainancrc</groupId>
            <artifactId>hncrc-spring-boot-starter-rpc</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <!-- kingbase -->
        <dependency>
            <groupId>com.kingbase</groupId>
            <artifactId>kingbase8</artifactId>
            <version>8.6.0</version>
        </dependency>
        <!-- spring-boot-devtools 开发自动重启 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
        </dependency>
        <dependency>
            <groupId>hainancrc</groupId>
            <artifactId>hncrc-spring-boot-starter-redis</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>hainancrc</groupId>
            <artifactId>hncrc-spring-boot-starter-security</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>hainancrc</groupId>-->
        <!--            <artifactId>hncrc-spring-boot-starter-tenant</artifactId>-->
        <!--            <version>1.0-SNAPSHOT</version>-->
        <!--        </dependency>-->
        <!-- 日志 -->
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-trace</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-logback-1.x</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.5.5</version>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>