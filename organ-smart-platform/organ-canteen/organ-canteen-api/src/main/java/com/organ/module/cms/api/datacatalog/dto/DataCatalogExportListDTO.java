package com.organ.module.cms.api.datacatalog.dto;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;
import com.hainancrc.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;


@ApiModel(value = "数据目录 Excel 导出 Request VO", description = "参数和 DataCatalogPageReqVO 是一致的")
@Data
public class DataCatalogExportListDTO {

    @ApiModelProperty(value = "数据目录类型id")
    private Long dataCatalogTypeId;
    @ApiModelProperty(value = "数据值")
    private String dataValue;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "更新人")
    private String updator;
}
