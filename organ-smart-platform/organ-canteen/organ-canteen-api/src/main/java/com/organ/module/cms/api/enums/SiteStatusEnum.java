package com.organ.module.cms.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SiteStatusEnum {
    ENABLED("enabled","启用"),
    DISABLED("disabled","停用")
    ;

    private String key;

    private String display;

    public static SiteStatusEnum match(String key) {
        SiteStatusEnum result = null;
        for (SiteStatusEnum s : values()) {
            if (s.getKey().equals(key)) {
                result = s;
                break;
            }
        }
        return result;
    }
}
