package com.organ.module.cms.api.enums;

import com.hainancrc.framework.common.exception.ErrorCode;
// ========== 网站基本信息 TODO 补充编号 ==========

public interface ErrorCodeConstants {
    ErrorCode WEBSITE_INFORMATION_NOT_EXISTS = new ErrorCode(60010001, "网站基本信息不存在");
    ErrorCode WEBSITE_DESCRIBE_NOT_EXISTS = new ErrorCode(50010001, "网站描述不存在");
    ErrorCode WEBSITE_NODE_NOT_EXISTS = new ErrorCode(50010002, "该信易贷节点已不存在，请重新获取最新数据");
    ErrorCode WEBSITE_NODE_401 = new ErrorCode(50010002, "该信易贷节点已被禁用");
    ErrorCode DATA_CATALOG_NOT_EXISTS = new ErrorCode(70010002, "数据目录不存在");
    ErrorCode DATA_CATALOG_TYPE_NOT_EXISTS = new ErrorCode(90010002, "数据目录类型不存在");
    ErrorCode SITE_BUSINESS_RELATION_NOT_EXISTS = new ErrorCode(80010002, "中间表不存在");
    ErrorCode SITE_NOT_EXISTS = new ErrorCode(80020002, "站点不存在");
    ErrorCode WEBSITE_BANNERS_MANAGE_NOT_EXISTS = new ErrorCode(80030002, "banner不存在");
    ErrorCode WEBSITE_NEWS_MANAGE_NOT_EXISTS = new ErrorCode(80030002, "banner不存在");

}
