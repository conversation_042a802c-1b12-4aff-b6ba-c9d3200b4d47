package com.organ.module.cms.api.datacatalog.dto;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

@ApiModel("数据目录更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DataCatalogUpdateDTO extends DataCatalogBaseDTO {

    @ApiModelProperty(value = "", required = true)
    @NotNull(message = "不能为空")
    private Long id;

}
