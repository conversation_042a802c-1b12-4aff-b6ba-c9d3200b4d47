package com.organ.module.cms.api.datacatalog.dto;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

/**
* 数据目录
*/
@Data
public class DataCatalogBaseDTO {

    @ApiModelProperty(value = "数据目录类型id")
    private Long dataCatalogTypeId;

    @ApiModelProperty(value = "数据值")
    private String dataValue;

    @ApiModelProperty(value = "更新人")
    private String updator;

}
