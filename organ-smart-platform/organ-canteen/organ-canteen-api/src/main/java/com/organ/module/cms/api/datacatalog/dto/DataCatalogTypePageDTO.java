package com.organ.module.cms.api.datacatalog.dto;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;
import com.hainancrc.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;


@ApiModel("数据目录类型分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DataCatalogTypePageDTO extends PageParam {


    @ApiModelProperty(value = "数据目录类型key")
    private String catalogTypeKey;

    @ApiModelProperty(value = "数据目录父级id")
    private Long parentId;

    @ApiModelProperty(value = "数据目录类型名称")
    private String name;

    @ApiModelProperty(value = "排序字段")
    private Integer sort;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新人")
    private String updator;
}
