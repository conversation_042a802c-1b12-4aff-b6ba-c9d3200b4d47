package com.organ.module.cms.api.datacatalog.vo;

import lombok.*;
import java.util.*;

import com.organ.module.cms.api.datacatalog.dto.*;

import io.swagger.annotations.*;

@ApiModel("数据目录类型 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DataCatalogTypeRespVO extends DataCatalogTypeBaseDTO {

    @ApiModelProperty(value = "主键id", required = true)
    private Long id;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

}
