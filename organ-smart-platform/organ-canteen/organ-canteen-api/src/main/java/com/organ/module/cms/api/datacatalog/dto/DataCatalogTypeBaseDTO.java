package com.organ.module.cms.api.datacatalog.dto;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

/**
* 数据目录类型
*/
@Data
public class DataCatalogTypeBaseDTO {

    @ApiModelProperty(value = "数据目录类型key")
    private String catalogTypeKey;

    @ApiModelProperty(value = "数据目录父级id")
    private Long parentId;

    @ApiModelProperty(value = "数据目录类型名称")
    private String name;

    @ApiModelProperty(value = "排序字段")
    private Integer sort;

    @ApiModelProperty(value = "更新人")
    private String updator;

}
