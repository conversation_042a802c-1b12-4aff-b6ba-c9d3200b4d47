{"version": "2.0.0", "tasks": [{"label": "Sleep 9s", "command": "sleep", "type": "shell", "args": ["9"]}, {"label": "Start FE PC Dev Server", "type": "shell", "command": "npm", "args": ["run", "dev"], "options": {"cwd": "${workspaceFolder}/organ-smart-platform-web"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "isBackground": true, "problemMatcher": {"owner": "vite", "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}, "background": {"activeOnStart": true, "beginsPattern": "^.*Local:.*$", "endsPattern": "^.*ready in.*$"}}}, {"label": "Start FE Mobile Dev Server", "type": "shell", "command": "yarn", "args": ["run", "dev"], "options": {"cwd": "${workspaceFolder}/organ-smart-platform-app"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "isBackground": true, "problemMatcher": {"owner": "vite", "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}, "background": {"activeOnStart": true, "beginsPattern": "^.*Local:.*$", "endsPattern": "^.*ready in.*$"}}}, {"label": "Pre-launch FE PC", "dependsOrder": "sequence", "dependsOn": ["Sleep 9s", "Start FE PC Dev Server", "Sleep 9s"]}, {"label": "Pre-launch FE Mobile", "dependsOrder": "sequence", "dependsOn": ["Sleep 9s", "Start FE Mobile Dev Server", "Sleep 9s"]}]}