const { defineConfig } = require("@vue/cli-service");

console.log("当前的开发模式为", process.env.NODE_ENV);
console.log("应用运行环境变量", process.env.VUE_APP_ENV);

module.exports = defineConfig({
  // publicPath:'/',
  // publicPath:'auto',
  publicPath: process.env.VUE_APP_DOMAIN_MODE === "true" ? "/tgxy/" : "/",
  transpileDependencies: true,
  configureWebpack: {
    plugins: [
      require("unplugin-auto-import/webpack").default({
        imports: ["vue", "pinia", "@vueuse/core"],
        eslintrc: {
          enabled: true,
        },
        dirs: ["./src/store/modules", "./src/router"],
        dts: "./types/auto-imports.d.ts",
      }),
    ],
    devtool: "source-map",
  },
  devServer: {
    historyApiFallback: true,
    client: {
      // 当出现编译错误或警告时，在浏览器中显示全屏覆盖
      overlay: false,
    },
    hot: true,
    port: 9599,
    proxy: {
      // 文启 鉴权接口
      // '/api/creditreports': {
      //     target: 'http://172.16.101.49:9001',
      //     changeOrigin: true,
      //     pathRewrite: { '^/api': '' }, //authorization
      // },
      "/api": {
        // target: 'http://10.111.240.11:8073', // 正式地址
        // target: 'http://10.131.75.167:9024/api', // 正式地址
        target: "http://10.131.75.167:9037/", // 正式地址
        changeOrigin: true,
        // pathRewrite: { '^/api': '' }, //authorization
      },
    },
  },
  chainWebpack: (config) => {
    config.plugin("eslint").tap((args) => {
      args[0].fix = true;
      return args;
    });
  },
});
