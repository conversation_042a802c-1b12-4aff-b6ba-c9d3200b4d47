{"name": "organ-smart-platform-web", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "build:sit": "vue-cli-service build --mode sit --no-module", "build:pre": "vue-cli-service build --mode pre --no-module", "build:prod": "vue-cli-service build --mode production --no-module", "build:pd": "vue-cli-service build --mode productiondomain --no-module", "zip": "npm run build:pboc-prod && rm -rf ./dist.tar && tar -czvf dist.tar dist", "analyze": "webpack --env mode=analyze", "lint": "vue-cli-service lint", "eslint:fix": "eslint ./src/* --fix"}, "dependencies": {"@liuarui/postcss-px-to-viewport": "^1.0.1", "element-ui": "2.15.14", "nprogress": "0.2.0", "pinia": "2.1.4", "qrcode": "^1.5.4", "vue": "2.7.16", "vue-router": "3", "vuex": "3", "@cell-x/el-table-sticky": "^1.0.4", "@vueuse/core": "^11.3.0", "axios": "1.7.7", "babel-polyfill": "6.26.0", "core-js": "3.38.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "echarts": "5.5.1", "echarts-gl": "^2.0.9", "echarts-wordcloud": "^2.1.0", "element-tiptap": "1.26.2", "he": "^1.2.0", "html2canvas": "1.4.1", "jquery": "^3.7.1", "js-base64": "^3.7.7", "js-beautify": "^1.15.1", "js-md5": "^0.8.3", "js-pinyin": "^0.2.7", "jspdf": "2.5.1", "less": "4.2.0", "less-loader": "10", "lodash-es": "4.17.21", "moment": "^2.29.1", "snowflake-id": "^1.1.0", "swiper": "5.4.5", "vue-codemirror": "^4.0.6", "vue-danmaku": "^1.7.2", "vue-demi": "0.14.10", "vue-i18n": "8.2.1", "vue-pdf-embed": "1.1.6", "vue-wordcloud": "^1.1.1", "vue2-water-marker": "^0.0.2", "vuedraggable": "^2.24.3", "vuescroll": "^4.18.1", "wangeditor": "^4.7.15", "xlsx": "^0.17.0", "xlsx-js-style": "^1.2.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@liuarui/postcss-px-to-viewport": "^1.0.1", "@types/crypto-js": "^4.2.2", "@types/he": "^1.2.3", "@types/lodash-es": "4.17.12", "@types/node": "^22.13.10", "@types/nprogress": "^0.2.3", "@types/path-browserify": "^1.0.3", "@types/qrcode": "^1.5.5", "@types/swiper": "5", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-typescript": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/eslint-config-typescript": "^9.1.0", "babel-eslint": "^10.1.0", "eslint": "^7.32.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-promise": "6.1.1", "eslint-plugin-vue": "^8.0.3", "postcss": "7.0.39", "postcss-loader": "7.0.0", "prettier": "^2.8.8", "sass": "1.23.7", "sass-loader": "^8.0.0", "sass-resources-loader": "2.0.3", "typescript": "5.1.5", "unplugin-auto-import": "^0.18.3", "vue-cli-plugin-pinia": "~0.2.4", "vue-template-compiler": "2.7.16"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8", "chrome >= 60"]}