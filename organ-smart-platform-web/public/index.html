<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
    />
    <link rel="icon" href="<%= BASE_URL %>favicon.ico" />
    <title>
      海南国际贸易“单一窗口”（口岸监管服务信息系统）- 通关信用管理系统
    </title>
    <script
      async
      src="https://uaa.digitalhainan.com.cn/js/sdk/fire-bear-browser-release-1.0.0.min.js"
      onload="fireSdkOnload()"
    ></script>
    <script
      async
      src="https://uaa.digitalhainan.com.cn/js/sdk/native-bear-monitor-release-1.0.0.min.js"
      onload="nativeSdkOnload()"
    ></script>
    <!-- 引入 jssdk SDK -->
    <script src="https://app-ding.digitalhainan.com.cn:10336/js/szhn-hzt-sdk-release-1.0.0.min.js"></script>
    <script>
      var CURRENT_ENV = "<%= VUE_APP_ENV %>";
      console.log("当前环境", CURRENT_ENV);
    </script>
    <script>
      function fireSdkOnload() {
        try {
          const instance = SZHN_FIRE_BEAR.init({
            appKey: "dmi8oj7dijw4e7qdbqt6trjisxzipwqkx2gwe83r",
            salt: "YM07YJmDyPXgxWE5",
            maxBreadcrumbs: 5,
            dns:
              CURRENT_ENV === "PROD"
                ? "https://analytics-waterbear-hzt.digitalhainan.com.cn"
                : "https://analytics-waterbear-dev.digitalhainan.com.cn",
          });
        } catch (error) {
          console.error("运营平台sdk");
        }
      }
    </script>
    <script>
      function nativeSdkOnload() {
        try {
          window.SZHN_NATIVE_BEAR.init({
            appKey: "c5hwjr8dk6gk51pnz250jooozlz1pa54emp80w4m",
            appVersion: "0.1.0",
            dsn:
              CURRENT_ENV === "PROD"
                ? "https://monitor-waterbear-hzt.digitalhainan.com.cn"
                : "https://waterbear-monitor-dev.digitalhainan.com.cn",
            isH5: true, // （可选）是否是H5项目
          });
        } catch (error) {
          console.error("监控平台sdk");
        }
      }
    </script>
  </head>
  <body>
    <noscript>
      <strong
        >We're sorry but 海南国际贸易“单一窗口”（口岸监管服务信息系统）-
        通关信用管理系统 doesn't work properly without JavaScript enabled.
        Please enable it to continue.</strong
      >
    </noscript>
    <div id="app"></div>
    <script>
      // 兼容TextEncoder
      function _TextEncoder() {
        //--DO NOTHING
      }
      _TextEncoder.prototype.encode = function (s) {
        //return unescape(encodeURIComponent(s)).split('').map(function(val) {return val.charCodeAt();});
        var data = unescape(encodeURIComponent(s))
          .split("")
          .map(function (val) {
            return val.charCodeAt();
          });
        return typeof Uint8Array == "function" ? new Uint8Array(data) : data; //new TextEncoder().encode返回Uint8Array
      };
      function _TextDecoder() {
        //--DO NOTHING
      }
      _TextDecoder.prototype.decode = function (code_arr) {
        return decodeURIComponent(
          escape(String.fromCharCode.apply(null, code_arr))
        );
      };

      window.TextEncoder = window.TextEncoder || _TextEncoder;
      window.TextDecoder = window.TextDecoder || _TextDecoder;
    </script>
    <!-- built files will be auto injected -->
  </body>
</html>
