const { merge } = require('webpack-merge');
const baseConfig = require('./webpack.config.base');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

const prodConfig = {
    mode: 'production',
    devtool: false,
    module: {
        rules: [
            {
                test: /\.css|\.less$/i,
                use: [MiniCssExtractPlugin.loader, 'css-loader', 'postcss-loader', 'less-loader'],
            },
        ],
    },
    plugins:[
        new MiniCssExtractPlugin({
            filename:'static/css/main.css'
        }),
    ]
};

module.exports = (env) => merge(baseConfig(env), prodConfig);
