const webpack = require('webpack')
const HtmlWebpackPlugin = require('html-webpack-plugin')
const { VueLoaderPlugin } = require('vue-loader')
const path = require('path')
const dayjs = require('dayjs')
const AutoImport = require('unplugin-auto-import/webpack')
const Components = require('unplugin-vue-components/webpack')
const { ElementPlusResolver } = require('unplugin-vue-components/resolvers')
const ProgressBarWebpackPlugin = require('progress-bar-webpack-plugin')
const Dotenv = require('dotenv-webpack')
const Icons = require('unplugin-icons/webpack')
const IconsResolver = require('unplugin-icons/resolver')
module.exports = (env) => {
    const appVersion = `${dayjs().format('YYYYMMDD')}`
    const buildTime = `${dayjs().format('YYYY-MM-DD HH:mm:ss')}`
    function resolve(dir) {
        
        return path.join(__dirname, dir)
    }
    return {
        output: {
            publicPath: '/',
        },
        entry: path.resolve(__dirname, '../src/main.ts'),
        resolve: {
            extensions: ['.tsx', '.ts', '.js', '.jsx', '.json'],
            alias: {
                '@': path.resolve(__dirname, '../src'),
            },
        },
        module: {
            rules: [
                {
                    test: /\.vue$/,
                    loader: 'vue-loader',
                },
                {
                    test: /\.m?js|\.jsx|\.ts|\.tsx$/,
                    exclude: /(node_modules|bower_components|\.json$)/,
                    use: {
                        loader: 'babel-loader',
                    },
                },
                {
                    test: /\.svg$/,
                    use: [
                        { loader: 'svg-sprite-loader', options: { symbolId: 'icon-[name]' } },
                        {
                            loader: 'svgo-loader',
                            options: {
                                plugins: [
                                    {
                                        name: 'removeAttrs',
                                        params: { attrs: 'fill' },
                                    },
                                ],
                            },
                        },
                    ],
                },
                {
                    test: /\.(png|jpg|gif)$/,
                    type: 'asset/resource',
                },
            ],
        },
        // 优化
        optimization: {
            realContentHash: true,
        },
        // 在配置中添加插件
        plugins: [
            new ProgressBarWebpackPlugin(),
            new webpack.DefinePlugin({
                __VUE_OPTIONS_API__: true,
                __VUE_PROD_DEVTOOLS__: false,
                'process.env.BUILD_TIME': `${JSON.stringify(buildTime)}`,
                'process.env.APP_VERSION': `${JSON.stringify(appVersion)}`,
            }),
            new webpack.LoaderOptionsPlugin({
                // test: /\.xxx$/, // may apply this only for some modules
                options: {
                    chainWebpack(config) {
                        config.plugins.delete('preload') // TODO: need test
                        config.plugins.delete('prefetch') // TODO: need test

                        // set svg-sprite-loader
                        config.module.rule('svg').exclude.add(resolve('src/assets/icons')).end()
                        config.module
                            .rule('icons')
                            .test(/\.svg$/)
                            .include.add(resolve('src/assets/icons'))
                            .end()
                            .use('svg-sprite-loader')
                            .loader('svg-sprite-loader')
                            .options({
                                symbolId: 'icon-[name]',
                            })
                            .end()
                    },
                },
            }),
            new Dotenv({
                path: path.resolve(__dirname, `../.env.${env.mode}`),
            }),
            AutoImport({
                imports: [
                    'vue',
                    'vue-router',
                    '@vueuse/core',
                    'pinia',
                    // 可额外添加需要 autoImport 的组件
                    {
                        '@/hooks/web/useMessage': ['useMessage'],
                    },
                ],
                resolvers: [
                    ElementPlusResolver(),
                    // 自动导入图标组件
                    IconsResolver({
                        prefix: 'Icon',
                    }),
                ],
                dts: './auto-imports.d.ts',
                eslintrc: {
                    enabled: false,
                },
            }),
            Components({
                resolvers: [
                    // 自动注册图标组件
                    IconsResolver({
                        enabledCollections: ['ep'],
                    }),
                    ElementPlusResolver(),
                ],
            }),
            Icons({
                autoInstall: true,
            }),
            new VueLoaderPlugin(),
            new HtmlWebpackPlugin({
                template: path.resolve(__dirname, '../public/index.html'),
                meta: {
                    BUILD_INFO: `APP_ENV: ${process.env.APP_ENV};BUILD_TIME:${buildTime}`,
                },
            }),
        ],
    }
}
