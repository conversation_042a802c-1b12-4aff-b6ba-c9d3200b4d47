const { merge } = require('webpack-merge');
const baseConfig = require('./webpack.config.base');
const ESLintPlugin = require('eslint-webpack-plugin');
const proxyConfig = require('../proxyConfig');
const devConfig = {
    mode: 'development',
    devtool: 'source-map',
    devServer: {
        historyApiFallback: true,
        client: {
            overlay: false, //true 开发设置为false修改遮罩层
        },
        proxy: proxyConfig,
    },
    module: {
        rules: [
            {
                test: /\.css|\.less$/i,
                use: ['style-loader', 'css-loader', 'postcss-loader', 'less-loader'],
            },
        ],
    },

    plugins: [
        new ESLintPlugin({
            files: ['src/**/*.ts', 'src/**/*.vue', 'src/**/*.js', 'src/**/*.jsx', 'src/**/*.tsx'],
            fix: true,
        }),
    ],
};

module.exports = (env) => merge(baseConfig(env), devConfig);
