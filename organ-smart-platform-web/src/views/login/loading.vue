<template>
  <div class="loading-container">
    <img
      class="loading-img"
      src="@/assets/images/loadingBar.gif"
      alt="loading"
    />
  </div>
</template>

<script setup lang="ts">
import { baseConfig } from "@/configs/baseConfig";
import { toLogin, isLogined } from "@/util/user";

const route = useRoute();
const router = useRouter();

const userStoreHook = useUserStoreHook();

/** 登录逻辑 */
const handleLogin = async () => {
  const isHZT = await SZHN_HZT_SDK.isDingTalk();
  console.log(
    "当前是否为海政通环境:",
    isHZT,
    process.env.VUE_APP_HZT_APP_KEY,
    baseConfig.appEnv
  );
  // 海政通环境下走海政通SDK登陆
  if (isHZT) {
    console.log("当前为海政通环境，即将完成登录...");
    // 初始化sdk
    await SZHN_HZT_SDK.init({
      appKey: process.env.VUE_APP_HZT_APP_KEY,
      env: baseConfig.appEnv === "DEV" ? "dev" : "prod",
      networkEnv: "govIntranet",
    });
    console.log("当前为海政通环境，完成环境初始化");
    // 获取token
    const accessToken = await SZHN_HZT_SDK.getUaaAuthToken({
      ddRedirect: false,
    });
    console.log(accessToken, "海政通快速登录结果");
    if (typeof accessToken === "string" && accessToken.length > 0) {
      window.location.replace(
        "https://app-ding.digitalhainan.com.cn:10890" +
          `/loading?accessToken=${accessToken}`
      );
    }
  } else {
    console.log("当前为非海政通环境，即将前往页面...");
    // 非海政通走常规的登录
    toLogin();
  }
};

/** 路由参数 */
const query = {
  accessToken: String(route.query.accessToken || ""),
  ticketSNO: String(route.query.ticketSNO),
  toUrl: route.query.toUrl
    ? decodeURIComponent(String(route.query.toUrl))
    : "/",
};

/** 如果有 accessToken 则直接登录 */
if (query.accessToken) {
  userStoreHook.getUserInfo(query.accessToken).then(() => {
    router.replace(query.toUrl);
  });
} else if (isLogined()) {
  router.replace(query.toUrl || "/");
} else {
  handleLogin();
}
</script>

<style lang="scss" scoped>
.loading-container {
  height: 100vh;
  width: 100vw;
  z-index: 999;
  background-color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;

  .loading-img {
    width: 300px;
  }
}
</style>
