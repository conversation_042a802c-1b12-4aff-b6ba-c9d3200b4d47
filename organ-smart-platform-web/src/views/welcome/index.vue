<script setup lang="ts">
import { CommonTable } from "@/components/index";
</script>

<template>
  <div class="welcome">
    <CommonTable
      :table-columns="[]"
      :tableData="[]"
      :is-show-pagination="false"
      emptyText=""
    />
  </div>
</template>

<style scoped>
.welcome {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
}
.welcome-img {
  width: 586px;
}
</style>
