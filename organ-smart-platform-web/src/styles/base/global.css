html,
body,
#app {
  height: 100%;
  width: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
ul,
li {
  padding: 0;
  margin: 0;
  list-style: none;
}
.mx-10 {
  margin-left: 10px;
  margin-right: 10px;
}
.subhead {
  color: #303133;
  font-size: 16px;
  font-weight: 700;
}
.h-\[30px\] {
  height: 30px;
}
.h-\[48px\] {
  height: 48px;
}
.h-\[50px\] {
  height: 50px;
}
.h-20 {
  height: 5rem;
}
.h-3,
.h3 {
  height: 0.75rem;
}
.h-5 {
  height: 1.25rem;
}
.h-full {
  height: 100%;
}
.h1 {
  height: 0.25rem;
}
.h2 {
  height: 0.5rem;
}
.h6 {
  height: 1.5rem;
}
.w-\[120px\] {
  width: 120px;
}
.w-\[30px\] {
  width: 30px;
}
.w-\[60\%\] {
  width: 60%;
}
.w-100 {
  width: 25rem;
}
.w-20 {
  width: 5rem;
}
.w-220px {
  width: 220px;
}
.w-3 {
  width: 0.75rem;
}
.w-5 {
  width: 1.25rem;
}
.w-full,
[w-full=""] {
  width: 100%;
}
.flex {
  display: flex;
}
.justify-center {
  justify-content: center;
}
.items-center {
  align-items: center;
}
a,
a:focus,
a:hover {
  color: inherit;
  text-decoration: none;
  cursor: pointer;
}
.font-bold {
  font-weight: 700;
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.ml-3 {
  /* margin-left: 0.75rem; */
  margin: auto;
}
.mb8 {
  margin-bottom: 8px;
}
.mt-15 {
  margin-top: 15px;
}
.text-white {
  color: rgba(255, 255, 255, 1);
}
.bg-gray-800 {
  background-color: rgba(31, 41, 55, 1);
}
.app-container {
  padding: 20px;
}
.app-left {
  position: relative;
  width: 100%;
  display: block;
  margin-bottom: 20px;
}
.app-right {
  position: relative;
  width: 100%;
  display: block;
}
.text-\[10px\] {
  font-size: 10px;
}
svg {
  vertical-align: -0.15em;
}
img,
svg {
  display: inline-block;
}
.color-\[var\(--el-text-color-regular\)\] {
  color: #606266;
}
.px-\[15px\] {
  padding-left: 15px;
  padding-right: 15px;
}
.el-dialog .el-dialog__header {
  background: #409eff !important;
  margin-right: 0!important;
}
.el-dialog .el-dialog__header .el-dialog__title {
  color: #000000;
  font-size: 30px;
  /* text-align: center; */
}
.el-dialog .el-dialog__header .el-dialog__headerbtn .el-icon {
}
.segmentation-module {
  width: 100%;
  display: block;
  margin: 20px 0;
  font-size: 16px;
  padding: 0 0 10px 0px;
  border-bottom: #409eff solid 1px;
  color: #409eff;
  text-align: left;
}
.segmentation-module2 {
  margin: 0 0 20px;
}
