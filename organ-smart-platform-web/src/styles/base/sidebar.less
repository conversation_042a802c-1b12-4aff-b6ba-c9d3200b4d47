.app {
  .main-container {
    position: relative;
    min-height: 100%;
    margin-left: var(--sideBarWidth);
    transition: margin-left 0.28s;
  }

  .sidebar-container {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    width: 210px !important;
    height: 100%;
    overflow: hidden;
    background-color: var(--menuBg);
    transition: width 0.28s;

    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out,
        0s padding-right ease-in-out;
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0;
    }

    .el-scrollbar {
      height: 100%;
    }

    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 50px);
      }
    }

    .is-horizontal {
      display: none;
    }

    .svg-icon {
      margin-right: 12px;
    }

    .sub-el-icon {
      margin-right: 12px;
      margin-left: -2px;
    }

    .el-menu {
      width: 100% !important;
      height: 100%;
      border: none;
      .el-menu-item{
        &.is-active {
          background-color: #0093ff !important;
        }
      }
    }

    .submenu-title-noDropdown,
    .el-sub-menu__title {
      &:hover {
        background-color: var(--menuHover) !important;
      }
    }
    :deep(.el-menu-item .is-active){
      background-color: var(--el-menu-active-color) !important;
    }
    .is-active>.el-sub-menu__title {
      color: var(--subMenuActiveText) !important;
    }

    & .nest-menu .el-sub-menu>.el-sub-menu__title,
    & .el-sub-menu .el-menu-item {
      min-width: var(--sideBarWidth) !important;
      background-color: var(--subMenuBg) !important;

      &:hover {
        background-color: var(--subMenuHover) !important;
      }
    }
  }

  .hideSidebar {
    .left-wrap {
      width: 54px;
    }

    .sidebar-container {
      width: 54px !important;

      .header {
        .logo-wrap {
          width: 54px !important;
          transition: transform 0.28s;
        }
      }
    }

    .main-container {
      margin-left: 54px;
    }

    .submenu-title-noDropdown {
      position: relative;
      padding: 0 !important;
      .el-tooltip {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }

        .sub-el-icon {
          margin-left: 19px;
        }
      }

      .div-svg {

        &>.svg-icon {
          margin-left: 20px;
        }

        &>span {
          display: inline-block;
          width: 0;
          height: 0;
          overflow: hidden;
          visibility: hidden;
        }
      }
    }

    .el-sub-menu {
      overflow: hidden;

      &>.el-sub-menu__title {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }

        .sub-el-icon {
          margin-left: 19px;
        }

        .el-sub-menu__icon-arrow {
          display: none;
        }
      }
    }

    .el-menu--collapse {
      .el-sub-menu {
        &>.el-sub-menu__title {
          &>span {
            display: inline-block;
            width: 0;
            height: 0;
            overflow: hidden;
            visibility: hidden;
          }
        }
      }
    }
  }

  .el-menu--collapse .el-menu .el-sub-menu {
    min-width: var(--sideBarWidth) !important;
  }

  .mobile {
    .main-container {
      margin-left: 0;
    }

    .sidebar-container {
      width: var(--sideBarWidth) !important;
      transition: transform 0.28s;
    }

    &.hideSidebar:not(.isMix, .isTop) {
      .sidebar-container {
        pointer-events: none;
        transition-duration: 0.3s;
        transform: translate3d(-var(--sideBarWidth), 0, 0);
      }
    }
  }

  .withoutAnimation {

    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

.el-menu--vertical {
  &>.el-menu {
    .svg-icon {
      margin-right: 12px;
    }

    .sub-el-icon {
      margin-right: 12px;
      margin-left: -2px;
    }
  }

  .nest-menu .el-sub-menu>.el-sub-menu__title,
  .el-menu-item {
    &:hover {
      background-color: var(--menuHover) !important;
    }
  }

  >.el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;

    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}

.app .sidebar-container .el-submenu .el-menu-item.is-active {
  background-color: #0093ff !important;
  color: #fff !important;
}
.app .sidebar-container .submenu-title-noDropdown .el-menu-item.is-active {
  background-color: #0093ff !important;
  color: #fff !important;
}