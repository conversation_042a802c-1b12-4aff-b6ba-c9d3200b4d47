.app .main-container {
  position: relative;
  min-height: 100%;
  margin-left: var(--sideBarWidth);
  transition: margin-left 0.28s;
}
.app .sidebar-container {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 1001;
  width: 210px !important;
  height: 100%;
  overflow: hidden;
  background-color: var(--menuBg);
  transition: width 0.28s;
}
.app .sidebar-container .horizontal-collapse-transition {
  transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
}
.app .sidebar-container .scrollbar-wrapper {
  overflow-x: hidden !important;
}
.app .sidebar-container .el-scrollbar__bar.is-vertical {
  right: 0;
}
.app .sidebar-container .el-scrollbar {
  height: 100%;
}
.app .sidebar-container.has-logo .el-scrollbar {
  height: calc(100% - 50px);
}
.app .sidebar-container .is-horizontal {
  display: none;
}
.app .sidebar-container .svg-icon {
  margin-right: 12px;
}
.app .sidebar-container .sub-el-icon {
  margin-right: 12px;
  margin-left: -2px;
}
.app .sidebar-container .el-menu {
  width: 100% !important;
  height: 100%;
  border: none;
}
.app .sidebar-container .submenu-title-noDropdown:hover,
.app .sidebar-container .el-sub-menu__title:hover {
  background-color: var(--menuHover) !important;
}
.app .sidebar-container :deep(.el-menu-item.is-active) {
  color: #fff !important;
  background-color: var(--el-menu-active-color) !important;
}
.app .sidebar-container .is-active > .el-sub-menu__title {
  color: var(--subMenuActiveText) !important;
}
.app .sidebar-container .nest-menu .el-sub-menu > .el-sub-menu__title,
.app .sidebar-container .el-sub-menu .el-menu-item {
  min-width: var(--sideBarWidth) !important;
  background-color: var(--subMenuBg) !important;
}
.app .sidebar-container .nest-menu .el-sub-menu > .el-sub-menu__title:hover,
.app .sidebar-container .el-sub-menu .el-menu-item:hover {
  background-color: var(--subMenuHover) !important;
}
.app .hideSidebar .left-wrap {
  width: 54px;
}
.app .hideSidebar .sidebar-container {
  width: 54px !important;
}
.app .hideSidebar .sidebar-container .header .logo-wrap {
  width: 54px !important;
  transition: transform 0.28s;
}
.app .hideSidebar .main-container {
  margin-left: 54px;
}
.app .hideSidebar .submenu-title-noDropdown {
  position: relative;
  padding: 0 !important;
}
.app .hideSidebar .submenu-title-noDropdown .el-menu-item.is-active {
  background-color: #0093ff !important;
  color: #fff !important;
}
.app .hideSidebar .submenu-title-noDropdown .el-tooltip {
  padding: 0 !important;
}
.app .hideSidebar .submenu-title-noDropdown .el-tooltip .svg-icon {
  margin-left: 20px;
}
.app .hideSidebar .submenu-title-noDropdown .el-tooltip .sub-el-icon {
  margin-left: 19px;
}
.app .hideSidebar .submenu-title-noDropdown .div-svg > .svg-icon {
  margin-left: 20px;
}
.app .hideSidebar .submenu-title-noDropdown .div-svg > span {
  display: inline-block;
  width: 0;
  height: 0;
  overflow: hidden;
  visibility: hidden;
}
.app .hideSidebar .el-sub-menu {
  overflow: hidden;
}
.app .hideSidebar .el-sub-menu > .el-sub-menu__title {
  padding: 0 !important;
}
.app .hideSidebar .el-sub-menu > .el-sub-menu__title .svg-icon {
  margin-left: 20px;
}
.app .hideSidebar .el-sub-menu > .el-sub-menu__title .sub-el-icon {
  margin-left: 19px;
}
.app .hideSidebar .el-sub-menu > .el-sub-menu__title .el-sub-menu__icon-arrow {
  display: none;
}
.app .hideSidebar .el-menu--collapse .el-sub-menu > .el-sub-menu__title > span {
  display: inline-block;
  width: 0;
  height: 0;
  overflow: hidden;
  visibility: hidden;
}
.app .el-menu--collapse .el-menu .el-sub-menu {
  min-width: var(--sideBarWidth) !important;
}
.app .mobile .main-container {
  margin-left: 0;
}
.app .mobile .sidebar-container {
  width: var(--sideBarWidth) !important;
  transition: transform 0.28s;
}
.app .mobile.hideSidebar:not(.isMix, .isTop) .sidebar-container {
  pointer-events: none;
  transition-duration: 0.3s;
  transform: translate3d(-var(--sideBarWidth), 0, 0);
}
.app .withoutAnimation .main-container,
.app .withoutAnimation .sidebar-container {
  transition: none;
}
.el-menu--vertical > .el-menu .svg-icon {
  margin-right: 12px;
}
.el-menu--vertical > .el-menu .sub-el-icon {
  margin-right: 12px;
  margin-left: -2px;
}
.el-menu--vertical .nest-menu .el-sub-menu > .el-sub-menu__title:hover,
.el-menu--vertical .el-menu-item:hover {
  background-color: var(--menuHover) !important;
}
.el-menu--vertical > .el-menu--popup {
  max-height: 100vh;
  overflow-y: auto;
}
.el-menu--vertical > .el-menu--popup::-webkit-scrollbar-track-piece {
  background: #d3dce6;
}
.el-menu--vertical > .el-menu--popup::-webkit-scrollbar {
  width: 6px;
}
.el-menu--vertical > .el-menu--popup::-webkit-scrollbar-thumb {
  background: #99a9bf;
  border-radius: 20px;
}
.app .sidebar-container .el-submenu .el-menu-item.is-active {
  background-color: #0093ff !important;
  color: #fff !important;
}
.app .sidebar-container .submenu-title-noDropdown .el-menu-item.is-active {
  background-color: #0093ff !important;
  color: #fff !important;
}
