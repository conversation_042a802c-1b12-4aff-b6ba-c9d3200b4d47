import { useIntersectionObserver } from "@vueuse/core";
import Vue from "vue";
Vue.directive("getData", {
  // 当绑定元素插入到DOM中
  inserted(el: HTMLElement, binding: any) {
    const { stop } = useIntersectionObserver(
      el,
      ([{ isIntersecting }]: any) => {
        if (isIntersecting) {
          // 进入视口区域
          if (binding.value.getData) {
            binding.value.getData();
          }
          stop();
        }
      }
    );
  },
});
