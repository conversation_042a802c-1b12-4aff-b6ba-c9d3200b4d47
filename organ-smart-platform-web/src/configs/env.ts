console.log(`当前的webpack环境变量为: ${process.env.NODE_ENV}`);
console.log(`当前的webpack环境变量为: ${process.env.VUE_APP_ENV}`);

export const envConfig: Record<
  string,
  {
    /** 登录地址 */
    externalLogin: string;
    /** 退出登录地址 */
    externalLogout: string;
  }
> = {
  /** 开发环境 */
  DEV: {
    externalLogin: "https://ucs-sso-dev.digitalhainan.com.cn/login",
    externalLogout: "https://ucs-sso-dev.digitalhainan.com.cn/logout",
  },
  /** 测试环境 */
  SIT: {
    externalLogin: "https://ucs-sso-dev.digitalhainan.com.cn/login",
    externalLogout: "https://ucs-sso-dev.digitalhainan.com.cn/logout",
  },
  /** 生产环境 */
  PROD: {
    // TODO 先使用测试环境的地址，后续调整
    externalLogin: "https://ucs-sso-dev.digitalhainan.com.cn/login",
    externalLogout: "https://ucs-sso-dev.digitalhainan.com.cn/logout",
    // externalLogin: 'https://ucs-sso.digitalhainan.com.cn/login',
    // externalLogout: 'https://ucs-sso.digitalhainan.com.cn/logout',
  },
};

/** 当前的环境配置 */
export const env = envConfig[process.env.VUE_APP_ENV];
