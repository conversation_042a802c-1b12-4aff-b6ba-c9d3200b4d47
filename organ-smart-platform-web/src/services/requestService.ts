import axios from "axios";
import type { AxiosRequestConfig } from "axios";
import { Message, MessageBox } from "element-ui";
import qs from "qs";
import { isEmpty } from "lodash-es";
import { TOKEN } from "@/constant/storage";

export const requestBaseURL = `${process.env.VUE_APP_BASE_API}`;
const instance = axios.create({
  withCredentials: false,
  baseURL: `${requestBaseURL}/api`,
  timeout: 60 * 1000,
  paramsSerializer: {
    serialize: (params) =>
      qs.stringify(params, {
        arrayFormat: "brackets",
        encode: true,
      }),
  },
});

let loadingInstance: undefined | { close: () => void };

instance.interceptors.request.use(
  (config) => {
    const isToken = (config.headers || {}).isToken === false;
    let token = sessionStorage.getItem(TOKEN);
    if (!isEmpty(token) && !isToken) {
      config.headers["Authorization"] = "Bearer " + token;
    }
    console.log("headers", config.headers);
    console.info("开始请求", config.url, config.data || config.params);
    return config;
  },
  (error) => {
    console.log("request error==========", error);
    loadingInstance && loadingInstance.close();
    console.log("request", error);
    return Promise.reject(error);
  }
);

/** 登录失效 code 集合 401 未登录，5001001 登录失效， 402 -> 登录授权类失败 */
const loginInvalidCode = [401, 402, 5001001];
instance.interceptors.response.use(
  (response) => {
    let res = response.data;
    if (!(res instanceof Object)) {
      res = {
        code: 0,
        data: res,
        msg: undefined,
      };
    }
    // 网络请求响应异常
    if (response.status !== 200) {
      Message.error(res.msg || "当前系统繁忙，请稍后再试");
      return Promise.reject(res);
    }
    // 接口返回异常
    if (Number(res.code) !== 0) {
      if (loginInvalidCode.includes(res.code)) {
        if (localStorage.getItem("authenticationCode")) {
          router.push("/403");
        } else {
          useUserStoreHook().clearUserInfo();
        }
      }
      Message.error(res.msg);
      return Promise.reject(res);
    } else {
      return Promise.resolve(res.data);
    }
  },
  (error) => {
    loadingInstance && loadingInstance.close();
    console.log("err" + error);
    if (JSON.stringify(error).indexOf("timeout")) {
      Message.error("请求超时，请重试");
      throw new Error(error);
    }
    Message.error("当前系统繁忙，请稍后再试");
    throw new Error(error);
  }
);

function requestService<P = AxiosRequestConfig["data"], R = any>(
  path: string,
  params?: P,
  options: AxiosRequestConfig = {}
) {
  const realOptions = {
    url: path,
    method: "POST",
    ...options,
  };
  if (realOptions.method === "GET") {
    realOptions.params = params;
  } else {
    realOptions.data = params;
  }

  return instance(realOptions) as Promise<R>;
}
export default requestService;
