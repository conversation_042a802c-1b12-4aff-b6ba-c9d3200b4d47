import axios, { AxiosRequestConfig } from 'axios';

const instance = axios.create({
    withCredentials: false,
    baseURL: `${process.env.VUE_APP_BASE_API}/api`,
    timeout: 60 * 1000,
    responseType: 'blob',
});

let loadingInstance: undefined | { close: () => void };

instance.interceptors.request.use(
    (config) => {
        const isToken = (config.headers || {}).isToken === false;
        return config;
    },
    (error) => {
        console.log('request error==========', error);
        loadingInstance && loadingInstance.close();
        console.log('request', error);
        return Promise.reject(error);
    },
);

function requestFileService<P = AxiosRequestConfig['data'], R = any>(
    path: string,
    params?: P,
    options: AxiosRequestConfig = {},
) {
    const realOptions = {
        url: path,
        method: 'POST',
        ...options,
    };
    if (realOptions.method === 'GET') {
        realOptions.params = params;
    } else {
        realOptions.data = params;
    }

    return instance(realOptions) as Promise<R>;
}
export default requestFileService;
