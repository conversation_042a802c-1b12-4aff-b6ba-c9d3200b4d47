<script lang="ts" setup>
import { debounce } from "lodash";
import { onMounted, ref } from "vue";

defineProps({
  packUpStyle: {
    type: [String, Object],
    default: "",
  },
  showCmission: {
    type: Boolean,
    default: false,
  },
});

const isHideText = ref(false);
const lineClamp = ref("1");
const observer = ref<ResizeObserver>();

onMounted(() => {
  init();
});

const ChildrenContainerRef = ref<HTMLDivElement>();
const TextCmissionRef = ref<HTMLDivElement>();

const handleResize = debounce(function () {
  const viewHeight = TextCmissionRef.value!.offsetHeight;
  const containerHeight = ChildrenContainerRef.value!.offsetHeight;
  isHideText.value = containerHeight > viewHeight;
}, 200);

function init() {
  observer.value = new ResizeObserver(handleResize);
  observer.value.observe(ChildrenContainerRef.value!);
}
</script>

<template>
  <div class="text-cmission" ref="TextCmissionRef">
    <div
      class="children"
      :style="{
        'padding-right': isHideText && lineClamp === '1' ? '60px' : '0px',
        '-webkit-line-clamp': lineClamp,
      }"
    >
      <slot></slot>
      <el-link
        class="down"
        type="primary"
        :underline="false"
        @click="lineClamp = 'initial'"
        v-if="showCmission && lineClamp === '1'"
        >展开<i class="el-icon-arrow-down"></i>
      </el-link>
      <el-link
        class="pack-up"
        :style="packUpStyle"
        type="primary"
        :underline="false"
        @click="lineClamp = '1'"
        v-if="showCmission && lineClamp === 'initial'"
        >收起<i class="el-icon-arrow-up"></i>
      </el-link>
    </div>
    <!-- 用于计算 文本 高度 -->
    <div class="children-container" ref="ChildrenContainerRef">
      <slot></slot>
    </div>
  </div>
</template>

<style scoped lang="scss">
.text-cmission {
  position: relative;
  .children {
    position: relative;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    box-sizing: border-box;
    .down {
      position: absolute;
      top: 0;
      right: 0;
      padding: 0 6px;
      // background-color: #edf2ff;
    }

    .pack-up {
      position: relative;
      left: 10px;
      top: 0px;
    }
  }
  .children-container {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    pointer-events: none;
  }
}
</style>
