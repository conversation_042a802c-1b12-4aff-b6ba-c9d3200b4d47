<script setup lang="ts">
import { cloneDeep } from "lodash-es";

interface Props {
  data: Record<string, any>;
  /** 控制整个组件的Alone */
  isAlone?: boolean;
  columns: {
    label: string;
    prop: string;
    formatter?: (value: Record<string, any>) => any;
    isAlone?: boolean;
  }[];
}

const props = defineProps<Props>();

function formatterData(arr: Props["columns"]) {
  return cloneDeep(arr || [])
    .reduce((accumulator, item) => {
      const last = accumulator.at(-1);
      if (Array.isArray(last) && last.length !== 2 && !item.isAlone) {
        last.push(item);
      } else {
        accumulator.push(props.isAlone || item.isAlone ? item : [item]);
      }
      return accumulator;
    }, [] as Array<Props["columns"] | Props["columns"][number]>)
    .map((item) => (Array.isArray(item) ? item : [item]));
}
</script>

<template>
  <div class="common-descriptions">
    <div
      class="common-descriptions-row"
      v-for="(row, rowIndex) in formatterData(props.columns)"
      :key="rowIndex"
    >
      <div
        class="common-descriptions-row-item"
        v-for="item in row"
        :key="item.prop"
      >
        <div class="item-text item-label">{{ item.label }}</div>
        <div class="item-text item-value" v-if="item.formatter">
          {{ item.formatter(data) }}
        </div>
        <div class="item-text item-value" v-else>
          {{ data[item.prop] || "-" }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.common-descriptions {
  border-right: 1px solid #cccccc;
  border-bottom: 1px solid #cccccc;
  .common-descriptions-row {
    display: flex;
    .common-descriptions-row-item {
      flex: 1;
      display: flex;
      border-top: 1px solid #cccccc;
      border-left: 1px solid #cccccc;
      .item-text {
        box-sizing: border-box;
        padding: 10px 20px;
        color: #666666;
        display: flex;
        align-items: center;
        line-height: 1.5;
      }
      .item-label {
        width: 190px;
        background-color: #edf2ff;
        border-right: 1px solid #cccccc;
      }
      .item-value {
        flex: 1;
      }
    }
  }
}
</style>
