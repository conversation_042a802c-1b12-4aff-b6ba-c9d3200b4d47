<script setup lang="ts">
const props = defineProps<{
    level?: string;
    levels: string[];
}>();
</script>

<template>
    <div class="level-display">
        <div class="level-progress">
            <div
                class="level-progress-item"
                :class="{ 'level-progress-item-active': item === level }"
                v-for="item in levels"
                :key="item"
            >
                <div class="level-progress-item-rank-container">
                    <div class="level-progress-item-rank"></div>
                </div>
            </div>
        </div>
        <div class="level-text">
            <div
                class="level-text-item"
                :class="{ 'level-text-item-active': item === level }"
                v-for="item in levels"
                :key="item"
            >
                {{ item }}
            </div>
        </div>
        <!-- <div class="level-display-item" v-for="item in levels" :key="item">
            <div class="level-display-item-level">{{ item }}</div>
        </div> -->
    </div>
</template>

<style scoped lang="less">
.level-display {
    width: 100%;
    height: fit-content;
    --div-spacing: 6px;
    --progress-size: 23px;
    --progress-item-height: 18px;
    .level-progress {
        width: 100%;
        height: var(--progress-size);
        display: flex;
        align-items: center;
        .level-progress-item {
            flex: 1;
            height: var(--progress-item-height);
            background-color: #d2ddff;
            display: flex;
            align-items: center;
            justify-content: center;
            .level-progress-item-rank-container {
                display: none;
                width: var(--progress-size);
                height: var(--progress-size);
                background-color: #5d83f9;
                border-radius: 50%;
                align-items: center;
                justify-content: center;
                .level-progress-item-rank {
                    width: calc(var(--progress-size) / 2);
                    height: calc(var(--progress-size) / 2);
                    border-radius: 50%;
                    border: 3px solid #fff;
                    background-color: #5d83f9;
                }
            }
            &:not(:first-child) {
                margin-left: var(--div-spacing);
            }
            &:first-child {
                border-top-left-radius: calc(var(--progress-item-height) / 2);
                border-bottom-left-radius: calc(var(--progress-item-height) / 2);
            }
            &:last-child {
                border-top-right-radius: calc(var(--progress-item-height) / 2);
                border-bottom-right-radius: calc(var(--progress-item-height) / 2);
            }
        }
        .level-progress-item-active {
            background-color: #5d83f9;
            .level-progress-item-rank-container {
                display: flex;
            }
        }
    }
    .level-text {
        display: flex;
        margin-top: 2px;
        .level-text-item {
            flex: 1;
            text-align: center;
            color: #cccccc;
            &:not(:first-child) {
                margin-left: var(--div-spacing);
            }
        }
        .level-text-item-active {
            color: #333333;
        }
    }
}
</style>
