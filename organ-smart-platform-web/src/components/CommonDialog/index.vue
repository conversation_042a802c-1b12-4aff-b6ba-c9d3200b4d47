<template>
  <el-dialog
    class="my-dialog"
    :title="title"
    :visible.sync="visible"
    :width="width"
    center
    :before-close="handleClose"
    v-bind="$attrs"
  >
    <template #title v-if="!title">
      <slot name="title">222</slot>
    </template>
    <slot name="body"></slot>
    <span slot="footer" class="dialog-footer">
      <slot name="footer"></slot>
    </span>
    <el-button v-show="showCloseBtn" slot="footer" @click="handleClose">{{
      closeBtnText
    }}</el-button>
  </el-dialog>
</template>

<script>
export default {
  name: "MyDialog",
  inheritAttrs: false,
  props: {
    title: String,
    width: {
      type: String,
      default: "30%",
    },
    visible: {
      type: Boolean,
      required: true,
    },
    showCloseBtn: {
      type: Boolean,
      default: true,
    },
    closeBtnText: {
      type: String,
      default: "关闭",
    },
  },
  methods: {
    handleClose() {
      this.$emit("close", false);
      this.$emit("update:visible", false);
    },
    handleConfirm() {
      this.handleClose();
    },
  },
  mounted() {
    setTimeout(() => {
      const headers = document.getElementsByClassName("el-dialog__header");
      for (let index = 0; index < headers.length; index++) {
        const element = headers[index];
        element.style.display = "flex";
      }
    });
  },
};
</script>

<style lang="less" scoped>
.my-dialog {
  /deep/ .dialog__wrapper .el-dialog .el-dialog__header {
    display: flex !important;
    text-align: left !important;
  }
  /deep/ .dialog__wrapper .el-dialog .el-dialog__header::before {
    display: none !important;
  }
  :deep(.el-dialog__header) {
    display: flex !important;
    border-bottom: 1px solid #f2f3f5;
    text-align: left !important;
  }
  :deep(.el-dialog__header::before) {
    display: none !important;
  }
  :deep(.el-dialog__headerbtn) {
    top: 10px !important;
  }
}
</style>
