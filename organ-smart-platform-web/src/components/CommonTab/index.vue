<script setup lang="ts">
import { ref, useAttrs } from "vue";

interface TabItem {
  label: string;
  value: string;
}
const props = withDefaults(
  defineProps<{
    /**
     * 标签页数据
     * @type {Array}
     * @default []
     */
    tabs: TabItem[];
    /**
     * 对齐方式
     * left | center | right
     */
    align?: string;
    /**
     * tab 元素是否占满赋父元素
     */
    full?: boolean;
    /** tab是否需要缓存， 默认不需要 */
    tabCache?: boolean;
  }>(),
  {
    tabs: () => [],
    align: "left",
    full: false,
    tabCache: false,
  }
);

const emit = defineEmits(["tab-click"]);
const attrs = useAttrs();
const activeName = ref(attrs.value);

const handleClick = (tab: any, event: Event) => {
  const current = props.tabs[tab.index];
  activeName.value = current.value;
  emit("tab-click", current);
};
</script>

<template>
  <el-tabs
    class="common-tab"
    :class="[
      `common-tab-align-${align}`,
      {
        'common-tab-full': full,
      },
    ]"
    v-bind="$attrs"
    @tab-click="handleClick"
  >
    <el-tab-pane
      :label="item.label"
      :name="item.value"
      v-for="item in tabs"
      :key="item.value"
    >
      <template #label>
        <slot :name="`label-${item.value}`"></slot>
      </template>
      <template v-if="tabCache">
        <slot :name="item.value" v-if="activeName === item.value"></slot>
      </template>
      <slot :name="item.value" v-else></slot>
    </el-tab-pane>
  </el-tabs>
</template>

<style scoped lang="less">
.common-tab {
  width: 100%;
}
.common-tab-full {
  height: 100%;
  display: flex;
  flex-direction: column;
  ::v-deep(.el-tabs__content) {
    flex: 1;
    width: 100%;
    .el-tab-pane {
      width: 100%;
      height: 100%;
    }
  }
}
.common-tab-align-center {
  ::v-deep(.el-tabs__nav-scroll) {
    display: flex;
    justify-content: center;
  }
}
.common-tab-align-left {
  ::v-deep(.el-tabs__nav-scroll) {
    display: flex;
    justify-content: flex-start;
  }
}
.common-tab-align-right {
  ::v-deep(.el-tabs__nav-scroll) {
    display: flex;
    justify-content: flex-end;
  }
}
</style>
