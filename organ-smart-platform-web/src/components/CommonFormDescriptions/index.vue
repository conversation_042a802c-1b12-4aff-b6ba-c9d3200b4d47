<script setup lang="ts">
interface Props {
  data: Record<string, any>;
  /** 控制整个组件的Alone */
  span?: string | number;
  columns: {
    label: string;
    prop: string;
    span?: string | number;
    formatter?: (value: any) => string;
  }[];
}

const props = defineProps<Props>();
</script>

<template>
  <el-form class="common-form-descriptions" inline label-width="160px">
    <el-row>
      <el-col :span="item.span" v-for="item in columns" :key="item.prop">
        <el-form-item :label="`${item.label}：`" :prop="item.prop">
          <slot :name="item.prop" v-bind="item">
            <template v-if="item.formatter">
              {{ item.formatter(data) }}
            </template>
            <template v-else>
              {{ data[item.prop] || "-" }}
            </template>
          </slot>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<style scoped lang="less">
.common-form-descriptions {
  overflow: hidden;
  .el-form-item {
    display: flex;
    :deep(.el-form-item__content) {
      width: calc(100% - 160px);
      box-sizing: border-box;
      padding: 0 20px;
      color: #383838;
      background-color: #f2f3f5;
      word-wrap: break-word;
      white-space: pre-line;
    }
  }
}
</style>
