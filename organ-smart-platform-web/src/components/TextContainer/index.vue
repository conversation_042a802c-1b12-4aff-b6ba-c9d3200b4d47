<script setup lang="ts">
import { ref, onMounted } from 'vue';

const props = defineProps({
    // 文本内容
    text: {
        type: String,
        required: true,
    },
    // 默认最多显示 3 行
    maxLines: {
        type: Number,
        default: 3,
    },
});

const textContent = ref<HTMLDivElement | null>(null);
const isCollapsed = ref(true);

const toggleCollapse = () => {
    isCollapsed.value = !isCollapsed.value;
};

const croppedText = ref('');
const showMoreButton = computed(() => {
    return croppedText.value.length < props.text.length;
});

watch(
    () => props.text,
    () => {
        getCroppedText();
    },
);

/** 获取裁剪后的文本 人努力一下自己都不知道自己能写出来 */
function getCroppedText() {
    const el = textContent.value!;
    const style = getComputedStyle(el, null);
    const width = el.offsetWidth;
    const fontSize = parseInt(style.fontSize);
    // 创建 Canvas 用于测量字符宽度
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d')!;
    ctx.font = style.font;
    let initWidth = 0;
    let textRowIndexs = [];
    let loopCount = 0;
    // 占位符的宽度
    const placeholderWidth = Math.max(ctx.measureText('...').width, fontSize) + fontSize * 2;
    for (let i = 0; i < props.text.length; i++) {
        const textWidth = ctx.measureText(props.text[i]).width;
        if (initWidth + textWidth > width) {
            textRowIndexs.push(i);
            loopCount += 1;
            initWidth = 0;
            if (loopCount >= props.maxLines) {
                break;
            }
        }
        initWidth += textWidth;
        if (i === props.text.length - 1) {
            textRowIndexs.push(i + 1);
        }
    }
    // 计算出每行需要展示的文本
    let tempText = '';
    textRowIndexs.reduce((acc, curr) => {
        tempText += props.text.slice(acc, curr);
        return curr;
    }, 0);

    if (tempText.length >= props.text.length) {
        console.log('不用展开');
        croppedText.value = props.text;
    }
    // 计算最后一行减去占位符的文本长度
    else {
        let tempTextWidth = 0;
        for (let i = tempText.length; i >= 0; i--) {
            const textWidth = ctx.measureText(props.text[i]).width;
            tempTextWidth += textWidth;
            if (tempTextWidth >= placeholderWidth) {
                croppedText.value = props.text.slice(0, i);
                break;
            }
        }
    }
    canvas.remove();
}
const observer = new ResizeObserver(getCroppedText);
onMounted(() => {
    getCroppedText();
    window.addEventListener('resize', getCroppedText);
});
onUnmounted(() => {
    window.removeEventListener('resize', getCroppedText);
    observer.disconnect();
});
</script>

<template>
    <div class="text-container">
        <div
            ref="textContent"
            class="text-content"
            :class="{ 'text-collapsed': isCollapsed }"
            :style="{ '-webkit-line-clamp': isCollapsed ? maxLines : 'none' }"
        >
            {{ isCollapsed ? croppedText : text }}
            <span v-if="showMoreButton && isCollapsed" @click="toggleCollapse"
                >...<i class="el-icon-arrow-down"></i
            ></span>
            <span class="retract" v-if="showMoreButton && !isCollapsed" @click="toggleCollapse"
                ><i class="el-icon-arrow-up"></i
            ></span>
        </div>
    </div>
</template>

<style scoped lang="scss">
.text-container {
    position: relative;
}
.text-content {
    line-height: 1.5;
}

.more-button:hover {
    text-decoration: underline;
}
i[class^='el-icon-'] {
    font-size: 1em;
}
</style>
