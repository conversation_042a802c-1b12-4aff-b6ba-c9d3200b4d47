<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    tags: string[];
    active: string;
    status?: "high" | "low";
  }>(),
  {
    status: "high",
  }
);
</script>

<template>
  <div class="common-active-tags">
    <div
      class="common-active-tags-item"
      :class="{
        'common-active-tags-item-active': tag === active,
        [`common-active-tags-item-active-${status}`]: tag === active,
      }"
      v-for="tag in tags"
      :key="tag"
    >
      {{ tag }}
    </div>
  </div>
</template>

<style scoped>
.common-active-tags {
  display: flex;
  height: 40px;
  background-color: #fff;
}
.common-active-tags-item {
  height: 100%;
  color: #808080;
  background-color: #f1f6ff;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0px 24px;
  line-height: 1;
}

.common-active-tags-item:first-child {
  border-top-left-radius: 50px 50px;
  border-bottom-left-radius: 50px 50px;
}
.common-active-tags-item:last-child {
  border-top-right-radius: 50px 50px;
  border-bottom-right-radius: 50px 50px;
}

.common-active-tags-item + .common-active-tags-item {
  margin-left: 4px;
}
.common-active-tags-item-active {
  color: #ffffff;
  font-weight: 500;
  border-top-left-radius: 50px 50px;
  border-bottom-left-radius: 50px 50px;
  border-top-right-radius: 50px 50px;
  border-bottom-right-radius: 50px 50px;
  background: linear-gradient(117.12deg, #abc6fa 0%, #335df5 100%);
}
.common-active-tags-item-active-high {
  background: linear-gradient(117.12deg, #abc6fa 0%, #335df5 100%);
}
.common-active-tags-item-active-low {
  background: linear-gradient(117.12deg, #cad5f5 0%, #99a6cd 100%);
}
</style>
