<script setup lang="ts">
import { computed, ref, watch, nextTick } from "vue";

const props = defineProps<{
  text?: string;
  height: number;
}>();

const emit = defineEmits<{
  (e: "collapse", height: number): void;
}>();

/** 是否超出 */
const isExceed = ref(false);
/** 是否展开 */
const isExpand = ref(false);
const height = computed(() => {
  if (isExceed.value && !isExpand.value) {
    return `${props.height}px`;
  }
  return "fit-content";
});
const textRef = ref<HTMLSpanElement>();
watch(
  () => props.text,
  () => {
    nextTick(() => {
      isExceed.value = (textRef.value?.offsetHeight || 0) > props.height;
      // 如果文本没有超出，则不展开
      if (!isExceed.value) {
        isExpand.value = false;
      }
    });
  },
  {
    immediate: true,
  }
);

function handleCollapse() {
  const textHeight = textRef.value?.offsetHeight;
  isExpand.value = false;
  if (textHeight) {
    emit("collapse", textHeight - props.height - props.height);
  }
}
</script>

<template>
  <div class="multi-text-cmission" :style="{ height: height }">
    <span ref="textRef">
      {{ text }}
      <span
        class="expand-btn"
        v-if="isExceed && isExpand"
        @click="handleCollapse"
      >
        收起
        <i class="el-icon-arrow-up"></i>
      </span>
    </span>
    <div
      class="expand-btn"
      v-if="isExceed && !isExpand"
      @click="isExpand = true"
    >
      展开
      <i class="el-icon-arrow-down"></i>
    </div>
  </div>
</template>

<style scoped lang="scss">
.multi-text-cmission {
  position: relative;
  overflow: hidden;
  white-space: pre-wrap;
  .expand-btn {
    position: absolute;
    width: 100px;
    bottom: 0;
    right: 0;
    padding: 0 6px;
    text-align: right;
    color: #409eff;
    background: linear-gradient(to right, transparent, #ffffff 50%);
  }
  .expand-btn {
    color: #409eff;
  }
}
</style>
