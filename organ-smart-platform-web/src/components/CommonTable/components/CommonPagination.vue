<template>
  <div :class="{ hidden: hidden }" class="pagination-container">
    <el-pagination
      :background="background"
      :current-page.sync="currentPage"
      :page-size.sync="limit"
      :layout="layout"
      :page-sizes="pageSizes"
      :total="total"
      v-bind="$attrs"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script>
export default {
  name: "CommonPagination",
  props: {
    total: {
      required: true,
      type: Number,
    },
    pageNo: {
      type: Number,
      default: 1,
    },
    pageSize: {
      type: Number,
      default: 20,
    },
    pageSizes: {
      type: Array,
      default() {
        return [10, 20, 30, 50];
      },
    },
    layout: {
      type: String,
      default: "total, sizes, prev, pager, next, jumper",
    },
    background: {
      type: Boolean,
      default: true,
    },
    hidden: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    currentPage: {
      get() {
        return this.pageNo;
      },
      set(val) {
        this.$emit("update:pageNum", val);
      },
    },
    limit: {
      get() {
        return this.pageSize;
      },
      set(val) {
        this.$emit("update:pageSize", val);
      },
    },
  },
  methods: {
    handleSizeChange(val) {
      this.currentPage = 1;
      this.$emit("pagination", {
        pageNum: 1,
        pageNo: 1,
        pageSize: val,
      });
    },
    handleCurrentChange(val) {
      this.$emit("pagination", {
        pageNum: val,
        pageNo: val,
        pageSize: this.pageSize,
      });
    },
  },
};
</script>

<style lang="less" scoped>
.pagination-container {
  background: #fff;
  padding: 15px 16px;
}
.pagination-container.hidden {
  display: none !important;
}
.el-pagination {
  :deep(.btn-prev),
  :deep(.btn-next),
  :deep(.el-pager li) {
    border: 1px solid rgba(212, 214, 217, 1);
    background-color: white;
    font-size: 14px;
    font-weight: 400;
    color: rgba(187, 189, 191, 1);
  }
}
</style>
