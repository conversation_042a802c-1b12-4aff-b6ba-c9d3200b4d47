<script setup lang="ts">
import { useTabsStoreHook } from "@/store/modules/tabs";
import type { MultiTagItem } from "@/store/modules/tabs";

/**
 * Tabs 标签页组件
 * 管理标签页的显示、切换、关闭等功能
 */
const router = useRouter();
const route = useRoute();
const tabsStore = useTabsStoreHook();

// 容器DOM引用
const containerDom = ref<HTMLElement>();
const scrollbarDom = ref<HTMLElement>();
const tabDom = ref<HTMLElement>();

// 是否显示左右箭头
const isShowArrow = ref(false);

/**
 * 处理滚轮事件
 */
const handleWheel = (e: WheelEvent) => {
  const eventDelta = e.deltaY || e.deltaX;
  scrollbarDom.value!.scrollLeft += eventDelta / 4;
};

/**
 * 处理滚动
 */
const handleScroll = (offset: number) => {
  scrollbarDom.value!.scrollLeft += offset;
};

/**
 * 标签页点击事件
 */
const tagOnClick = (item: MultiTagItem) => {
  const { name, path } = item;
  if (name) {
    if (item.query) {
      router.push({
        name,
        query: item.query,
      });
    } else if (item.params) {
      router.push({
        name,
        params: item.params,
      });
    } else {
      router.push({ name });
    }
  } else {
    router.push({ path, query: item.query, params: item.params });
  }
};

/**
 * 右键菜单
 */
const openMenu = (tag: MultiTagItem, e: MouseEvent) => {
  e.preventDefault();
  e.stopPropagation();

  // 获取点击的标签页元素
  const target = e.currentTarget as HTMLElement;
  const targetRect = target.getBoundingClientRect();

  // 获取页面滚动位置
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

  // 计算菜单位置：标签页下方居中，考虑页面滚动
  const x = targetRect.left + scrollLeft + targetRect.width / 2;
  const y = targetRect.bottom + scrollTop + 5;

  tabsStore.showContextMenu(tag, { x, y });
};

/**
 * 删除标签页
 */
const deleteMenu = (item: MultiTagItem) => {
  tabsStore.removeTab(item.path);

  // 如果删除的是当前激活的标签页，需要跳转到最后一个标签页
  if (tabsStore.isTabActive(item, route)) {
    const remainingTabs = tabsStore.multiTags;
    if (remainingTabs.length > 0) {
      const lastTab = remainingTabs[remainingTabs.length - 1];
      tagOnClick(lastTab);
    }
  }
};

/**
 * 检查标签页是否激活
 */
const isActive = (item: MultiTagItem) => {
  return tabsStore.isTabActive(item, route);
};

// 监听路由变化，智能添加标签页
watch(
  () => [route.path, route.query, route.params],
  () => {
    tabsStore.smartAddTab(route);
  },
  { immediate: true, deep: true }
);

// 监听点击事件关闭右键菜单
onMounted(() => {
  document.addEventListener("click", tabsStore.hideContextMenu);
});

onUnmounted(() => {
  document.removeEventListener("click", tabsStore.hideContextMenu);
});
</script>

<template>
  <div ref="containerDom" class="tabs-container">
    <!-- 左箭头 -->
    <span v-show="isShowArrow" class="arrow-left" @click="handleScroll(200)">
      <i class="el-icon-arrow-left"></i>
    </span>

    <!-- 标签页滚动容器 -->
    <div
      ref="scrollbarDom"
      class="scroll-container"
      @wheel.prevent="handleWheel"
    >
      <div ref="tabDom" class="tab-list">
        <div
          v-for="item in tabsStore.multiTags"
          :key="tabsStore.generateTabKey(item)"
          :class="['tab-item', { 'is-active': isActive(item) }]"
          @contextmenu.prevent="openMenu(item, $event)"
          @click="tagOnClick(item)"
        >
          <span class="tab-title">
            {{ item.meta.title }}
          </span>
          <span
            v-if="!item.meta.affix"
            class="tab-close"
            @click.stop="deleteMenu(item)"
          >
            <i class="el-icon-close"></i>
          </span>
        </div>
      </div>
    </div>

    <!-- 右箭头 -->
    <span v-show="isShowArrow" class="arrow-right" @click="handleScroll(-200)">
      <i class="el-icon-arrow-right"></i>
    </span>

    <!-- 右键菜单 -->
    <transition name="el-zoom-in-top">
      <ul
        v-show="tabsStore.contextMenuVisible"
        :style="{
          left: tabsStore.contextMenuPosition.x + 'px',
          top: tabsStore.contextMenuPosition.y + 'px',
        }"
        class="context-menu"
      >
        <li
          v-for="item in tabsStore.getContextMenuItems"
          :key="item.key"
          :class="{ disabled: item.disabled, divided: item.divided }"
          @click="tabsStore.handleContextMenuClick(item.key)"
        >
          <i :class="item.icon"></i>
          {{ item.label }}
        </li>
      </ul>
    </transition>

    <!-- 下拉菜单 -->
    <el-dropdown
      v-if="false"
      trigger="click"
      placement="bottom-end"
      @command="tabsStore.handleContextMenuClick"
    >
      <span class="dropdown-trigger">
        <i class="el-icon-arrow-down"></i>
      </span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item
          v-for="item in tabsStore.getContextMenuItems"
          :key="item.key"
          :command="item.key"
          :divided="item.divided"
          :disabled="item.disabled"
        >
          <i :class="item.icon"></i>
          {{ item.label }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<style scoped lang="less">
.tabs-container {
  height: 42px;
  position: relative;
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border-bottom: 1px solid #ffffff;
  padding: 0 8px;

  .arrow-left,
  .arrow-right {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #909399;
    background-color: #ffffff;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    margin: 0 4px;
    transition: all 0.2s;

    &:hover {
      color: var(--primary-color);
      border-color: var(--primary-color);
      background-color: #f0f9ff;
    }
  }

  .scroll-container {
    flex: 1;
    overflow: hidden;
    white-space: nowrap;
    box-sizing: border-box;

    .tab-list {
      display: flex;
      align-items: center;
      height: 40px;
      transition: transform 0.3s;

      .tab-item {
        display: inline-flex;
        box-sizing: border-box;
        align-items: center;
        height: 30px;
        padding: 0 12px;
        margin: 0 2px;
        background-color: #ffffff;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s ease;
        white-space: nowrap;
        user-select: none;

        &:hover {
          background-color: #f5f7fa;
          border-color: var(--primary-color);
          color: var(--primary-color);
          border-bottom: 2px solid var(--primary-color);
        }

        &.is-active {
          background-color: #ffffff;
          border-bottom: 2px solid var(--primary-color);
          color: var(--primary-color);

          .tag-title {
            color: var(--primary-color);
            font-weight: 500;
          }

          .el-icon-close {
            color: var(--primary-color);
          }
        }

        .tab-title {
          font-size: 13px;
          color: #606266;
          margin-right: 6px;
          transition: all 0.2s;
          line-height: 1.2;
          max-width: 120px;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .tab-close {
          width: 16px;
          height: 16px;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          transition: all 0.2s;
          font-size: 12px;
          color: #c0c4cc;
          opacity: 0;
          display: none;

          &:hover {
            background-color: rgba(255, 108, 108, 0.1);
            color: #f56c6c;
          }
        }

        &:hover .tab-close,
        &.is-active .tab-close {
          opacity: 1;
          display: inline-flex;
        }
      }
    }
  }

  .dropdown-trigger {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #909399;
    background-color: #ffffff;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    margin-left: 4px;
    transition: all 0.2s;

    &:hover {
      color: var(--primary-color);
      border-color: var(--primary-color);
      background-color: #f0f9ff;
    }
  }
}

// 右键菜单样式
.context-menu {
  position: fixed;
  background-color: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  padding: 8px 0;
  margin: 0;
  list-style: none;
  z-index: 9999;
  min-width: 160px;

  li {
    display: flex;
    align-items: center;
    padding: 10px 16px;
    font-size: 14px;
    color: #606266;
    cursor: pointer;
    transition: all 0.2s;
    line-height: 1.4;

    &:hover:not(.disabled) {
      background-color: #f5f7fa;
      color: var(--primary-color);
    }

    &.disabled {
      color: #c0c4cc;
      cursor: not-allowed;
    }

    &.divided {
      border-top: 1px solid #e4e7ed;
    }

    i {
      margin-right: 10px;
      font-size: 16px;
      width: 16px;
      text-align: center;
    }
  }
}
</style>
