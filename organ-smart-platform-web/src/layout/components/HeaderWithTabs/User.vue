<script setup lang="ts">
import { useTabsStoreHook } from "@/store/modules/tabs";

/**
 * 用户信息组件
 * 显示用户头像、姓名，提供用户操作菜单
 */
const layoutStore = useLayoutStoreHook();
const userStore = useUserStoreHook();
const tabsStore = useTabsStoreHook();

// 用户信息
const userInfo = computed(() => {
  return {
    name: userStore.user.name || userStore.userInfo.name || "用户",
    avatar: userStore.user.avatar || userStore.userInfo.avatar || "",
  };
});

/**
 * 处理退出登录
 */
const handleLogout = async () => {
  try {
    // 清理标签页
    tabsStore.closeAllTabs();

    // 清理用户信息
    userStore.clearUserInfo();
  } catch (error) {
    // 用户取消退出
    console.log("退出登录失败");
  }
};

/** 处理命令 */
const handleCommand = (command: string) => {
  switch (command) {
    case "logout":
      handleLogout();
      break;
  }
};
</script>

<template>
  <el-dropdown trigger="click" @command="handleCommand">
    <div class="reference-btn" :style="{ height: layoutStore.header.height }">
      <span class="user-name">{{ userInfo.name }}</span>
      <div class="user-avatar">
        <img v-if="userInfo.avatar" :src="userInfo.avatar" />
        <i v-else class="el-icon-user"></i>
      </div>
    </div>

    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item command="logout">
          <span style="color: #606266">退出登录</span>
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<style scoped lang="less">
.reference-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 0 12px;
  border-radius: 4px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }

  .user-name {
    margin-right: 8px;
    font-size: 14px;
    font-weight: 400;
    color: #606266;
  }

  .user-avatar {
    border: none;
    width: 42px;
    height: 42px;
    border-radius: 50%;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #b2b2b2;
    color: #ffffff;
    font-size: 20px;
  }
}
</style>
