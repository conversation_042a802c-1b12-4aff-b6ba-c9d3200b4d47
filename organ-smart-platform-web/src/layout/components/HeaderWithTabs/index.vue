<script setup lang="ts">
import Tabs from "./tabs.vue";
import User from "./User.vue";

/**
 * 头部组件（包含标签页）
 * 将原来的Header和Tabs组件合并，实现统一的头部区域
 */
const layoutStore = useLayoutStoreHook();
const route = useRoute();

// 获取主题色
const primaryColor = computed(() => layoutStore.theme.primaryColor);

// 当前页面标题
const currentPageTitle = computed(() => {
  return route.meta?.title || "机关事务管理系统";
});
</script>

<template>
  <div class="header-with-tabs" :style="{ '--primary-color': primaryColor }">
    <!-- 头部区域 -->
    <div class="header-section">
      <!-- 左侧：当前页面标题 -->
      <div class="header-left">
        <div class="page-title">
          {{ currentPageTitle }}
        </div>
      </div>

      <!-- 右侧：用户信息 -->
      <div class="header-right">
        <User />
      </div>
    </div>

    <!-- 标签页区域 -->
    <Tabs />
  </div>
</template>

<style scoped lang="less">
.header-with-tabs {
  width: 100%;
  background-color: #e6effd;
  border-bottom: 1px solid #e8e8e8;

  .header-section {
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    border-bottom: 1px solid #ffffff;

    .header-left {
      flex: 1;
      display: flex;
      align-items: center;

      .page-title {
        color: #606266;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .header-right {
      flex-shrink: 0;
      display: flex;
      align-items: center;
    }
  }
}
</style>
