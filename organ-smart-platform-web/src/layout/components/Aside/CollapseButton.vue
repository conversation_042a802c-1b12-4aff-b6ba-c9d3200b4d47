<script setup lang="ts">
/**
 * 折叠按钮组件
 * 用于控制侧边栏的展开和折叠
 */
const layoutStore = useLayoutStoreHook();

/** 是否折叠 */
const isCollapse = computed(() => {
  return layoutStore.aside.isCollapse;
});

/**
 * 处理折叠按钮点击
 */
const handleCollapseClick = () => {
  layoutStore.toggleCollapse();
};
</script>

<template>
  <div class="collapse-container">
    <div class="collapse-button" @click="handleCollapseClick">
      <!-- 折叠图标 -->
      <div
        class="collapse-icon"
        :style="{ color: layoutStore.aside.textColor }"
      >
        <transition name="flip" mode="out-in">
          <i
            class="el-icon-s-fold collapse-icon-inner"
            v-if="!isCollapse"
            key="expand"
          />
          <i
            class="el-icon-s-unfold collapse-icon-inner"
            v-else
            key="collapse"
          />
        </transition>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.collapse-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);

  .collapse-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 48px;
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }

    &.is-collapsed {
      justify-content: center;
    }

    .collapse-icon {
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      border: none;
      background-color: transparent;
      font-size: 20px;
      color: #ffffff;

      .collapse-icon-inner {
        font-size: 20px;
        color: #ffffff;
      }
    }
  }
}

// 左右翻转动画
.flip-enter-active,
.flip-leave-active {
  transition: all 0.3s ease;
}

.flip-enter-from {
  transform: scaleX(-1);
  opacity: 0;
}

.flip-leave-to {
  transform: scaleX(-1);
  opacity: 0;
}

.flip-enter-to,
.flip-leave-from {
  transform: scaleX(1);
  opacity: 1;
}
</style>
