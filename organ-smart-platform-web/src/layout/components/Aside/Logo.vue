<script setup lang="ts">
/**
 * Logo组件
 * 显示系统Logo和标题，支持折叠状态下的简化显示
 */
const layoutStore = useLayoutStoreHook();
</script>

<template>
  <div class="logo-container">
    <!-- Logo图标 -->
    <div class="logo-icon">
      <img
        src="https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg"
        alt="logo"
        class="logo-img"
      />
    </div>

    <!-- 系统标题 -->
    <transition name="fade">
      <div
        v-show="!layoutStore.aside.isCollapse"
        class="logo-title"
        :style="{ color: layoutStore.aside.textColor }"
      >
        {{ layoutStore.logo.title }}
      </div>
    </transition>
  </div>
</template>

<style scoped lang="less">
.logo-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 100%;
  padding: 0 24px;
  position: relative;

  .logo-icon {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;

    .logo-img {
      width: 32px;
      height: 32px;
    }
  }

  .logo-title {
    margin-left: 12px;
    font-size: 16px;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    color: #ffffff;
  }
}

// 淡入淡出动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
