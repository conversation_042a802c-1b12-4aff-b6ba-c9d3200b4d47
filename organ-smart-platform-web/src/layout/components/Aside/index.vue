<script setup lang="ts">
import Logo from "./Logo.vue";
import Menu from "./Menu.vue";
import CollapseButton from "./CollapseButton.vue";

/**
 * 侧边栏主组件
 * 包含Logo区域、菜单区域、折叠按钮区域
 */
const layoutStore = useLayoutStoreHook();
</script>

<template>
  <aside
    class="aside-container"
    :style="{
      width: layoutStore.getCurrentAsideWidth,
      backgroundColor: layoutStore.aside.backgroundColor,
    }"
  >
    <!-- Logo区域 -->
    <div
      class="logo-section"
      :style="{
        height: layoutStore.logo.height,
      }"
    >
      <Logo />
    </div>

    <!-- 菜单区域 -->
    <div class="menu-section">
      <Menu />
    </div>

    <!-- 折叠按钮区域 -->
    <div
      class="collapse-section"
      :style="{
        height: layoutStore.collapseButton.height,
      }"
    >
      <CollapseButton />
    </div>
  </aside>
</template>

<style scoped lang="less">
.aside-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  box-shadow: 2px 0 6px rgba(0, 0, 0, 0.1);

  .logo-section {
    flex-shrink: 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .menu-section {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: #d1d5db;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #9ca3af;
    }
  }

  .collapse-section {
    flex-shrink: 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
}
</style>
