<script setup lang="ts">
import { authImportRoutes } from "@/router";
import type { RouteConfig } from "vue-router";

/**
 * 菜单组件
 * 使用Element UI的el-menu组件实现，支持手风琴模式
 */
const layoutStore = useLayoutStoreHook();
const router = useRouter();
const route = useRoute();

// 菜单数据
const menuData = computed(() => {
  return authImportRoutes.filter((item) => item?.meta?.layout !== false);
});

// 当前激活的菜单项
const activeMenu = ref<string>("");

// 当前展开的子菜单（手风琴模式下只能展开一个）
const openedMenus = ref<string[]>([]);

// 菜单引用
const menuRef = ref();

/**
 * 监听路由变化，自动更新菜单激活状态和展开状态
 */
const updateMenuState = () => {
  const currentPath = route.path;
  activeMenu.value = currentPath;

  // 查找当前路由对应的父级菜单，自动展开
  const parentMenu = findParentMenu(currentPath);
  if (parentMenu) {
    openedMenus.value = [parentMenu.path];
  }
};

/**
 * 查找当前路由的父级菜单
 */
const findParentMenu = (currentPath: string): RouteConfig | null => {
  for (const menu of menuData.value) {
    if (menu.children && menu.children.length > 0) {
      const hasChild = menu.children.some(
        (child: RouteConfig) =>
          currentPath === child.path || currentPath.startsWith(child.path + "/")
      );
      if (hasChild) {
        return menu;
      }
    }
  }
  return null;
};

/**
 * 处理菜单选择事件
 */
const handleMenuSelect = (index: string) => {
  // 直接跳转到选中的路由
  router.push(index);
};

/**
 * 处理子菜单展开/收起事件
 */
const handleSubMenuToggle = (_index: string, _indexPath: string[]) => {
  // 手风琴模式下，el-menu会自动处理展开/收起
  // 这里可以添加额外的逻辑
};

/**
 * 获取菜单图标
 */
const getMenuIcon = (meta: any) => {
  return meta?.icon || "el-icon-menu";
};

// 监听路由变化
watch(
  () => route.path,
  () => {
    updateMenuState();
  },
  { immediate: true }
);

// 组件挂载时初始化菜单状态
onMounted(() => {
  updateMenuState();
});
</script>

<template>
  <div class="menu-container">
    <el-menu
      ref="menuRef"
      :default-active="activeMenu"
      :default-openeds="openedMenus"
      :collapse="layoutStore.aside.isCollapse"
      :unique-opened="true"
      background-color="transparent"
      text-color="#ffffff"
      active-text-color="#ffffff"
      @select="handleMenuSelect"
      @open="handleSubMenuToggle"
      @close="handleSubMenuToggle"
      class="custom-menu"
    >
      <!-- 遍历菜单数据 -->
      <template v-for="menuItem in menuData">
        <!-- 有多个子菜单的情况，渲染为子菜单组 -->
        <el-submenu
          v-if="menuItem.children && menuItem.children.length > 1"
          :index="menuItem.path"
          :key="menuItem.path"
          class="custom-submenu"
        >
          <template slot="title">
            <i :class="getMenuIcon(menuItem.meta)" class="menu-icon"></i>
            <span class="menu-title">{{ menuItem.meta?.title }}</span>
          </template>

          <!-- 子菜单项 -->
          <el-menu-item
            v-for="childItem in menuItem.children"
            :key="childItem.path"
            :index="childItem.path"
            class="custom-menu-item"
          >
            <i :class="getMenuIcon(childItem.meta)" class="submenu-icon"></i>
            <span class="submenu-title">{{ childItem.meta?.title }}</span>
          </el-menu-item>
        </el-submenu>

        <!-- 只有一个子菜单的情况，直接渲染子菜单 -->
        <el-menu-item
          v-else-if="menuItem.children && menuItem.children.length === 1"
          :index="menuItem.children[0].path"
          :key="menuItem.path"
          class="custom-menu-item"
        >
          <i
            :class="getMenuIcon(menuItem.children[0].meta)"
            class="menu-icon"
          ></i>
          <span class="menu-title">{{ menuItem.children[0].meta?.title }}</span>
        </el-menu-item>

        <!-- 没有子菜单的情况 -->
        <el-menu-item
          v-else
          :index="menuItem.path"
          :key="menuItem.path"
          class="custom-menu-item"
        >
          <i :class="getMenuIcon(menuItem.meta)" class="menu-icon"></i>
          <span class="menu-title">{{ menuItem.meta?.title }}</span>
        </el-menu-item>
      </template>
    </el-menu>
  </div>
</template>

<style scoped lang="less">
.menu-container {
  height: 100%;

  // 自定义菜单样式
  :deep(.custom-menu) {
    border: none;
    background-color: transparent;
    height: 100%;

    // 菜单项样式
    .el-menu-item {
      height: 56px;
      line-height: 56px;
      padding: 0 20px;
      color: #ffffff !important;
      background-color: transparent !important;
      border-bottom: none;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1) !important;
      }

      &.is-active {
        background-color: rgba(255, 255, 255, 0.2) !important;
        color: #ffffff !important;
      }

      .menu-icon,
      .submenu-icon {
        color: #ffffff;
        font-size: 18px;
        margin-right: 12px;
        width: 18px;
        text-align: center;
      }

      .menu-title,
      .submenu-title {
        color: #ffffff;
        font-size: 14px;
        font-weight: 400;
      }
    }

    // 子菜单样式
    .el-submenu {
      .el-submenu__title {
        height: 56px;
        line-height: 56px;
        padding: 0 20px;
        color: #ffffff !important;
        background-color: transparent !important;
        border-bottom: none;

        &:hover {
          background-color: rgba(255, 255, 255, 0.1) !important;
        }

        .menu-icon {
          color: #ffffff;
          font-size: 18px;
          margin-right: 12px;
          width: 18px;
          text-align: center;
        }

        .menu-title {
          color: #ffffff;
          font-size: 14px;
          font-weight: 400;
        }

        .el-submenu__icon-arrow {
          color: #ffffff;
          font-size: 12px;
        }
      }

      // 子菜单容器
      .el-menu {
        background-color: rgba(0, 0, 0, 0.1);

        .el-menu-item {
          height: 48px;
          line-height: 48px;
          padding-left: 50px;

          .submenu-icon {
            font-size: 16px;
            margin-right: 10px;
          }

          .submenu-title {
            font-size: 13px;
          }
        }
      }

      &.is-active {
        .el-submenu__title {
          .el-submenu__icon-arrow {
            color: #000000 !important;
          }
        }
      }

      &.is-opened {
        .el-submenu__title {
          // background-color: rgba(255, 255, 255, 0.1) !important;
        }
      }
    }

    // 折叠状态下的样式
    &.el-menu--collapse {
      width: 64px;

      .el-menu-item,
      .el-submenu .el-submenu__title {
        padding: 0 20px;
        text-align: center;

        .menu-icon,
        .submenu-icon {
          margin-right: 0;
          font-size: 20px;
        }

        .menu-title,
        .submenu-title {
          height: 0;
          width: 0;
          overflow: hidden;
          visibility: hidden;
          display: inline-block;
        }
      }

      .el-submenu {
        .el-submenu__icon-arrow {
          display: none;
        }

        .el-submenu__title .el-submenu__icon-arrow {
          display: none;
        }
      }

      // 折叠状态下的子菜单弹出效果
      .el-submenu .el-menu {
        position: absolute;
        left: 64px;
        top: 0;
        min-width: 200px;
        background-color: rgba(0, 0, 0, 0.8);
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        z-index: 99;
      }
    }
  }
}
</style>
