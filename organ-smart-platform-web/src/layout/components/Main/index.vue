<script setup lang="ts">
import { useTabsStoreHook } from "@/store/modules/tabs";

/**
 * Main主内容区组件
 * 显示路由页面内容，支持keep-alive缓存和自适应布局
 */
const tabsStore = useTabsStoreHook();
const route = useRoute();

// 计算主内容区的样式 - 自动占满剩余区域，背景白色，外边距16px
const mainStyle = computed(() => {
  return {
    margin: "16px",
    backgroundColor: "#ffffff",
    height: "calc(100% - 32px)", // 减去上下边距
    borderRadius: "8px",
    boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
  };
});

// 监听路由变化，更新标签页
watch(
  () => route,
  (newRoute) => {
    if (newRoute.meta?.history !== false) {
      // 添加标签页，激活状态由路由变化自动管理
      tabsStore.addTab(newRoute);
    }
  },
  { immediate: true, deep: true }
);
</script>

<template>
  <main class="main-container" :style="mainStyle">
    <div class="main-scrollbar">
      <div class="router-view-content">
        <keep-alive :include="tabsStore.cachedViews">
          <router-view v-if="$route.meta?.keepAlive" :key="$route.fullPath" />
        </keep-alive>
        <router-view v-if="!$route.meta?.keepAlive" :key="$route.fullPath" />
      </div>
    </div>
  </main>
</template>

<style scoped lang="less">
.main-container {
  height: 100%;
  transition: all 0.3s ease;
  overflow: hidden;

  .main-scrollbar {
    height: 100%;
    overflow: auto;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(144, 147, 153, 0.3);
      border-radius: 3px;
      transition: background 0.3s;

      &:hover {
        background: rgba(144, 147, 153, 0.5);
      }
    }

    &::-webkit-scrollbar-corner {
      background: #f1f1f1;
    }

    // 内容区域
    :deep(.router-view-content) {
      padding: 24px;
      min-height: calc(100% - 48px);
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .main-container {
    .main-content {
      border-radius: 0;
      box-shadow: none;

      .main-scrollbar {
        :deep(.el-scrollbar__view) {
          padding: 16px;
        }
      }
    }
  }
}
</style>
