<script lang="ts" setup>
import Aside from "./components/Aside/index.vue";
import HeaderWithTabs from "./components/HeaderWithTabs/index.vue";
import Main from "./components/Main/index.vue";

/**
 * Layout主布局组件
 * 使用Element UI的Container布局容器实现
 * 布局结构：侧边栏 + (头部包含标签页 + 主内容区)
 */
const route = useRoute();
const layoutStore = useLayoutStoreHook();

// 是否为全屏模式（不显示布局组件）
const isFullScreen = computed(() => {
  return (
    route.meta?.fill ||
    route.query.embedded === "true" ||
    route.query.authenticationCode
  );
});

// 计算侧边栏宽度
const asideWidth = computed(() => {
  return layoutStore.getCurrentAsideWidth;
});
</script>

<template>
  <div class="layout-wrapper">
    <!-- 全屏模式：只显示Main组件 -->
    <Main v-if="isFullScreen" />

    <!-- 正常布局模式：使用Element UI Container布局 -->
    <el-container v-else class="layout-container">
      <!-- 侧边栏 -->
      <el-aside :width="asideWidth" class="layout-aside">
        <Aside />
      </el-aside>

      <!-- 右侧容器：头部+主内容区 -->
      <el-container class="layout-main-container">
        <!-- 头部（包含标签页） -->
        <el-header class="layout-header">
          <HeaderWithTabs />
        </el-header>

        <!-- 主内容区 -->
        <el-main class="layout-main">
          <Main />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<style scoped lang="less">
.layout-wrapper {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

.layout-container {
  height: 100vh;

  .layout-aside {
    height: 100vh;
    overflow: hidden;
    transition: width 0.3s ease;

    // 移除默认的padding
    :deep(.el-aside) {
      padding: 0;
    }
  }

  .layout-main-container {
    height: 100vh;
    overflow: hidden;
  }

  .layout-header {
    height: auto !important; // 允许头部高度自适应
    padding: 0;
    overflow: hidden;

    // 移除默认的padding
    :deep(.el-header) {
      padding: 0;
    }
  }

  .layout-main {
    padding: 0;
    overflow: hidden;
    background-color: #f0f2f5;

    // 移除默认的padding
    :deep(.el-main) {
      padding: 0;
    }
  }
}
</style>
