import NProgress from "nprogress";
import type VueRouter from "vue-router";
import { objectToUrlParams } from "@/util/object";
import { isEmpty } from "lodash-es";
import { TOKEN, ACCESS_TOKEN } from "@/constant/storage";
import { useUserStoreHook } from "@/store/modules/user";
import { verifyAuthenticationCode } from "@/api/creditengine";
import { Message } from "element-ui";

const userStoreHook = useUserStoreHook();

const whiteList = [
  "/403",
  "/login/loading",
  "/mobile/decode",
  "/mobile/creditProfileMobile",
  "/mobile/creditProfileMobileDetail",
  "/corpCenter/myCredit/common/previewCreditReport",
];

NProgress.configure({ showSpinner: false }); // 进度条
export function commonBeforeHook(router: VueRouter) {
  router.beforeEach(async (to, _from, next) => {
    // 白名单
    if (whiteList.includes(to.path)) {
      next();
    }
    // 携带了 ticketSNO 的都会去 loading页面换取用用户信息
    // else if (to.query.accessToken && to.path !== "/login/loading") {
    // 携带了 accessToken 的都会去 loading页面换取用用户信息
    else if (to.query.accessToken && to.path !== "/login/loading") {
      const query = { ...to.query };
      // delete query.ticketSNO;
      // delete query.authenticationCode;
      delete query.accessToken;
      next({
        path: "/login/loading",
        query: {
          accessToken: to.query.accessToken,
          // ticketSNO: to.query.ticketSNO,
          // toUrl: `${to.path}?${encodeURIComponent(objectToUrlParams(query))}`,
        },
      });
    }
    // 兼容旧版本的 authenticationCode 登录
    else if (localStorage.getItem("authenticationCode")) {
      next();
    } else if (to.query.authenticationCode) {
      try {
        const authenticationCode = String(to.query.authenticationCode);
        await verifyAuthenticationCode(authenticationCode);
        localStorage.setItem("authenticationCode", authenticationCode);
        next();
      } catch {
        localStorage.removeItem("authenticationCode");
        next("/403");
      }
    }
    // 如果没有 token 则跳转至登录页面
    // else if (isEmpty(sessionStorage.getItem(TOKEN))) {
    else if (isEmpty(userStoreHook.getToken())) {
      userStoreHook.clearUserInfo();
    } else {
      userStoreHook.loginCheck();
      next();
    }
  });
}
