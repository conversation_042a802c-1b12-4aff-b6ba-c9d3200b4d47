import type VueRouter from "vue-router";

import { baseConfig } from "@/configs/baseConfig";

export function setTitleHook(router: VueRouter) {
  router.afterEach((to) => {
    if (to.meta && typeof to.meta.title === "string" && to.meta.title !== "") {
      document.title = baseConfig.title;
      // document.title = to.meta.title || baseConfig.title;
      return;
    }
    // document.title = baseConfig.title;
  });
}
