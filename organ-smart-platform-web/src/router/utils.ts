import type { RouteConfig } from "vue-router";
import { defaultMeta } from "./config";

/**
 * 创建模块路由
 * @param route
 * @returns
 */
export function createModuleRoute(
  route: RouteConfig,
  parent?: RouteConfig
): RouteConfig {
  // 拼接父路径
  if (parent?.path && !route.path.startsWith("/")) {
    if (!route.path) {
      route.path = parent.path;
    } else if (parent.path.endsWith("/")) {
      route.path = `${parent.path}${route.path}`;
    } else {
      route.path = `${parent.path}/${route.path}`;
    }
  }

  route.meta = Object.assign({}, { ...defaultMeta }, route.meta);

  // 递归创建子路由
  if (Array.isArray(route.children) && route.children.length) {
    route.children = route.children.map((item) =>
      createModuleRoute(item, route)
    );
  }

  // 重定向 等子路由创建完毕后，再重定向
  if (!route.redirect && !(route as any).component && route.children?.length) {
    route.redirect = route.children[0].path;
  }
  return route;
}
