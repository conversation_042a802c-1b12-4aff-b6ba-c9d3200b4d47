import type { RouteConfig } from "vue-router";

export default {
  path: "/bookingManage",
  component: () => import("@/layout/index.vue"),
  meta: {
    title: "核销管理",
    layout: true,
    icon: "el-icon-menu",
    index: 1,
    permission: "booking_manage",
  },
  children: [
    {
      path: "/bookingManage/checkInManage",
      name: "CheckInManage",
      component: () =>
        import("@/views/canteen/bookingManage/checkInManage/index.vue"),
      meta: {
        title: "核销情况",
        icon: "el-icon-menu",
        keepAlive: false,
        history: true,
      },
    },
    {
      path: "/bookingManage/reconciliation",
      name: "Reconciliation",
      component: () =>
        import("@/views/canteen/bookingManage/reconciliation/index.vue"),
      meta: {
        title: "对账管理",
        icon: "el-icon-menu",
        keepAlive: false,
        history: true,
      },
    },
  ],
} as RouteConfig;
