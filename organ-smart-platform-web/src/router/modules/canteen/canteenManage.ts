import type { RouteConfig } from "vue-router";

export default {
  path: "/canteenManage",
  component: () => import("@/layout/index.vue"),
  meta: {
    title: "食堂管理",
    layout: true,
    icon: "el-icon-menu",
    index: 1,
    permission: "canteen_manage",
  },
  children: [
    {
      path: "/canteenManage/infoManage",
      name: "CanteenInfoManage",
      component: () =>
        import("@/views/canteen/canteenManage/infoManage/index.vue"),
      meta: {
        title: "食堂信息",
        icon: "el-icon-menu",
        keepAlive: false,
        history: true,
      },
    },
    {
      path: "/canteenManage/urgeRequest",
      name: "CanteenUrgeRequest",
      component: () =>
        import("@/views/canteen/canteenManage/urgeRequest/index.vue"),
      meta: {
        title: "催发管理",
        icon: "el-icon-menu",
        keepAlive: false,
        history: true,
      },
    },
  ],
} as RouteConfig;
