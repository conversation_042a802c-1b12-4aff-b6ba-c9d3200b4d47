import type { RouteConfig } from "vue-router";
export default {
  path: "/login",
  component: () => import("@/layout/index.vue"),
  meta: {
    title: "登录",
    layout: false,
  },
  children: [
    {
      path: "loading",
      component: () => import("@/views/login/loading.vue"),
      meta: {
        menu: false,
        fill: true,
        title: "登录",
        history: false,
        keepAlive: false,
      },
    },
  ],
} as RouteConfig;
