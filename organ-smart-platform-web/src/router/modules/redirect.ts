import type { RouteConfig } from "vue-router";

export default {
  path: "/redirect",
  component: () => import("@/layout/index.vue"),
  meta: {
    title: "重定向",
    layout: false,
    hiddenTag: true,
  },
  children: [
    {
      path: "/redirect/:path(.*)",
      component: () => import("@/views/redirect/index.vue"),
      meta: {
        title: "重定向",
        hiddenTag: true,
        history: false,
      },
    },
  ],
} as RouteConfig;
