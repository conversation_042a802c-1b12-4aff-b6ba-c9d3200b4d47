import type { RouteConfig } from "vue-router";

export default {
  path: "/welcome",
  meta: {
    title: "首页",
    layout: true,
    icon: "el-icon-house",
    index: 0,
    affix: true,
  },
  component: () => import("@/layout/index.vue"),
  children: [
    {
      path: "",
      name: "Welcome",
      meta: {
        title: "首页",
        history: true,
        keepAlive: false,
        affix: true,
        icon: "el-icon-house",
      },
      component: () => import("@/views/welcome/index.vue"),
    },
  ],
} as RouteConfig;
