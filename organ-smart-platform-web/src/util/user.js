import { getStore } from "@/util/store";

/**
 * 判断用户是否登录
 */
export function isLogined() {
  return [
    getStore({ name: "access_token" }),
    // getCookie("jvs_session_uid"),
  ].some(Boolean);
}

/**
 * 获取cookie
 */
export function getCookie(cname) {
  let name = cname + "=";
  let ca = document.cookie.split(";");
  for (let i = 0; i < ca.length; i++) {
    let c = ca[i].trim();
    if (c.indexOf(name) == 0) {
      return c.substring(name.length, c.length);
    }
  }
}

/**
 * 获取登录url
 */
export function getLoginUrl() {
  return process.env.VUE_APP_LOGIN_URL;
}

/**
 * 跳转至登录页
 */
export function toLogin() {
  window.location.href = getLoginUrl();
}
