import { env } from "@/configs/env";
import { TOKEN, TICKET_SNO } from "@/constant/storage";

/** 前往登录，并清空用户信息 */
export function toLogin() {
  sessionStorage.removeItem("authenticationCode");
  sessionStorage.removeItem(TOKEN);
  localStorage.removeItem(TICKET_SNO);
  const fullUrl = window.location.href;
  const loginUrl = `${env.externalLogin}?backUrl=${encodeURIComponent(
    fullUrl
  )}`;
  location.href = loginUrl;
}
