import { verifyAuthenticationCode } from '@/api/creditengine';

/**
 * webview 用户信息校验
 * @param {function|undefined} cb
 * @returns
 */
export function checkWebViewAuth(cb) {
    const route = useRoute();
    const { authenticationCode } = route.query;

    if (authenticationCode) {
        const localCode = localStorage.getItem('authenticationCode');
        if (localCode !== authenticationCode) {
            // console.log('webview 开始用户信息校验....');
            // console.log('本地code', localCode);
            // console.log('传入code', authenticationCode);
            verifyAuthenticationCode(authenticationCode)
                .then(() => {
                    localStorage.setItem('authenticationCode', authenticationCode);
                    // console.log('webview 用户信息校验成功....');
                    // authenticationStore.vberifiedAlready.push(authenticationCode);
                    cb?.();
                })
                .catch(() => {
                    localStorage.removeItem('authenticationCode');
                    router.push('/403');
                });
            return;
        }
        // console.log('code 一致, 直接执行');
        cb?.();
    } else {
        cb?.();
    }
}
