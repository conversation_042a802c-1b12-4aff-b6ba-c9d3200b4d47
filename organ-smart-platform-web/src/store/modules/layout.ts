import { store } from "@/store";

/**
 * 布局配置store
 * 管理整个应用的布局状态和配置
 */
const useLayoutStore = defineStore("layout", () => {
  // 侧边栏配置
  const aside = ref({
    isCollapse: false, // 是否折叠
    expandedWidth: "280px", // 展开宽度
    collapsedWidth: "64px", // 折叠宽度
    textColor: "#FFFFFF", // 文字颜色
    activeTextColor: "#FFFFFF", // 激活状态文字颜色
    backgroundColor: "#3775FF", // 背景色
    activeBackgroundColor: "#1890FF", // 激活状态背景色
    hoverBackgroundColor: "rgba(255, 255, 255, 0.1)", // 悬停背景色
  });

  // 头部配置
  const header = ref({
    height: "64px", // 头部高度
    textColor: "#606266", // 文字颜色
    backgroundColor: "#E6EFFD", // 背景色
    borderBottom: "1px solid #f0f0f0", // 底部边框
  });

  // 主内容区配置
  const main = ref({
    backgroundColor: "#F0F2F5", // 背景色
    expandedMarginLeft: "0px", // 侧边栏展开时的左边距
    collapsedMarginLeft: "0px", // 侧边栏折叠时的左边距
    topMargin: "0px", // 距离头部的上边距
    padding: "16px", // 内边距
  });

  // Logo区域配置
  const logo = ref({
    height: "64px", // Logo区域高度
    title: "跨区就餐后台管理系统", // 系统标题
    collapsedTitle: "AD", // 折叠时显示的简短标题
  });

  // 折叠按钮配置
  const collapseButton = ref({
    height: "50px", // 折叠按钮区域高度
  });

  // 主题色配置
  const theme = ref({
    primaryColor: "#409eff", // 主题色
  });

  /**
   * 切换侧边栏折叠状态
   */
  const toggleCollapse = () => {
    aside.value.isCollapse = !aside.value.isCollapse;
  };

  /**
   * 设置侧边栏折叠状态
   * @param collapse 是否折叠
   */
  const setCollapse = (collapse: boolean) => {
    aside.value.isCollapse = collapse;
  };

  /**
   * 获取当前侧边栏宽度
   */
  const getCurrentAsideWidth = computed(() => {
    return aside.value.isCollapse
      ? aside.value.collapsedWidth
      : aside.value.expandedWidth;
  });

  /**
   * 获取当前主内容区左边距
   */
  const getCurrentMainMarginLeft = computed(() => {
    return aside.value.isCollapse
      ? main.value.collapsedMarginLeft
      : main.value.expandedMarginLeft;
  });

  return {
    aside,
    header,
    main,
    logo,
    collapseButton,
    theme,
    toggleCollapse,
    setCollapse,
    getCurrentAsideWidth,
    getCurrentMainMarginLeft,
  };
});

export function useLayoutStoreHook() {
  return useLayoutStore(store);
}
