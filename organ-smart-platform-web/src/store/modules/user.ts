import { store } from "@/store";
import { TOKEN, TICKET_SNO, ACCESS_TOKEN } from "@/constant/storage";
import { clearStore, getStore, removeStore, setStore } from "@/util/store";
import { getUserToken } from "@/api/user";
import { toLogin } from "@/util/login";

export interface UserInfo {
  name: string;
  avatar: string;
  adminFlag: boolean;
  permissions: string[];
}

const useUserStore = defineStore("user", () => {
  /** 用户信息 */
  const user = ref<Partial<UserInfo>>({});

  /** 用户信息 */
  const userInfo = ref<Partial<UserInfo>>({});

  const ticketSNO = ref("");

  const accessToken = ref("");

  const token = ref("");

  /** 设置 token */
  function setToken(loginToken: string) {
    token.value = loginToken;
    setStore({ name: TOKEN, content: loginToken });
  }

  /** 清理 token */
  function clearToken() {
    token.value = "";
    removeStore({ name: TOKEN });
  }

  /** 设置 accessToken  */
  function setAccessToken(scretictKey: string) {
    accessToken.value = scretictKey;
    setStore({ name: ACCESS_TOKEN, content: scretictKey });
  }
  /** 清理 accessToken  */
  function clearAccessToken() {
    accessToken.value = "";
    removeStore({ name: ACCESS_TOKEN });
  }

  /** 设置用户信息 */
  function setStoreUserInfo(userInfo: Partial<UserInfo>) {
    user.value = userInfo;
    setStore({ name: "userInfo", content: userInfo });
  }

  /** 清理用户信息 */
  function clearStoreUserInfo() {
    user.value = {};
    removeStore({ name: "userInfo", content: {} });
  }

  /** 获取登录后的token */
  const getToken = () => {
    return getStore({ name: TOKEN });
  };

  /**
   * 获取用户信息
   */
  function getUserInfo(secretKey: string) {
    if (process.env.VUE_APP_ENV === "DEV" && secretKey === "123456") {
      user.value = {
        name: "系统管理员",
        avatar: "",
        adminFlag: true,
        permissions: ["*"],
      };
      setStoreUserInfo(user.value);
      setToken("123456");
      setAccessToken(secretKey);
      return Promise.resolve();
    }

    return getUserToken(secretKey).then((res) => {
      setToken(res.token);
      setAccessToken(secretKey);
      setStoreUserInfo(res);
      // sessionStorage.setItem(TOKEN, res);
      // ticketSNO.value = TICKET_SNO;
      // localStorage.setItem(TICKET_SNO, secretKey);
    });
  }

  /** 前端登出 */
  function clearUserInfo() {
    user.value = {};
    ticketSNO.value = "";
    clearStoreUserInfo();
    clearToken();
    clearAccessToken();
    toLogin();
  }

  /** 重置用户store信息 */
  function resetUserStore() {
    const token = getStore({ name: TOKEN });
    if (token) {
      setToken(token);
    }
    const userInfo = getStore({ name: "userInfo" });
    if (userInfo) {
      setStoreUserInfo(userInfo);
    }
    const accessToken = getStore({ name: ACCESS_TOKEN });
    if (accessToken) {
      setAccessToken(accessToken);
    }
  }

  /** 登录检查 */
  function loginCheck() {
    const token = getStore({ name: TOKEN });
    if (!token) {
      clearUserInfo();
      return false;
    }
    resetUserStore();
    return true;
  }

  return {
    user,
    userInfo,
    ticketSNO,
    getUserInfo,
    clearUserInfo,
    accessToken,
    token,
    getToken,
    loginCheck,
  };
});

export function useUserStoreHook() {
  return useUserStore(store);
}
