import { store } from '@/store';
import { queryDictionary } from '@/api/qryDatabase/Dictionary';
export const useDictionaryStore = defineStore('dictionary', () => {
    const departurePortList = ref<any>([]); // 离境口岸
    const supervisionModeList = ref<any>([]); // 监管方式
    const customsCreditInspectionResult = ref<any>([]); // 通关信用查验结果

    /** 异议申述分类 */
    const objectionAppealClass = ref<
        {
            /** 分类 */
            label: string;
            /** 分类 */
            value: string;
            children: {
                /** 标签名 */
                label: string;
                /** 标签名 */
                value: string;
            }[];
        }[]
    >([]);

    /** 获取离境口岸列表 */
    const getDeparturePortList = async () => {
        if (departurePortList.value.length === 0) {
            const result = await queryDictionary('离境口岸');
            departurePortList.value = result.map((item: any) => ({
                name: item.dictionaryValue,
                value: item.dictionaryValue,
            }));
        }
        return departurePortList.value;
    };

    /** 获取监管方式列表 */
    const getSupervisionModeList = async () => {
        if (supervisionModeList.value.length === 0) {
            const result = await queryDictionary('监管方式');
            supervisionModeList.value = result.map((item: any) => ({
                name: item.dictionaryValue,
                value: item.dictionaryValue,
            }));
        }
        return supervisionModeList.value;
    };

    /** 获取监管方式列表 */
    const getCustomsCreditInspectionResult = async () => {
        if (customsCreditInspectionResult.value.length === 0) {
            const result = await queryDictionary('查验结果');
            customsCreditInspectionResult.value = result.map((item: any) => ({
                name: item.dictionaryValue,
                value: item.dictionaryValue,
            }));
        }
        return customsCreditInspectionResult.value;
    };

    const getObjectionAppealClass = () => {
        if (objectionAppealClass.value.length === 0) {
            queryDictionary('异议申述分类').then((res: any[]) => {
                const dict = new Map<
                    string,
                    {
                        /** 标签名 */
                        label: string;
                        /** 数据来源单位 */
                        value: string;
                    }[]
                >();

                (res || []).forEach((item) => {
                    const value = dict.get(item.dictionaryLevel2) || [];
                    value.push({
                        label: item.dictionaryLevel3,
                        value: item.dictionaryLevel3,
                    });
                    dict.set(item.dictionaryLevel2, value);
                });
                const arr = [];
                for (const [key, value] of dict) {
                    arr.push({
                        label: key,
                        value: key,
                        children: value,
                    });
                }
                objectionAppealClass.value = arr;
            });
        }
    };
    return {
        getDeparturePortList,
        getSupervisionModeList,
        getCustomsCreditInspectionResult,
        getObjectionAppealClass,
        objectionAppealClass,
    };
});

export function useDictionaryStoreHook() {
    return useDictionaryStore(store);
}
