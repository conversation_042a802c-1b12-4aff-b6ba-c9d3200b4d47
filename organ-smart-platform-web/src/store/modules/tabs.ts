import { store } from "@/store";
import router from "@/router";
import { useRouter } from "@/router";

/**
 * 额外的Meta信息
 */
export interface MultiTagMeta {
  /** 页面标题 */
  title: string;
  /** tabName(用于展示title) */
  tabName: string;
  /** 图标 */
  icon?: string;
  /** 是否隐藏标签 */
  hiddenTag?: boolean;
  /** 是否显示链接 */
  showLink?: boolean;
  /** 动态路由级别 */
  dynamicLevel?: number;
  /** 是否固定标签 */
  affix?: boolean;
  /** 是否缓存 */
  keepAlive?: boolean;
}

/**
 * 多标签页项接口
 */
export interface MultiTagItem {
  /** 路由路径 */
  path: string;
  /** 路由全路径 */
  fullPath: string;
  /** 路由名称 */
  name?: string;
  /** 路由meta信息 */
  meta: MultiTagMeta;
  /** 路由参数 */
  params?: Record<string, any>;
  /** 查询参数 */
  query?: Record<string, any>;
}

/**
 * 位置参数接口
 */
export interface PositionType {
  startIndex?: number;
  length?: number;
}

/**
 * 右键菜单项接口
 */
export interface ContextMenuItem {
  key: string;
  label: string;
  icon: string;
  disabled?: boolean;
  divided?: boolean;
}

/**
 * 多标签页管理store
 * 管理已打开的页面标签
 */
const useTabsStore = defineStore("multiTags", () => {
  // 存储标签页信息（路由信息），默认值layout为true，affix为true的所有路由
  const multiTags = ref<MultiTagItem[]>([
    {
      path: "/welcome",
      fullPath: "/welcome",
      // name: "welcome",
      meta: {
        title: "首页",
        tabName: "首页",
        icon: "el-icon-s-home",
        hiddenTag: false,
        showLink: true,
        dynamicLevel: 0,
        affix: true,
        keepAlive: true,
      },
      params: {},
      query: {},
    },
  ]);

  // 是否缓存多标签页
  const multiTagsCache = ref<boolean>(true);

  // 缓存的页面列表（用于keep-alive）
  const cachedViews = ref<string[]>([]);

  // 右键菜单显示状态
  const contextMenuVisible = ref<boolean>(false);

  // 右键菜单位置
  const contextMenuPosition = ref<{ x: number; y: number }>({ x: 0, y: 0 });

  // 当前右键选中的标签页
  const currentContextTag = ref<MultiTagItem | null>(null);

  /** 比较是否是同一个路由(tabItem) */
  const isSameRoute = (tagItem: MultiTagItem, route: any) => {
    // if (Object.keys(route.query || {}).length > 0) {
    //   return (
    //     JSON.stringify(route.query) === JSON.stringify(tagItem.query) &&
    //     tagItem.path === route.path
    //   );
    // } else if (Object.keys(route.params || {}).length > 0) {
    //   return (
    //     JSON.stringify(route.params) === JSON.stringify(tagItem.params) &&
    //     tagItem.path === route.path
    //   );
    // } else {
    //   return tagItem.path === route.path;
    // }
    return tagItem.path === route.path;
  };

  /** 初始化multiTags */
  const initMultiTags = () => {
    multiTags.value = router
      .getRoutes()
      .filter((route) => {
        return route.meta?.layout && route.meta?.affix;
      })
      .map((route) => {
        return {
          path: route.path,
          fullPath: route.path,
          name: route.name as string,
          meta: {
            title: route.meta?.title || "未命名页面",
            tabName: route.meta?.tabName || route.meta?.title || "未命名页面",
            icon: route.meta?.icon,
            hiddenTag: route.meta?.hiddenTag,
            showLink: route.meta?.showLink,
            dynamicLevel: route.meta?.dynamicLevel,
            affix: route.meta?.affix,
            keepAlive: route.meta?.keepAlive,
          },
          params: {},
          query: {},
        };
      });
    cachedViews.value = multiTags.value
      .filter((tag) => tag.meta?.keepAlive)
      .map((tag) => tag.name as string);
  };

  /**
   * 处理标签页操作
   * @param mode 操作模式
   * @param value 操作值
   * @param position 位置参数
   */
  const handleTags = <T>(
    mode: string,
    value?: T | MultiTagItem,
    position?: PositionType
  ): T | void => {
    switch (mode) {
      case "equal":
        multiTags.value = value as MultiTagItem[];
        cachedViews.value = multiTags.value
          .filter((tag) => tag.meta?.keepAlive)
          .map((tag) => tag.name as string);
        // 如果当前路由不再包含在标签页列表中，跳转到上一次路由对应的标签页
        if (
          !multiTags.value.some((tag) => tag.path === router.currentRoute.path)
        ) {
          // 如果标签列表为空的话则直接跳转到首页
          if (multiTags.value.length === 0) {
            router.push("/welcome");
            return;
          }
          router.push(multiTags.value[multiTags.value.length - 1].path);
        }
        break;
      case "push":
        {
          const tagVal = value as MultiTagItem;
          // 不添加到标签页
          if (tagVal?.meta?.hiddenTag) return;
          // 如果title为空拒绝添加空信息到标签页
          if (!tagVal?.meta?.tabName || tagVal?.meta?.tabName.length === 0)
            return;

          const tagPath = tagVal.path;
          // 判断tag是否已存在
          const tagHasExits = multiTags.value.some((tag) => {
            return isSameRoute(tag, tagVal);
            // return tag.path === tagPath;
          });

          // 判断tag中的query键值是否相等
          const tagQueryHasExits = multiTags.value.some((tag) => {
            return JSON.stringify(tag?.query) === JSON.stringify(tagVal?.query);
          });

          // 判断tag中的params键值是否相等
          const tagParamsHasExits = multiTags.value.some((tag) => {
            return (
              JSON.stringify(tag?.params) === JSON.stringify(tagVal?.params)
            );
          });

          // if (tagHasExits && tagQueryHasExits && tagParamsHasExits) return;
          if (tagHasExits) return;

          // 动态路由可打开的最大数量
          const dynamicLevel = tagVal?.meta?.dynamicLevel ?? -1;
          if (dynamicLevel > 0) {
            if (
              multiTags.value.filter((e) => e?.path === tagPath).length >=
              dynamicLevel
            ) {
              // 如果当前已打开的动态路由数大于dynamicLevel，替换第一个动态路由标签
              const index = multiTags.value.findIndex(
                (item) => item?.path === tagPath
              );
              index !== -1 && multiTags.value.splice(index, 1);
            }
          }

          multiTags.value.push(tagVal);

          // 添加到缓存列表
          if (
            tagVal.meta?.keepAlive &&
            tagVal.name &&
            !cachedViews.value.includes(tagVal.name)
          ) {
            cachedViews.value.push(tagVal.name);
          }
        }
        break;
      case "splice":
        if (!position) {
          const index = multiTags.value.findIndex((v) => v.path === value);
          if (index === -1) return;
          const removedTag = multiTags.value[index];
          multiTags.value.splice(index, 1);

          // 从缓存列表中移除
          if (removedTag.name) {
            const cacheIndex = cachedViews.value.indexOf(removedTag.name);
            if (cacheIndex > -1) {
              cachedViews.value.splice(cacheIndex, 1);
            }
          }
        } else {
          const removedTags = multiTags.value.splice(
            position?.startIndex || 0,
            position?.length || 1
          );

          // 从缓存列表中移除
          removedTags.forEach((tag) => {
            if (tag.name) {
              const cacheIndex = cachedViews.value.indexOf(tag.name);
              if (cacheIndex > -1) {
                cachedViews.value.splice(cacheIndex, 1);
              }
            }
          });
        }
        // 如果当前路由不再包含在标签页列表中，跳转到上一次路由对应的标签页
        if (
          !multiTags.value.some((tag) => tag.path === router.currentRoute.path)
        ) {
          // 如果标签列表为空的话则直接跳转到首页
          if (multiTags.value.length === 0) {
            router.push("/welcome");
            return;
          }
          router.push(multiTags.value[multiTags.value.length - 1].path);
        }
        return multiTags.value as T;
      case "slice":
        return multiTags.value.slice(-1) as T;
    }
  };

  /**
   * 添加标签页
   * @param route 路由信息
   */
  const addTab = (route: any) => {
    const tagItem: MultiTagItem = {
      path: route.path,
      fullPath: route.fullPath,
      name: route.name as string,
      meta: {
        title: route.meta?.title || "未命名页面",
        tabName: route.meta?.tabName || route.meta?.title || "未命名页面",
        icon: route.meta?.icon,
        hiddenTag: route.meta?.hiddenTag,
        showLink: route.meta?.showLink,
        dynamicLevel: route.meta?.dynamicLevel,
        affix: route.meta?.affix,
        keepAlive: route.meta?.keepAlive,
      },
      params: route.params,
      query: route.query,
    };

    handleTags("push", tagItem);
  };

  /**
   * 移除标签页
   * @param path 标签路径
   */
  const removeTab = (path: string) => {
    handleTags("splice", path);
  };

  /**
   * 关闭其他标签页
   * @param currentTag 当前标签
   */
  const closeOtherTabs = (currentTag: MultiTagItem) => {
    const affixTags = multiTags.value.filter((tag) => tag.meta.affix);
    handleTags("equal", [...affixTags, currentTag]);
  };

  /**
   * 关闭所有标签页
   */
  const closeAllTabs = () => {
    const affixTags = multiTags.value.filter((tag) => tag.meta?.affix);
    handleTags("equal", affixTags);
  };

  /**
   * 关闭左侧标签页
   * @param currentTag 当前标签
   */
  const closeLeftTabs = (currentTag: MultiTagItem) => {
    const currentIndex = multiTags.value.findIndex(
      (tag) => tag.path === currentTag.path
    );
    if (currentIndex > 0) {
      const leftTags = multiTags.value.slice(0, currentIndex);
      const affixTags = leftTags.filter((tag) => tag.meta?.affix);
      const rightTags = multiTags.value.slice(currentIndex);
      handleTags("equal", [...affixTags, ...rightTags]);
    }
  };

  /**
   * 关闭右侧标签页
   * @param currentTag 当前标签
   */
  const closeRightTabs = (currentTag: MultiTagItem) => {
    const currentIndex = multiTags.value.findIndex(
      (tag) => tag.path === currentTag.path
    );
    if (currentIndex < multiTags.value.length - 1) {
      const leftTags = multiTags.value.slice(0, currentIndex + 1);
      const rightTags = multiTags.value.slice(currentIndex + 1);
      const affixTags = rightTags.filter((tag) => tag.meta?.affix);
      handleTags("equal", [...leftTags, ...affixTags]);
    }
  };

  /**
   * 刷新当前页面
   */
  const refreshCurrentPage = () => {
    const { fullPath, query } = router.currentRoute;
    router.replace({
      path: "/redirect" + fullPath,
      query,
    });
  };

  /**
   * 显示右键菜单
   * @param tag 标签页
   * @param position 菜单位置
   */
  const showContextMenu = (
    tag: MultiTagItem,
    position: { x: number; y: number }
  ) => {
    currentContextTag.value = tag;
    contextMenuPosition.value = position;
    contextMenuVisible.value = true;
  };

  /**
   * 隐藏右键菜单
   */
  const hideContextMenu = () => {
    contextMenuVisible.value = false;
    currentContextTag.value = null;
  };

  /**
   * 获取右键菜单项
   */
  const getContextMenuItems = computed((): ContextMenuItem[] => {
    const currentTag = currentContextTag.value;
    if (!currentTag) return [];

    const currentIndex = multiTags.value.findIndex(
      (tag) => tag.path === currentTag.path
    );
    const isFirst = currentIndex === 0;
    const isLast = currentIndex === multiTags.value.length - 1;
    const hasMultipleTabs = multiTags.value.length > 1;
    const isAffix = currentTag.meta.affix;
    const isCurrentActive = isTabActive(currentTag, router.currentRoute);

    return [
      {
        key: "refresh",
        label: "重新加载",
        icon: "el-icon-refresh-right",
        disabled: !isCurrentActive,
      },
      {
        key: "close",
        label: "关闭当前标签页",
        icon: "el-icon-close",
        disabled: isAffix || !hasMultipleTabs,
        divided: true,
      },
      {
        key: "closeLeft",
        label: "关闭左侧标签页",
        icon: "el-icon-back",
        disabled: isFirst || !hasMultipleTabs,
      },
      {
        key: "closeRight",
        label: "关闭右侧标签页",
        icon: "el-icon-right",
        disabled: isLast || !hasMultipleTabs,
      },
      {
        key: "closeOther",
        label: "关闭其他标签页",
        icon: "el-icon-remove",
        disabled: !hasMultipleTabs,
        divided: true,
      },
      {
        key: "closeAll",
        label: "关闭全部标签页",
        icon: "el-icon-minus",
        disabled: multiTags.value.filter((tag) => !tag.meta.affix).length === 0,
      },
    ];
  });

  /**
   * 处理右键菜单点击
   * @param key 菜单项key
   */
  const handleContextMenuClick = (key: string) => {
    const currentTag = currentContextTag.value;
    if (!currentTag) return;

    switch (key) {
      case "refresh":
        refreshCurrentPage();
        break;
      case "close":
        removeTab(currentTag.path);
        break;
      case "closeLeft":
        closeLeftTabs(currentTag);
        break;
      case "closeRight":
        closeRightTabs(currentTag);
        break;
      case "closeOther":
        closeOtherTabs(currentTag);
        break;
      case "closeAll":
        closeAllTabs();
        break;
    }

    hideContextMenu();
  };

  /**
   * 检查标签页是否激活
   * @param tag 标签页
   * @param currentRoute 当前路由
   */
  const isTabActive = (tag: MultiTagItem, currentRoute: any) => {
    if (Object.keys(currentRoute.query || {}).length > 0) {
      return (
        JSON.stringify(currentRoute.query) === JSON.stringify(tag.query) &&
        tag.path === currentRoute.path
      );
    } else if (Object.keys(currentRoute.params || {}).length > 0) {
      return (
        JSON.stringify(currentRoute.params) === JSON.stringify(tag.params) &&
        tag.path === currentRoute.path
      );
    } else {
      return tag.path === currentRoute.path;
    }
  };

  /**
   * 生成标签页唯一标识
   * @param routeInfo 路由信息
   */
  const generateTabKey = (routeInfo: any) => {
    const queryStr =
      Object.keys(routeInfo.query || {}).length > 0
        ? JSON.stringify(routeInfo.query)
        : "";
    const paramsStr =
      Object.keys(routeInfo.params || {}).length > 0
        ? JSON.stringify(routeInfo.params)
        : "";
    return `${routeInfo.path}${queryStr}${paramsStr}`;
  };

  /**
   * 智能添加标签页（防止重复）
   * @param route 路由信息
   */
  const smartAddTab = (route: any, extMeta: any = {}) => {
    // 检查是否需要添加到标签页（排除不需要历史记录的页面）
    if (route.meta?.history === false) return;

    // const currentTabKey = generateTabKey(route);
    // 检查是否已经存在该标签页
    const existingTabIndex = multiTags.value.findIndex((tag) => {
      // const tagKey = generateTabKey(tag);
      // return tagKey === currentTabKey;
      return isSameRoute(tag, route);
    });

    if (existingTabIndex === -1) {
      // 新路由，添加新标签页
      // 合并一下 meta信息
      route.meta = {
        ...route.meta,
        ...extMeta,
      };
      addTab(route);
    } else {
      // 已存在的标签页，更新其信息（可能 meta 信息有变化）
      const existingTab = multiTags.value[existingTabIndex];
      if (
        route.meta &&
        JSON.stringify(existingTab.meta) !== JSON.stringify(route.meta)
      ) {
        multiTags.value[existingTabIndex] = {
          ...existingTab,
          meta: {
            ...existingTab.meta,
            ...route.meta,
            title: route.meta.title || existingTab.meta.title,
            ...extMeta,
          },
        };
      }
    }
  };

  return {
    multiTags,
    multiTagsCache,
    cachedViews,
    contextMenuVisible,
    contextMenuPosition,
    currentContextTag,
    handleTags,
    addTab,
    removeTab,
    closeOtherTabs,
    closeAllTabs,
    closeLeftTabs,
    closeRightTabs,
    refreshCurrentPage,
    showContextMenu,
    hideContextMenu,
    getContextMenuItems,
    handleContextMenuClick,
    isTabActive,
    generateTabKey,
    smartAddTab,
  };
});

export function useTabsStoreHook() {
  return useTabsStore(store);
}
