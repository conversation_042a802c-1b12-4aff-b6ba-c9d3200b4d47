// // 兼容域名处理, 用于解决不同环境下的资源路径问题
// if (window.location.pathname.startsWith('/tgxy/')) {
//     // https://psp.singlewindow.hn.cn/tgxy/
//     __webpack_public_path__ = '/tgxy/';
// } else {
//     __webpack_public_path__ = '/';
// }

import "babel-polyfill";
import "core-js/actual";

import Vue from "vue";
import App from "./App.vue";

import ElementUI from "element-ui";
import "element-ui/lib/theme-chalk/index.css";
import router from "@/router/index";
import "./directives";

import i18n from "@/lang/index";
import { createPinia, PiniaVuePlugin } from "pinia";
// @ts-ignore
import vue2WaterMarker from "vue2-water-marker";
// 全局注册less
import "@/styles/index.css";
// 本地SVG图标
import "nprogress/nprogress.css";
Vue.use(PiniaVuePlugin);
const pinia = createPinia();
Vue.config.productionTip = false;
Vue.use(ElementUI, {
  size: "small",
});

Vue.use(vue2WaterMarker);

new Vue({
  el: "#app",
  pinia,
  i18n,
  router,
  render: (h) => h(App),
});
