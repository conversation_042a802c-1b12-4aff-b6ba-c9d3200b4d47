import requestService from "@/services/requestService";

const baseUrl = "/creditreports";

/**
 * 获取认证码
 * @returns
 */
export const getAuthenticationCode = () => {
  return requestService(
    `${baseUrl}/creditengine2/credit-report-account-info/authentication/getAuthenticationCode`,
    {
      ak: "ak",
      sk: "sk",
    },
    {
      method: "POST",
    }
  );
};
/**
 * 检验认证码
 */
export const verifyAuthenticationCode = (authenticationCode: string) => {
  return requestService(
    `${baseUrl}/creditengine2/credit-report-account-info/authentication/verify`,
    { authenticationCode },
    { method: "POST" }
  );
};
