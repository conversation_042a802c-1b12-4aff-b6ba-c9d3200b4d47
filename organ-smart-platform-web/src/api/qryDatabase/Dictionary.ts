import requestService from '@/services/requestService';

export type DictionaryResult = {
    dictionaryLevel1: string;
    dictionaryLevel2: string;
    dictionaryLevel3: string;
    dictionaryValue: string;
};

/**
 * 查询字典表
 * @param params
 * @returns
 */

export const queryDictionary = (dictionaryLevel1: string, dictionaryLevel2?: string) => {
    return requestService<any, any>(
        '/creditdatabase/dictionary/query',
        { dictionaryLevel1, dictionaryLevel2 },
        { method: 'GET' },
    );
};

/**
 * 查询地区省份
 * @returns
 */
export const queryRegionProvinces = () => {
    return requestService<any, any>(
        '/creditdatabase/dictionary/queryRegionProvinces',
        {},
        { method: 'GET' },
    );
};

/**
 * 查询地区城市
 * @returns
 */
export const queryRegionCities = (province: string) => {
    return requestService<any, any>(
        '/creditdatabase/dictionary/queryRegionCities',
        { province },
        { method: 'GET' },
    );
};

/**
 * 查询地区区县
 * @returns
 */
export const queryRegionCounties = (city: string) => {
    return requestService<any, any>(
        '/creditdatabase/dictionary/queryRegionCounties',
        { city },
        { method: 'GET' },
    );
};

/**
 * 查询地区树
 * @returns
 */
export const queryRegionTree = () => {
    return requestService<any, any>(
        '/creditdatabase/dictionary/queryRegionTree',
        {},
        { method: 'GET' },
    );
};

/**
 * 查询字典树
 */
export const queryDictionaryTree = (dictionaryLevel1: string) => {
    return requestService<any, any>(
        `/creditdatabase/dictionary/queryDictionaryTree?dictionaryLevel1=${dictionaryLevel1}`,
        {},
        { method: 'GET' },
    );
};

// 获取过滤字典数据
export const getFilterDict = () => {
    return requestService<any, any>(
        '/dyck.creditapplication/data-invoke-log/getFilterDict',
        { t: new Date().getTime() },
        { method: 'GET' },
    );
};
