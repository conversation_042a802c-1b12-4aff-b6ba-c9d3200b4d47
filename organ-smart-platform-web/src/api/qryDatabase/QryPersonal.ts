import requestService from '@/services/requestService';

export interface queryListReqVO {
    pageNo: number; // 页码
    pageSize: number; // 每页条数
    // ageMax: number; // 年龄范围查询最大年龄
    // ageMin: number; // 年龄范围查询最小年龄
    profession?: string; // 职业
    lastYearDishonesty?: string; // 最近一年是否存在失信信息
    location?: string; // 所在行政区域
    importantSmugglingUser?: string; // 是否是反走私重点关注名单
    thisYearFreeTaxAmountMax?: number; // 当年免税购买金额查询最大金额
    thisYearFreeTaxAmountMin?: number | string; // 当年免税购买金额查询起始金额
    thisYearFreeTaxCountMax?: number | string; // 当年免税购买次数最大次数
    thisYearFreeTaxCountMin?: number; // 当年免税购买次数范围查询最小次数
    searchKeyword: string; // 关键字
    industryAppraisalResult?: string; // 行业信用评价结果
    customsAppraisalResult?: string; // 通关信用评价结果
    jinYeFenLevel?: string; // 金椰分等级
}

const preStr = '/dyck.creditapplication/person-details';

export const queryList = (params: queryListReqVO) => {
    return requestService<queryListReqVO, any>(`${preStr}/page`, params, {
        method: 'GET',
    });
};
/**
 * 获取自然人通关信用评价结果
 * @param cardNo
 * @returns
 */
export const getPersonPassResult = (cardNo: string) => {
    return requestService<any, any>(
        `/dyck.creditapplication/person-details/getEvaluation?cardNo=${cardNo}`,
        {},
        {
            method: 'GET',
        },
    );
};

export const getDetail = (params: any) => {
    return requestService<any, any>(`${preStr}/get`, params, {
        method: 'GET',
    });
};

export const getPersonDetailsByCryptCard = (data: any) => {
    return requestService<any, any>(`${preStr}/getPersonDetailsByCryptCard`, data, {
        method: 'POST',
    });
};
