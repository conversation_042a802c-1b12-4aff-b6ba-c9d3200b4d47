import requestService from '@/services/requestService';

export const getEnterpriseQRCode = (data: any) => {
    return requestService<any, any>(`/creditdatabase/qrccode/getEnterpriseQRCode`, data, {
        method: 'POST',
    });
};

export const getCodeInfo = (data: any) => {
    return requestService(`/creditdatabase/qrccode/getCodeInfo`, data, { method: 'POST' });
};

export const scanQRCode = (data: any) => {
    return requestService(`/creditdatabase/qrccode/scanQRCode`, data, { method: 'POST' });
};
