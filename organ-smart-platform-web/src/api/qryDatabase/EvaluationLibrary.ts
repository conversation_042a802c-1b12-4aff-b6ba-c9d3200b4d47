import requestService from '@/services/requestService';

export interface queryListReqVO {
    pageNo: number; // 页码
    pageSize: number; // 每页条数
    evaluationType: string; // 评价类型
    evaluationField: string; // 评价领域
    evaluationUnit: string; // 评价单位
    evaluationCycle: string; // 评价周期
    evaluationStartTime: string; // 评价开始时间
    evaluationEndTime: string; // 评价结束时间
    hasPolicySupport: string; // 是否有政策支持
    evaluationName: string; // 搜索框文字
}

const preStr = '/creditdatabase/evaluation/';

/**
 * 获取所有评价领域类型
 * @returns
 */
export const getAllEvaluationField = () => {
    return requestService<any, any>(
        `${preStr}getAllEvaluationField`,
        {
            t: new Date().getTime(),
        },
        {
            method: 'GET',
        },
    );
};

/**
 *
 * @returns 获取所有评价单位
 */
export const getAllEvaluationUnit = () => {
    return requestService<any, any>(
        `${preStr}getAllEvaluationUnit`,
        {
            t: new Date().getTime(),
        },
        {
            method: 'GET',
        },
    );
};

/**
 *
 * @returns 获取所有评价周期
 */
export const getAllEvaluationCycle = () => {
    return requestService<any, any>(
        `${preStr}getAllEvaluationCycle`,
        {
            t: new Date().getTime(),
        },
        {
            method: 'GET',
        },
    );
};

export const queryList = (params: queryListReqVO) => {
    return requestService<queryListReqVO, any>('/creditdatabase/evaluation/query', params, {
        method: 'GET',
    });
};

export const getCompanyList = (params: queryListReqVO) => {
    return requestService<queryListReqVO, any>(
        '/creditdatabase/evaluation/getCompanyList',
        params,
        {
            method: 'GET',
        },
    );
};

export const getDetail = (params: { id: string }) => {
    return requestService<{ id: string }, any>('/creditdatabase/evaluation/get', params, {
        method: 'GET',
    });
};

export const getInduEvaluationDictionary = (params: {
    evaluationName?: string | undefined;
}): Promise<
    [
        {
            name: string;
            value: string[];
        },
    ]
> => {
    return requestService<{ evaluationName?: string | undefined }, any>(
        '/creditdatabase/evaluation/getInduEvaluationDictionary',
        params,
        {
            method: 'GET',
        },
    );
};

export const getEntSearchEvaluationCondition = (params: {
    evaluationName?: string | undefined;
}): Promise<
    [
        {
            name: string;
            value: string[];
        },
    ]
> => {
    return requestService<{ evaluationName?: string | undefined }, any>(
        '/creditdatabase/evaluation/getEntSearchEvaluationCondition',
        params,
        {
            method: 'GET',
        },
    );
};
