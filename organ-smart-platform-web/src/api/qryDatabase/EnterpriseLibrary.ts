import requestFileService from '@/services/requestFileService';
import requestService from '@/services/requestService';

export interface queryListReqVO {
    pageNo: number; // 页码
    pageSize: number; // 每页条数
    entname: string; // 企业名称
    tgpjjg?: string; // 企业通关信用评价结果
    otherIndustyEvaluation?: string; // 其他行业评价结果（评价领域、评价结果）
    qygm?: any; // 企业规模
    industryCategory?: any; // 企业类型（所属行业）
    isSxIn1years?: string; // 近一年内是否存在失信信息
    pjGgxy?: string; // 海南省企业法人公共信用综合评价结果[国家信用中心监测处]信息
    isAeo?: string; // 是否是AEO
    isXhzt?: string; // 是否享惠主体
    xhlx?: string; // 享惠类型
    region?: string; // 所在行政区划
    // regorg?: string; // 所在行政区划
    isTrade?: string; // 有无实际进出口业务企业
    uniscType?: string; // 机构类型
}

export interface queryCurrency {
    pageNo: number; // 页码
    pageSize: number; // 每页条数
    uniscid: any; //信用代码
}

/**
 * 根据查询条件获得企业基本信息分页
 * @returns
 */
export const queryList = (params: queryListReqVO) => {
    // return requestService<queryListReqVO, any>('/creditdatabase/company-basic/search', params, {
    return requestService<queryListReqVO, any>(
        '/creditdatabase/dwdEntpCrdtEvalPort/search',
        params,
        {
            method: 'POST',
        },
    );
};

/**
 * 获取企业基本信息
 * @returns
 */
export const getByUniscidCompanyBasic = (params: { uniscid: any }) => {
    return requestService<{ uniscid: String }, any>(
        '/creditdatabase/company-basic/getByUniscid',
        params,
        {
            method: 'GET',
        },
    );
};

/**
 * 获得经营信息-税务信息
 * @returns
 */
export const getDetermineData = (params: { uniscid: any }) => {
    return requestService<{ uniscid: any }, any>(
        '/creditdatabase/company-basic/abnormal-house/determine-data',
        params,
        {
            method: 'GET',
        },
    );
};

/**
 * 获得经营信息-企业纳税等级
 * @returns
 */
export const getTaxRatingList = (params: queryCurrency) => {
    return requestService<queryCurrency, any>('/creditdatabase/tax-rating/page', params, {
        method: 'GET',
    });
};

/**
 * 获得经营信息-行政许可证
 * @returns
 */
export const getAdministrativeLicensePage = (params: queryCurrency) => {
    return requestService<queryCurrency, any>(
        '/creditdatabase/administrative-license/page',
        params,
        {
            method: 'GET',
        },
    );
};

/**
 * 获得经营风险-欠税信息
 * @returns
 */
export const getOwingTaxInfo = (params: queryCurrency) => {
    return requestService<queryCurrency, any>('/creditdatabase/owing-tax-info/page', params, {
        method: 'GET',
    });
};

/**
 * 获得经营风险-行政处罚分页
 * @returns
 */

export const getAdministrativePenalty = (params: queryCurrency) => {
    return requestService<queryCurrency, any>(
        '/creditdatabase/administrative-penalty/page',
        params,
        {
            method: 'GET',
        },
    );
};

/**
 * 获得经营风险-企业用电情况
 * @returns
 */
export const getEnterpriseElectricity = (params: queryCurrency) => {
    return requestService<queryCurrency, any>(
        '/creditdatabase/enterprise-electricity/page',
        params,
        {
            method: 'GET',
        },
    );
};

/**
 * 获取失信信息
 * @returns
 */
export const dishonestyInformationPage = (params: {
    uniscid: any;
    pageNo: number;
    pageSize: number;
}) => {
    return requestService<any, any>(
        '/creditdatabase/dishonesty-information/get',
        {
            ...params,
            pageNum: params.pageNo,
        },
        {
            method: 'GET',
        },
    );
};

/**
 * 失信信息-获得黑名单信息
 * @returns
 */
export const blacklistInformationPage = (params: queryCurrency) => {
    return requestService<queryCurrency, any>(
        '/creditdatabase/blacklist-information/page',
        params,
        {
            method: 'GET',
        },
    );
};

/**
 * 失信信息-重大税收违法失信主体
 * @returns
 */
export const getMajorTaxViolationsPage = (params: queryCurrency) => {
    return requestService<queryCurrency, any>('/creditdatabase/major-tax-violations/page', params, {
        method: 'GET',
    });
};

/**
 * 失信信息-税务行政强制信息
 * @returns
 */
export const getAdministrativeCompulsion = (params: queryCurrency) => {
    return requestService<queryCurrency, any>(
        '/creditdatabase/administrative-compulsion/getByUniscid',
        params,
        {
            method: 'GET',
        },
    );
};

/**
 * 失信信息-社会保险领域重大失信主体
 * @returns
 */
export const majorTaxCreditPage = (params: queryCurrency) => {
    // return requestService<queryCurrency, any>('/creditdatabase/major-tax-credit/page', params, {
    return requestService<queryCurrency, any>(
        '/creditdatabase/social-insurance-dishonesty/page',
        params,
        {
            method: 'GET',
        },
    );
};

/**
 * 司法信息-获得裁判文书分页
 * @returns
 */
export const judgmentDocPage = (params: queryCurrency) => {
    return requestService<queryCurrency, any>('/creditdatabase/judgment-doc/page', params, {
        method: 'GET',
    });
};

/**
 * 获得知识产权-获得专利信息
 * @returns
 */
export const patentInfoPage = (params: queryCurrency) => {
    return requestService<queryCurrency, any>('/creditdatabase/patent-info/page', params, {
        method: 'GET',
    });
};

/**
 * 获得知识产权-商标信息列表
 * @returns
 */
export const getTrademarkInfoList = (params: queryCurrency) => {
    return requestService<queryCurrency, any>('/creditdatabase/trademark-info/page', params, {
        method: 'GET',
    });
};

/**
 * 获得行业评价数据列表
 * @returns
 */
export const getIndustryEvaluationList = (params: { uniscid: any }) => {
    return requestService<{ uniscid: any }, any>(
        '/creditdatabase/indu-eval-data/getByUniscId',
        params,
        {
            method: 'GET',
        },
    );
};

export const getCreditImageByUniscId = (params: { uniscid: any }) => {
    return requestService<{ uniscid: any }, any>(
        '/checkcreditimage/Check-Credit-Image/getByUniscId',
        params,
        {
            method: 'GET',
        },
    );
};

export const getAllEvaluationResult = (params: { evaluationName: any }) => {
    return requestService<{ evaluationName: any }, any>(
        '/checkcreditimage/Check-Credit-Image/getAllEvaluationResult',
        params,
        {
            method: 'GET',
        },
    );
};
export const getCreditReportFile = (uniscid: string) => {
    return requestFileService<any, any>(
        `/creditdatabase/reportinformation/getPdf?uniscid=${uniscid}`,
        {},
        {
            method: 'GET',
        },
    );
};

export const searchResultPjTgxxRate = (params: any) => {
    // return requestService<queryListReqVO, any>('/creditdatabase/company-basic/search', params, {
    return requestService<any, any>(
        '/creditdatabase/dwdEntpCrdtEvalPort/searchResultPjTgxxRate',
        params,
        {
            method: 'GET',
        },
    );
};

/**
 * 异议申诉
 */
export const objectionAppealsCreate = (params: {
    contactInfo: string;
    contactName: string;
    creditCode: string;
    enterpriseName: string;
    objectionDescription: string;
    objectionType?: string;
}) => {
    return requestService<any, any>('/creditapplication/objection-appeals/create', params, {
        method: 'POST',
    });
};

/**
 * 获得企业信用评价结果
 * @returns
 */
type AccordingTo = Array<{
    /** 评价依据 */
    title: string;
    /** 等级标签 */
    levels: string[];
    /** 当前等级 */
    level?: string;
    /** 评价时间 时间戳 */
    time?: number;
    /** 评价单位 */
    unit: string;
    /** 右侧标签 */
    tag?: string;
    /** 评价标签选中颜色类型 默认为high */
    status?: 'high' | 'low';
    /** 标签状态颜色 */
    tagStatus?: 'high' | 'low';
}>;
export const getEvaluationResultByCompany = (params: { uniscid: string }) => {
    return requestService<any, AccordingTo>(
        '/creditdatabase/evaluation/dictionary/getEvaluationResultByCompany',
        params,
        {
            method: 'GET',
        },
    );
};

/** 根据统一信用代码，获得海关-行政处罚 */
export const getManualHgXzcfByUniscid = (params: {
    uniscid: string;
    pageNo: number;
    pageSize: number;
}) => {
    return requestService<any, any>(
        '/creditdatabase/manual-hg-xzcf/getByUniscid',
        {
            ...params,
            pageNum: params.pageNo,
        },
        {
            method: 'GET',
        },
    );
};

/** 我的信用-根据统一社会信用代码获得行业评价数据 */
export const getMyCreditByUniscid = (params: { uniscid: string }) => {
    return requestService<any, any>(
        '/creditdatabase/indu-eval-data/myCredit/getByUniscId',
        params,
        {
            method: 'GET',
        },
    );
};
