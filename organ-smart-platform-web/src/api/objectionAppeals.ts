import requestService from '@/services/requestService';

const moduleUrl = '/creditapplication/objection-appeals/';

/** 获取异议处理列表 */
export function getObjectionAppealsPage(params: {
    pageNo: number;
    pageSize: number;
    creditCode?: string;
    enterpriseName?: string;
}) {
    return requestService(`${moduleUrl}page`, params, {
        method: 'GET',
    });
}

/** 更新异议处理 */
export function auditObjectionAppeal(params: {
    auditOpinion: string;
    auditStatus: string;
    id: number | string;
}) {
    return requestService(`${moduleUrl}audit`, params, {
        method: 'POST',
    });
}

/**
 * ObjectionAppealsRespVO
 */
export interface ObjectionAppealsRespVO {
    /**
     * 审核意见
     */
    auditOpinion?: string;
    /**
     * 审核状态
     */
    auditStatus?: string;
    /**
     * 审核时间
     */
    auditTime?: string;
    /**
     * 联系方式
     */
    contactInfo: string;
    /**
     * 联系人姓名
     */
    contactName: string;
    /**
     * 提交时间
     */
    createTime?: string;
    /**
     * 统一社会信用代码
     */
    creditCode: string;
    /**
     * 数据源单位
     */
    dataUnit?: string;
    /**
     * 异议数据
     */
    disputedData: string;
    /**
     * 企业名称
     */
    enterpriseName: string;
    /**
     * 自增主键，唯一标识每条记录
     */
    id: number;
    /**
     * 异议申诉的具体描述
     */
    objectionDescription: string;
    /**
     * 异议申诉的类型
     */
    objectionType?: string;
}

/**
 * 获取异议申诉信息
 * @returns
 */
export const getObjectionAppeals = (params: { id: string }) => {
    return requestService<any, ObjectionAppealsRespVO>(
        '/creditapplication/objection-appeals/get',
        params,
        {
            method: 'GET',
        },
    );
};
