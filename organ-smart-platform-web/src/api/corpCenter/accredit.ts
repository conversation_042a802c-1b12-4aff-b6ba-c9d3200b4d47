import requestService from '@/services/requestService';

/**授权管理-我的申请记录 */
export const getMyApplyList = (params: any) => {
    return requestService<any, any>(
        '/creditapplication/authorization-records/getMyApplyList',
        params,
        {
            method: 'GET',
        },
    );
};

/**授权管理-我的授权记录 */
export const getMyAuthList = (params: any) => {
    return requestService<any, any>(
        '/creditapplication/authorization-records/getMyAuthList',
        params,
        {
            method: 'GET',
        },
    );
};

/**授权管理-获取待审核数量 */
export const getReviewCount = (params: any) => {
    return requestService<any, any>(
        '/creditapplication/authorization-records/getReviewCount',
        params,
        {
            method: 'GET',
        },
    );
};

/**授权管理-取消授权 */
export const cancelAuth = (params: { id: string; myEntUniscid: string }) => {
    return requestService<any, any>(`/creditapplication/authorization-records/cancelAuth`, params, {
        method: 'GET',
    });
};
