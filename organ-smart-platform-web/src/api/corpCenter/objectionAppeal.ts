import requestService from '@/services/requestService';

/**
 *  创建异议申诉信息 TS 入参类型
 */
export interface ObjectionAppealsCreateDTO {
    /**
     * 联系方式
     */
    contactInfo: string;
    /**
     * 联系人姓名
     */
    contactName: string;
    /**
     * 提交时间
     */
    createTime?: string;
    /**
     * 统一社会信用代码
     */
    creditCode: string;
    /**
     * 数据源单位
     */
    dataUnit?: string;
    /**
     * 异议数据
     */
    disputedData?: string;
    /**
     * 企业名称
     */
    enterpriseName: string;
    /**
     * 异议申诉的具体描述
     */
    objectionDescription: string;
    /**
     * 异议申诉的类型
     */
    objectionType?: string;
}

/**
 * 创建异议申诉信息
 * @returns
 */
export const createObjectionAppeals = (params: ObjectionAppealsCreateDTO) => {
    return requestService<ObjectionAppealsCreateDTO, any>(
        '/creditapplication/objection-appeals/create',
        params,
        {
            method: 'POST',
        },
    );
};

/**
 * ObjectionAppealsRespVO
 */
export interface ObjectionAppealsRespVO {
    /**
     * 审核意见
     */
    auditOpinion?: string;
    /**
     * 审核状态
     */
    auditStatus?: string;
    /**
     * 审核时间
     */
    auditTime?: string;
    /**
     * 联系方式
     */
    contactInfo: string;
    /**
     * 联系人姓名
     */
    contactName: string;
    /**
     * 提交时间
     */
    createTime?: string;
    /**
     * 统一社会信用代码
     */
    creditCode: string;
    /**
     * 数据源单位
     */
    dataUnit?: string;
    /**
     * 异议数据
     */
    disputedData: string;
    /**
     * 企业名称
     */
    enterpriseName: string;
    /**
     * 自增主键，唯一标识每条记录
     */
    id: number;
    /**
     * 异议申诉的具体描述
     */
    objectionDescription: string;
    /**
     * 异议申诉的类型
     */
    objectionType?: string;
}

/**
 * 获取异议申诉信息
 * @returns
 */
export const getObjectionAppeals = (params: { id: string }) => {
    return requestService<any, ObjectionAppealsRespVO>(
        '/creditapplication/objection-appeals/get',
        params,
        {
            method: 'GET',
        },
    );
};

/**
 * 异议申诉信息提交，发送验证码
 * @returns
 */
export const sendPhoneCode = (params: { phone: string | number }) => {
    return requestService<any, string>(
        '/creditapplication/objection-appeals/sendCode?phone=' + params.phone,
        params,
        {
            method: 'POST',
        },
    );
};

/**
 * 校验短信验证码
 * @returns
 */
export const verifyPhoneCode = (params: {
    phone: string | number;
    token: string;
    code: string;
}) => {
    return requestService<any, string>(
        `/creditapplication/objection-appeals/verifyCode?token=${params.token}&phone=${params.phone}&code=${params.code}`,
        params,
        {
            method: 'POST',
        },
    );
};
