import requestService from '@/services/requestService';

/** 接口前缀 */
const API_PREFIX = '/creditdatabase/inspection-record';

/**
 * 通关信用查验信息VO
 */
export interface InspectionRecordVO {
    /** 报关单号*/
    declarationNo?: string;
    /** 离境口岸*/
    departurePort?: string;
    /** 商品名称*/
    goodsName?: string;
    /** 查验日期 */
    inspectionDate?: string;
    /** 查验结果 */
    inspectionResult?: string;
    /** 监管方式 */
    supervisionMode?: string;
}
/**
 * 获得信用通关查验信息分页
 * @returns
 */
export const getCheckInformationList = (params: any) => {
    return requestService<any, { list: InspectionRecordVO[]; total: number }>(
        `${API_PREFIX}/page`,
        params,
        {
            method: 'GET',
        },
    );
};

/**
 * 获得信用通关查验统计信息
 */
export interface InspectionStatisticsVO {
    /** 查验次数 */
    inspectionCount: number;
    /** 查验率 */
    inspectionRate: number;
    /** 查验通过率 */
    passRate: number;
    /** 报关总量 */
    totalDeclarations: number;
}

/**
 * 获得信用通关查验信息分页
 * @returns
 */
export const getCheckInformationStatistics = (params: any) => {
    return requestService<any, InspectionStatisticsVO>(`${API_PREFIX}/statistics`, params, {
        method: 'GET',
    });
};
