import requestService from '@/services/requestService';

/**
 * 企业基本信息接口 返回
 * PageResultDwdEntpCrdtEvalPortRespVO
 */
export interface PageResultDwdEntpCrdtEvalPortRespVO {
    /**
     * 数据
     */
    list: DwdEntpCrdtEvalPortRespVO[];
    /**
     * 总量
     */
    total: number;
}

/**
 * 企业基本信息 Response VO
 *
 * DwdEntpCrdtEvalPortRespVO
 */
export interface DwdEntpCrdtEvalPortRespVO {
    /**
     * 企业名称
     */
    entname?: string;
    /**
     * 企业类型（所属行业）
     */
    IndustryCategory?: string;
    /**
     * 是否AEO
     */
    isAeo?: string;
    /**
     * 近一年内是否存在失信信息
     */
    isSxIn1years?: string;
    /**
     * 是否享惠主体
     */
    isXhzt?: string;
    /**
     * 法人代表
     */
    LeRep?: string;
    /**
     * 海南电网电力信用等级评价信息
     */
    pjDlxy?: string;
    /**
     * 海南省粮食和物资储备局法人行业信用评价信息
     */
    pjDlysZlkhKy?: string;
    /**
     * 道路运输市场从业企业信用评价（质量信誉考核）信息
     */
    pjDlysZlkhQy?: string;
    /**
     * 海南省儋州市环保信用评价（信用主体）信息
     */
    pjDzHbxy?: string;
    /**
     * 海南省儋州市企业信用评价表信息
     */
    pjDzQyxy?: string;
    /**
     * 房地产领域信用评价结果（法人）信息
     */
    pjFdcly?: string;
    /**
     * 海南省公安厅法人行业信用评价信息
     */
    pjGatHyxy?: string;
    /**
     * 各部门法人信用评价信息
     */
    pjGbmxy?: string;
    /**
     * 海南省企业法人公共信用综合评价结果[国家信用中心监测处]信息
     */
    pjGgxy?: string;
    /**
     * 海南省公共信用综合评价信息
     */
    pjGgxyzh?: string;
    /**
     * 海南省住房公积金行业信用评价（法人及其他组织）信息
     */
    pjGjjHyxy?: string;
    /**
     * 公路建设市场从业企业信用评价结果信息
     */
    pjGljs?: string;
    /**
     * 海南省环保行业信用评价信息
     */
    pjHbHyxy?: string;
    /**
     * 海南省环保信用评价信息
     */
    pjHbxy?: string;
    /**
     * 海南省环保信用评价情况信息
     */
    pjHbxyqk?: string;
    /**
     * 海南省海口海关海关高级认证企业信息
     */
    pjHgGj?: string;
    /**
     * 海南省环境信用管理企业环境信用评价信息
     */
    pjHjxygl?: string;
    /**
     * 海南省环境信用管理其他信用评价法人信息
     */
    pjHjxyglQt?: string;
    /**
     * 海南省环保行业信用评价信息
     */
    pjHjyxly?: string;
    /**
     * 海南省法人海域使用论证信用评价结果信息
     */
    pjHysy?: string;
    /**
     * 海南省法人行业信用评价信息
     */
    pjHyxy?: string;
    /**
     * 海南省行业信用评价(法人)信息
     */
    pjHyxyFr?: string;
    /**
     * 行业信用评价法人信息
     */
    pjHyxyFrxx?: string;
    /**
     * 海南省法人建设工程领域信用评价结果信息
     */
    pjJsgc?: string;
    /**
     * 海南省交通运输厅法人行业信用评价信息
     */
    pjJttHyxy?: string;
    /**
     * 交通运输市场信用监管交通运输领域信用评价结果_法人信息
     */
    pjJtysly?: string;
    /**
     * 海南省法人家政领域信用评价结果信息
     */
    pjJzly?: string;
    /**
     * 海南省道路运输客运企业质量信誉考核结果信息
     */
    pjLcjHyxy?: string;
    /**
     * 海南省法人纳税信用评价信息
     */
    pjNsxy?: string;
    /**
     * 海南省法人其他信用评价信息
     */
    pjQtxy?: string;
    /**
     * 海南省人力资源服务机构信用评价信息
     */
    pjRlzy?: string;
    /**
     * 海南省人社厅其他信用评价法人信息
     */
    pjRstQtxy?: string;
    /**
     * 海南省市场监督管理局企业信用风险分类监管评价
     */
    pjSjhnxyfx?: string;
    /**
     * 海南省涉税专业服务机构信用积分及信用等级信息
     */
    pjSsjgdj?: string;
    /**
     * 海南省水土保持领域信用评价结果信息
     */
    pjStbcly?: string;
    /**
     * 水务建设从业单位信用评价信息
     */
    pjSwjs?: string;
    /**
     * 海南省税务局信用等级评定信息
     */
    pjSwjXydj?: string;
    /**
     * 海南省商务厅法人行业信用评价信息
     */
    pjSwtHyxy?: string;
    /**
     * 水运工程建设市场从业企业信用评价结果信息
     */
    pjSygc?: string;
    /**
     * 水运工程建设企业信用等级信息
     */
    pjSygcdj?: string;
    /**
     * 水运工程建设企业当前信用等级信息
     */
    pjSygcdqdj?: string;
    /**
     * 三医联动药监机构信用评价信息
     */
    pjSyldyj?: string;
    /**
     * 省邮政管理局法人行业信用评价
     */
    pjSyzhyxyfr?: string;
    /**
     * 企业通关信用评价结果
     */
    pjTgxy?: string;
    /**
     * 海南省统计诚信管理名单信息
     */
    pjTjjCxgl?: string;
    /**
     * 海南省统计信用评价结果信息
     */
    pjTjjHyxy?: string;
    /**
     * 海南省卫生健康委员会法人行业信用评价信息
     */
    pjWjwHyxy?: string;
    /**
     * 海南省医疗保障局法人行业信用评价信息
     */
    pjYbjHyxy?: string;
    /**
     * 海南省医疗保障局法人其他信用评价信息
     */
    pjYbjQtxy?: string;
    /**
     * 海南省应急管理厅法人行业信用评价信息
     */
    pjYjglHyxy?: string;
    /**
     * 海南药监行业信用评价法人信息
     */
    pjYjHyxy?: string;
    /**
     * 海南省自然资源和规划厅法人海域使用论证信用评价结果信息
     */
    pjZgtHysy?: string;
    /**
     * 海南省自然资源和规划厅法人行业信用评价信息
     */
    pjZgtHyxy?: string;
    /**
     * 海南省资源规划企业阶段评价信息
     */
    pjZgtJd?: string;
    /**
     * 海南省自然资源和规划厅法人其他信用评价信息
     */
    pjZgtQtxy?: string;
    /**
     * 海南省法人专利代理行业信用评价信息
     */
    pjZldlHyxy?: string;
    /**
     * 企业规模
     */
    qygm?: string;
    /**
     * 所在行政区划
     */
    region?: string;
    /**
     * 企业统一社会信用代码
     */
    uniscid?: string;
    /**
     * 享惠类型
     */
    xhlx?: string;
    /**
     * 授权状态
     */
    authStatus?: string;
    /**
     * 授权ID
     * */
    authId?: string;
}

/**
 * 根据查询条件获得企业基本信息分页
 * @returns
 */
/* export const searchEnterprises = (params: {
    pageNo: number;
    pageSize: number;
    entname: string;
}) => {
    // return requestService<queryListReqVO, any>('/creditdatabase/company-basic/search', params, {
    return requestService<any, PageResultDwdEntpCrdtEvalPortRespVO>(
        '/creditdatabase/dwdEntpCrdtEvalPort/newSearch',
        params,
        {
            method: 'POST',
        },
    );
}; */

/**
 * 根据查询条件获得企业基本信息分页
 * @returns
 */
export const searchEnterprises = (params: {
    pageNo: number;
    pageSize: number;
    searchKey: string;
    uniscid: string;
}) => {
    // return requestService<queryListReqVO, any>('/creditdatabase/company-basic/search', params, {
    return requestService<any, PageResultDwdEntpCrdtEvalPortRespVO>(
        '/creditdatabase/dwdEntpCrdtEvalPort/ent/search',
        params,
        {
            method: 'POST',
        },
    );
};

/**
 * 创建企业授权记录
 */
export const authorizationRecordsCreate = (params: {
    /**发起企业统代 */
    initiatorUniscid: string;
    /**发起企业名称 */
    initiatorName: string;
    /**申请原因 */
    initiatorReason: string;
    /**同意授权企业查看你的企业档案 */
    agreeAuthorView: boolean;
    /**授权企业统代 */
    authEntUniscid: string;
    /**授权企业名称 */
    authEntName?: string;
}) => {
    return requestService<any, any>('/creditapplication/authorization-records/create', params, {
        method: 'POST',
    });
};

/**企业授权审批 */
export const authorizationRecordsApproval = (params: {
    /**授权ID */
    id: number;
    /**审批意见 */
    approvalDesc: string;
    /**审批状态 */
    authStatus: string;
}) => {
    return requestService<any, any>('/creditapplication/authorization-records/approval', params, {
        method: 'POST',
    });
};

/**获得企业授权记录 */

export const getAuthorizationRecords = (params: any) => {
    return requestService<any, any>('/creditapplication/authorization-records/get', params, {
        method: 'GET',
    });
};
