import Vue from "vue";
import VueI18n from "vue-i18n";

Vue.use(VueI18n); // 全局挂载

// 本地语言包
import enLocale from "./package/en";
import zhCnLocale from "./package/zh-cn";

// const appStore = useAppStore();

const messages = {
  "zh-cn": {
    ...zhCnLocale,
  },
  en: {
    ...enLocale,
  },
};

const i18n = new VueI18n({
  // locale: appStore.language,
  locale: "zh-cn",
  messages: messages,
});

export default i18n;
