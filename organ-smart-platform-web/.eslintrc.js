module.exports = {
  root: true,
  extends: ["./.eslintrc-auto-import.json"],
  parser: "babel-eslint",
  parserOptions: {
    sourceType: "module",
    ecmaVersion: 2020,
  },
  globals: {
    process: "readonly",
    wx: "readonly",
    useVModel: true,
  },
  env: {
    jest: true,
    es6: true,
    commonjs: true,
    browser: true,
  },
  extends: ["plugin:prettier/recommended"],
  plugins: ["promise", "prettier"],
  rules: {
    "prettier/prettier": "error",
    "no-console": process.env.NODE_ENV === "production" ? "warn" : "off",
    "no-debugger": process.env.NODE_ENV === "production" ? "error" : "off",
  },
  overrides: [
    {
      files: ["*.js", "*.jsx"],
      parser: "@babel/eslint-parser",
    },
    {
      files: ["*.vue"],
      parser: "vue-eslint-parser",
      parserOptions: {
        parser: "@typescript-eslint/parser",
      },
      rules: {
        "vue/multi-word-component-names": "off",
        "vue/no-v-model-argument": "off",
      },
    },
    {
      files: ["*.ts", "*.tsx"],
      parser: "@typescript-eslint/parser",
    },
  ],
};
