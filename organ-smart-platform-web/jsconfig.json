{
  "compilerOptions": {
    "target": "es5",
    "module": "esnext",
    "baseUrl": "./",
    "moduleResolution": "node",
    "paths": {
      "@/*": [
        "src/*"
      ]
    },
    "lib": [
      "esnext",
      "dom",
      "dom.iterable",
      "scripthost"
    ]
  },
  "include": [
    "src/**/*.js",
    "src/**/*.ts",
    "src/**/*.vue",
    "src/**/*.tsx",
  ],
  "exclude": [
    "node_modules",
    "**/node_modules/*",
    "src/lib/*"
  ]
}