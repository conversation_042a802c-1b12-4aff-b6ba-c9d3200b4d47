# projectName、k8sNamespace、k8sPullSecret、imageBuildName 为自动替换变量
# 配置路径为deploy/config.sh

apiVersion: apps/v1
kind: Deployment
metadata:
  name: hncrc-dyck-credit-platform-c-web-d
  namespace: k8sNamespace
spec:
  replicas: 1
  selector:
    matchLabels:
      app: hncrc-dyck-credit-platform-c-web-d
  template:
    metadata:
      labels:
        app: hncrc-dyck-credit-platform-c-web-d
    spec:
      imagePullSecrets:
      - name: k8sPullSecret
      containers:
      - name: hncrc-dyck-credit-platform-c-web-d
        image: imageBuildName
        ports:
        - containerPort: 80
