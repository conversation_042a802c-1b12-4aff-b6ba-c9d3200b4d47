# projectName、k8sNamespace、k8sPullSecret、imageBuildName 为自动替换变量
# 配置路径为deploy/config.sh

apiVersion: apps/v1
kind: Deployment
metadata:
    name: projectName
    namespace: k8sNamespace
spec:
    replicas: 1
    selector:
        matchLabels:
            app: projectName
    template:
        metadata:
            labels:
                app: projectName
        spec:
            imagePullSecrets:
              - name: k8sPullSecret
            containers:
              - name: projectName
                image: imageBuildName
                ports:
                  - containerPort: 80
