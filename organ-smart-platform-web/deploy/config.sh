buildTime=$(date "+%Y%m%d%H%M%S")
# 工程名
projectName="hncrc-dyck-credit-platform-c-web"

# 镜像名
sitIName="myharbor.ffcs.cn/hncrc-dyck/${projectName}:${buildTime}-${deployEnv}"
preIName="myharbor.ffcs.cn/hncrc-dyck/${projectName}:${buildTime}-${deployEnv}"
prodIName="myharbor.ffcs.cn/hncrc-dyck/${projectName}:${buildTime}-${deployEnv}"
pdIName="myharbor.ffcs.cn/hncrc-dyck/${projectName}:${buildTime}-${deployEnv}"

# k8s configPath
sitK8sConfigPath="/opt/.kube/config"
preK8sConfigPath="/opt/.kube/config"
prodK8sConfigPath="/opt/prd/.kube/config"

# k8s镜像拉取secret
sitPullSecret="docker-pull-secret"
prePullSecret="docker-pull-secret"
prodPullSecret="docker-pull-secret"

# k8s命名空间名
sitK8sNs="test"
preK8sNs="dev"
prodK8sNs="prod"

# k8s部署yaml变量替换key
targetProjectNameReplace="projectName"
targetImageReplace="imageBuildName"
targetNsReplace="k8sNamespace"
targetPullSecretReplace="k8sPullSecret"
