#!/bin/bash

# Variables
iName=""
k8sConfigPath=""
pullSecret=""
k8sNs=""
DOCKER_LOGIN_NAME=${DOCKER_LOGIN_NAME}
DOCKER_LOGIN_PASSWORD=${DOCKER_LOGIN_PASSWORD}

# Functions
yarnInstall() {
	/usr/local/nodejs/bin/yarn
}

initEnvVal() {
	case "$deployEnv" in
	"sit")
		iName=$sitIName
		k8sConfigPath=$sitK8sConfigPath
		pullSecret=$sitPullSecret
		k8sNs=$sitK8sNs
		;;
	"pre")
		iName=$preIName
		k8sConfigPath=$preK8sConfigPath
		pullSecret=$prePullSecret
		k8sNs=$preK8sNs
		;;
	"pd")
		iName=$pdIName
		k8sConfigPath=$prodK8sConfigPath
		pullSecret=$prodPullSecret
		k8sNs=$prodK8sNs
		;;
	*)
		iName=$prodIName
		k8sConfigPath=$prodK8sConfigPath
		pullSecret=$prodPullSecret
		k8sNs=$prodK8sNs
		;;
	esac
}

genK8sDeployYaml() {
	local deployYamlPath="$WORKSPACE/dockerBuild/deploy.yaml"
	cp "$basePath/deploy/deployYaml/$deployEnv.yaml" "$WORKSPACE/dockerBuild/"
	mv "$WORKSPACE/dockerBuild/$deployEnv.yaml" "$deployYamlPath"
	sed -i "s!$targetProjectNameReplace!$projectName!g" "$deployYamlPath"
	sed -i "s!$targetImageReplace!$iName!g" "$deployYamlPath"
	sed -i "s!$targetNsReplace!$k8sNs!g" "$deployYamlPath"
	sed -i "s!$targetPullSecretReplace!$pullSecret!g" "$deployYamlPath"
}

buildImage() {
	local tempFileName="$projectName-$buildTime"
	docker -v
	docker build --network=host -t "$iName" "$WORKSPACE/dockerBuild/" --build-arg DEPLOY_ENV="$deployEnv"
	docker save -o "$WORKSPACE/$tempFileName.tar" "$iName"
	echo "Preparing to deploy with the following files:"
	echo "Image tarball: $WORKSPACE/$tempFileName.tar"
	echo "K8s deployment YAML: $WORKSPACE/dockerBuild/deploy.yaml"
	echo "Temporary file name: $tempFileName.tar"
	echo "Image name: $iName"
	echo "Deployment YAML name: deploy.yaml"
	sh deploy/fetch.sh "$WORKSPACE/$tempFileName.tar" "$WORKSPACE/dockerBuild/deploy.yaml" "$tempFileName.tar" "$iName" "deploy.yaml"
	rm -rf "$WORKSPACE/$tempFileName.tar"
}

doK8sDeploy() {
	kubectl --kubeconfig "$k8sConfigPath" apply -f "$WORKSPACE/dockerBuild/deploy.yaml"
}

initPublishWorkspace() {
	rm -rf "$WORKSPACE/dockerBuild"
	mkdir "$WORKSPACE/dockerBuild"
}
