set -e
# 部署环境 从脚本外部传递
deployEnv=$DEPLOY_ENV
deployPackage=packages/c-web
basePath=$WORKSPACE/$deployPackage
source $basePath/deploy/config.sh
source $basePath/deploy/utils.sh

# 初始化环境变量
initEnvVal
initPublishWorkspace

echo "-----------------构建项目名: $projectName -----------------"
echo "-----------------构建时间: $buildTime -----------------"
echo "-----------------构建环境: $deployEnv -----------------"
echo "-----------------构建镜像名: $iName -----------------"
echo "-----------------工作目录: $basePath -----------------"
echo "-----------------k8s命名空间: $k8sNs -----------------"
echo "-----------------部署配置文件路径: $k8sConfigPath -----------------"
echo "-----------------镜像拉取密钥key: $pullSercret -----------------"

build() {
	rsync -r --exclude=dockerBuild/* ./ $WORKSPACE/dockerBuild/
	cp $basePath/deploy/nginx-$deployEnv.conf $WORKSPACE/dockerBuild/nginx.conf
	cp $basePath/deploy/Dockerfile $WORKSPACE/dockerBuild
	genK8sDeployYaml

	buildImage

	# doK8sDeploy

}

build
echo "------------------- ${projectName} 构建完成 -------------------"