FROM node:18.19.0-buster-slim as build

ARG DEPLOY_ENV
ENV DEPLOY_ENV=${DEPLOY_ENV}

WORKDIR /app
COPY .eslintrc.js ./
COPY .prettierrc.js ./
COPY postcss.config.cjs ./
COPY tsconfig.json ./
COPY packages ./packages
COPY package.json ./
COPY yarn.lock ./
RUN yarn install -W --prefer-offline --frozen-lockfile --network-concurrency 8

WORKDIR /app/packages/c-web
RUN npm run build:${DEPLOY_ENV}

FROM harbor.ffcs.cn/hn-credit/nginx:v1.24.0
COPY --from=build /app/packages/c-web/dist /usr/share/nginx/html/
COPY --from=build /app/packages/c-web/dist /usr/share/nginx/html/tgxy

COPY nginx.conf /etc/nginx/conf.d/default.conf
CMD ["nginx", "-g", "daemon off;"]
