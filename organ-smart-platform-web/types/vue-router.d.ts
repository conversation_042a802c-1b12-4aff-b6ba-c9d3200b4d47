export {};

declare module 'vue-router' {
    interface RouteMeta {
        /** 标题 如果要在menu里显示则必写 */
        title?: string;
        /** 是否在 layout 菜单中显示 仅在静态路由初始化时生效 */
        layout?: boolean;
        /** 在菜单中的位置顺序 */
        index?: number;
        /** 是否在菜单中显示 */
        menu?: boolean;
        /** 是否在菜单中平铺 */
        flat?: boolean;
        /** 是否显示在历史记录中 */
        history?: boolean;
        /** 是否填充 整个layout */
        fill?: boolean;
        /** 是否缓存 */
        keepAlive?: boolean;
        /** 激活菜单 一般是 跳转详情页使用 */
        activeMenu?: string;
        /** 是否是一个动态路由 (由接口请求过来的路由) */
        dynamic?: boolean;
    }
}
