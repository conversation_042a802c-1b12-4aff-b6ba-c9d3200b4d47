server {
        listen 80;
        server_name localhost;
        access_log /var/log/nginx/host.access.log main;
        client_max_body_size 100m;
        client_header_timeout 1m;
        client_body_timeout 1m;
        proxy_connect_timeout 60s;
        proxy_read_timeout 1m;
        proxy_send_timeout 1m;

        proxy_buffer_size 4k;
        proxy_buffers 4 32k;
        proxy_busy_buffers_size 64k;
        proxy_temp_file_write_size 64k;

        # gzip 模块设置
        gzip on; #开启 gzip 压缩输出
        gzip_min_length 1k; #最小压缩文件大小
        gzip_buffers 4 16k; #压缩缓冲区
        gzip_http_version 1.0; #压缩版本（默认 1.1，前端如果是 squid2.5 请使用 1.0）
        gzip_comp_level 2; #压缩等级，gzip 压缩比，1 为最小，处理最快；9 为压缩比最大，处理最慢，传输速度最快，也最消耗 CPU；
        gzip_types text/plain application/javascript application/x-javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png font/ttf font/otf image/svg+xml;
        #压缩类型，默认就已经包含 text/html，所以下面就不用再写了，写上去也不会有问题，但是会有一个 warn。
        gzip_vary on; # 根据客户端的HTTP头来判断，是否需要压缩


        error_log /var/log/nginx/error.log error;

        # proxy_hide_header Access-Control-Allow-Origin;
        # add_header Access-Control-Allow-Origin *;
        # add_header Access-Control-Allow-Headers *;

        location /micro {
                alias /usr/share/nginx/html/micro;
                index index.html index.htm;
                try_files $uri $uri/ /micro/index.html;
        }

        location / {
                root /usr/share/nginx/html;
                index index.html index.htm;
                try_files $uri $uri/ /index.html;
        }


        error_page 500 502 503 504 /50x.html;

        location = /50x.html {
                root /usr/share/nginx/html;
        }

        location ^~/api/sysuser/ {
                if ($request_method = OPTIONS) {
                        return 204;
                }

                proxy_pass http://ctyun-gateway.ctyun-register.svc.cluster.local:80/sysuser/; # note the trailing slash!
        }

        location ^~/api/ {
                if ($request_method = OPTIONS) {
                        return 204;
                }

                proxy_pass http://ctyun-gateway.ctyun-register.svc.cluster.local:80/; # note the trailing slash!
        }


}