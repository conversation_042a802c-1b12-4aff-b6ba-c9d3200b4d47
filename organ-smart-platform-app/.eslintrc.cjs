module.exports = {
  root: true,
  env: {
    browser: true,
    es2022: true,
    node: true,
  },
  extends: [
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:vue/recommended',
  ],
  parser: 'vue-eslint-parser',
  plugins: [
    '@typescript-eslint',
    'vue',
  ],
  parserOptions: {
    ecmaVersion: 2022,
    sourceType: 'module',
    parser: '@typescript-eslint/parser',
    extraFileExtensions: ['.vue'],
  },
  rules: {
    // Vue 相关规则
    'vue/multi-word-component-names': 'off',
    'vue/no-v-html': 'off',
    'vue/require-default-prop': 'off',
    'vue/require-explicit-emits': 'off',
    'vue/component-tags-order': ['error', {
      order: ['script', 'template', 'style'],
    }],
    'vue/html-self-closing': ['error', {
      html: {
        void: 'always',
        normal: 'always',
        component: 'always',
      },
      svg: 'always',
      math: 'always',
    }],
    'vue/max-attributes-per-line': ['error', {
      singleline: 3,
      multiline: 1,
    }],
    
    // TypeScript 相关规则
    '@typescript-eslint/no-explicit-any': 'off',
    '@typescript-eslint/no-unused-vars': ['error', {
      argsIgnorePattern: '^_',
      varsIgnorePattern: '^_',
    }],
    '@typescript-eslint/ban-ts-comment': 'off',
    '@typescript-eslint/prefer-ts-expect-error': 'off',
    '@typescript-eslint/ban-types': 'off', // 允许使用 {} 类型
    
    // 通用规则
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'prefer-const': 'error',
    'no-var': 'error',
    
    // 导入相关规则 - 暂时禁用，需要 eslint-plugin-import
    // 'import/order': ['error', {
    //   groups: [
    //     'builtin',
    //     'external',
    //     'internal',
    //     'parent',
    //     'sibling',
    //     'index',
    //   ],
    //   'newlines-between': 'never',
    //   alphabetize: {
    //     order: 'asc',
    //     caseInsensitive: true,
    //   },
    // }],
    
    // 关闭一些过于严格的规则
    'antfu/if-newline': 'off',
    'antfu/top-level-function': 'off',
    'unicorn/prefer-number-properties': 'off',
    'unicorn/filename-case': 'off',
    'unicorn/prevent-abbreviations': 'off',
  },
  overrides: [
    {
      // 配置文件特殊规则
      files: [
        '*.config.{js,ts}',
        'webpack.config.js',
        'babel.config.js',
        'postcss.config.ts',
        '.eslintrc.cjs',
      ],
      parserOptions: {
        ecmaVersion: 2022,
        sourceType: 'module',
      },
      rules: {
        'no-console': 'off',
        '@typescript-eslint/no-var-requires': 'off',
        'import/no-default-export': 'off',
        'import/no-named-as-default-member': 'off',
      },
    },
  ],
  ignorePatterns: [
    'dist/**',
    'node_modules/**',
    '*.d.ts',
    '*.map',
    'public/**',
    'deploy/**',
    '.eslintrc-auto-import.json',
  ],
}
