import path from "node:path";
import { fileURLToPath } from "node:url";
import dotenv from "dotenv";
import HtmlWebpackPlugin from "html-webpack-plugin";
import AutoImport from "unplugin-auto-import/webpack";
import { VueLoaderPlugin } from "vue-loader";
import webpack from "webpack";

const __dirname = path.dirname(fileURLToPath(import.meta.url));

export default (env, argv) => {
  const mode = argv.mode || "development";
  const isProduction = mode === "production";

  // 根据 mode 动态加载对应的环境变量文件
  const envFile = `.env.${mode}`;
  const envConfig = dotenv.config({ path: envFile }).parsed || {};

  // 处理环境变量，只包含 VUE_APP_ 开头的变量和 NODE_ENV
  const envVars = {};
  Object.keys(envConfig).forEach((key) => {
    if (key.startsWith("VUE_APP_") || key === "NODE_ENV") {
      envVars[`process.env.${key}`] = JSON.stringify(envConfig[key]);
    }
  });

  // 确保 NODE_ENV 始终与 mode 保持一致
  envVars["process.env.NODE_ENV"] = JSON.stringify(mode);

  return {
    entry: "./src/main.ts",
    output: {
      path: path.resolve(__dirname, "dist"),
      filename: isProduction ? "[name].[contenthash].js" : "[name].js",
      clean: true,
      publicPath: "/",
    },
    resolve: {
      extensions: [".ts", ".js", ".vue", ".json"],
      alias: {
        "@": path.resolve(__dirname, "src"),
        // 确保所有Vue引用都指向同一个ESM版本
        vue$: "vue/dist/vue.esm.js",
        vue: "vue/dist/vue.esm.js",
      },
    },
    module: {
      rules: [
        {
          test: /\.vue$/,
          loader: "vue-loader",
        },
        {
          test: /\.(ts|js)$/,
          use: [
            {
              loader: "babel-loader",
              options: {
                cacheDirectory: true, // 启用缓存提高构建速度
              },
            },
            {
              loader: "ts-loader",
              options: {
                appendTsSuffixTo: [/\.vue$/],
                transpileOnly: true, // 只转译，不进行类型检查（类型检查交给 babel）
              },
            },
          ],
          exclude: /node_modules/,
        },
        {
          test: /\.css$/,
          use: ["style-loader", "css-loader"],
        },
        {
          test: /\.s[ac]ss$/i,
          use: ["style-loader", "css-loader", "sass-loader"],
        },
        {
          test: /\.(png|jpe?g|gif|svg)$/i,
          type: "asset/resource",
        },
      ],
    },
    plugins: [
      new VueLoaderPlugin(),
      AutoImport({
        imports: [
          "vue",
          {
            "@/router/hook": ["useRoute", "useRouter"],
          },
        ],
        dts: "./src/types/auto-imports.d.ts", // 生成类型声明文件到 src/types 目录
        eslintrc: {
          enabled: true, // 生成 ESLint 配置
          filepath: "./.eslintrc-auto-import.json",
        },
      }),
      new HtmlWebpackPlugin({
        template: "./public/index.html",
        title: "HNCRC Job Report Web",
      }),
      new webpack.DefinePlugin({
        ...envVars,
      }),
    ],
    devServer: {
      static: {
        directory: path.join(__dirname, "public"),
      },
      compress: true,
      hot: true,
      historyApiFallback: true,
      open: false,
      port: 9102,
      client: {
        overlay: {
          runtimeErrors: false,
        },
      },
      proxy: {
        "/admin-api": {
          target: envConfig.VUE_APP_BASE_API,
          changeOrigin: true,
          secure: false,
          logLevel: "debug",
        },
      },
    },
    devtool: isProduction ? false : "eval-source-map",
  };
};
