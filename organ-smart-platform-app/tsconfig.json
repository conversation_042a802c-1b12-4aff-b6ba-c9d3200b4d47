{"compilerOptions": {"target": "ES2018", "jsx": "preserve", "lib": ["DOM", "DOM.Iterable", "ES6"], "baseUrl": ".", "module": "esnext", "moduleResolution": "node", "paths": {"@/*": ["src/*"]}, "resolveJsonModule": true, "types": ["node", "vue/types/vue", "webpack-env"], "allowJs": true, "strict": false, "noFallthroughCasesInSwitch": true, "noImplicitAny": false, "noImplicitThis": false, "declaration": false, "declarationMap": false, "noEmit": false, "sourceMap": true, "inlineSourceMap": false, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "skipLibCheck": true}, "include": ["src/**/*", "types/**/*"], "exclude": ["node_modules", "dist"]}