# hncrc-job-report-web 智慧食堂前端

一个基于 Vue 2.7 + TypeScript + Vue Router 的现代化工作报告管理系统。

## 技术栈

- **前端框架**: Vue.js 2.7
- **编程语言**: TypeScript
- **路由管理**: Vue Router 3.x
- **构建工具**: Webpack 5
- **代码规范**: ESLint + TypeScript ESLint
- **样式**: 原生 CSS (支持 Scoped CSS)

## 功能特性

- 📊 工作报告管理 - 创建、编辑、查看工作报告
- 🔍 智能搜索 - 支持关键词搜索和状态筛选
- 📱 响应式设计 - 适配桌面端和移动端
- 🎨 现代化 UI - 简洁美观的用户界面
- 🔒 类型安全 - 完整的 TypeScript 类型定义
- 🚀 性能优化 - 路由懒加载和代码分割

## 项目结构

```
hncrc-job-report-web/
├── public/                 # 静态资源
│   └── index.html         # HTML 模板
├── src/                   # 源代码
│   ├── api/              # API 接口
│   ├── assets/           # 静态资源
│   ├── components/       # 公共组件
│   ├── router/           # 路由配置
│   ├── store/            # 状态管理
│   ├── types/            # TypeScript 类型定义
│   ├── utils/            # 工具函数
│   ├── views/            # 页面组件
│   ├── App.vue           # 根组件
│   ├── main.ts           # 应用入口
│   └── shims-vue.d.ts    # Vue 类型声明
├── .eslintrc.js          # ESLint 配置
├── .gitignore            # Git 忽略文件
├── package.json          # 项目配置
├── tsconfig.json         # TypeScript 配置
└── webpack.config.js     # Webpack 配置
```

## 开发指南

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0 或 yarn >= 1.22.0

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

访问 http://localhost:8080 查看应用。

### 构建生产版本

```bash
npm run build
```

构建文件将输出到 `dist/` 目录。

### 代码检查

```bash
npm run lint
```

### 类型检查

```bash
npm run type-check
```

## 部署说明

### 生产环境部署

1. 构建项目

```bash
npm run build
```

2. 将 `dist/` 目录部署到 Web 服务器

3. 配置服务器支持 History 模式路由

### Nginx 配置示例

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }
}
```
