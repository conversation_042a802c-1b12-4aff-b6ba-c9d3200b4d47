// 项目特定的类型定义
// 导入环境变量类型定义
import './env'

declare global {
  /**
   * 指定的字段变为必填，其他字段保持不变。
   * @example
   * type User = { id: number; name?: string; age?: number };
   * type UserWithRequiredName = RequiredSomeFields<User, 'name'>; // { id: number; name: string; age?: number }
   */
  export type RequiredSomeFields<T extends object, K extends keyof T> = Omit<
    T,
    K
  > & {
    [P in K]-?: T[P];
  }

  /**
   * 分页请求入参结构
   */
  export type PaginatedRequestParams<T extends object = object> = {
    /**
     * 页码，从1开始
     */
    pageNo: number
    /**
     * 每页数量
     */
    pageSize: number
  } & T

  /**
   * 分页请求出参结构
   */
  export interface PaginatedResponseData<T = any> {
    /**
     * 总数
     */
    total: number
    /**
     * 当前页数据
     */
    list: T[]
  }

  type FixedLengthTuple<T, N extends number, R extends T[] = []>
  = R['length'] extends N ? R : FixedLengthTuple<T, N, [...R, T]>

}

export {}
