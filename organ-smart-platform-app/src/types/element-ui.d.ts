/**
 * Element UI 组件类型定义补充
 * 为 Element UI 中缺失的组件类型定义提供支持
 */

import Vue from 'vue'

/**
 * Scrollbar 组件属性接口
 */
export interface ScrollbarProps {
  /**
   * 是否显示原生滚动条
   * @default false
   */
  native?: boolean

  /**
   * 滚动条包裹的样式
   */
  wrapStyle?: string | object

  /**
   * 滚动条包裹的类名
   */
  wrapClass?: string | string[]

  /**
   * 视图的样式
   */
  viewStyle?: string | object

  /**
   * 视图的类名
   */
  viewClass?: string | string[]

  /**
   * 不响应 resize 事件，如果 container 尺寸不会发生变化，最好设置它可以优化性能
   * @default false
   */
  noresize?: boolean

  /**
   * 滚动条标签名
   * @default 'div'
   */
  tag?: string
}

/**
 * Scrollbar 组件实例方法
 */
export interface ScrollbarMethods {
  /**
   * 更新滚动条状态
   */
  update: () => void

  /**
   * 滚动到指定位置
   * @param options 滚动选项
   */
  scrollTo: (options: { left?: number, top?: number }) => void

  /**
   * 设置滚动条到顶部的距离
   * @param scrollTop 距离顶部的像素值
   */
  setScrollTop: (scrollTop: number) => void

  /**
   * 设置滚动条到左边的距离
   * @param scrollLeft 距离左边的像素值
   */
  setScrollLeft: (scrollLeft: number) => void
}

/**
 * Scrollbar 组件类型定义
 */
export declare class ElScrollbar extends Vue {
  /** Scrollbar 组件属性 */
  $props: ScrollbarProps

  /** 更新滚动条状态 */
  update(): void

  /** 滚动到指定位置 */
  scrollTo(options: { left?: number, top?: number }): void

  /** 设置滚动条到顶部的距离 */
  setScrollTop(scrollTop: number): void

  /** 设置滚动条到左边的距离 */
  setScrollLeft(scrollLeft: number): void
}

/**
 * 扩展 Element UI 模块声明
 */
declare module 'element-ui' {
  export const Scrollbar: typeof ElScrollbar
}

/**
 * 扩展 Vue 组件选项
 */
declare module 'vue/types/vue' {
  interface Vue {
    $scrollbar?: ElScrollbar
  }
}

export default ElScrollbar
