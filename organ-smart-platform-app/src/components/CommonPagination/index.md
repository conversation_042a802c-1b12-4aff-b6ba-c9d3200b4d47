# CommonPagination 通用分页组件

基于 Element UI 分页组件封装的通用分页组件，提供统一的 API 和样式。

## 何时使用

- 需要对大量数据进行分页显示时
- 需要用户能够切换每页显示条数时
- 需要快速跳转到指定页码时
- 需要显示数据总数信息时

## 代码演示

### 基础用法

最简单的分页组件使用方式。

```vue
<script setup lang="ts">
import { ref } from 'vue'
import CommonPagination from '@/components/CommonPagination/index.vue'

const current = ref(1)
const pageSize = ref(10)
const total = ref(100)

function handleChange(page: number, size: number) {
  current.value = page
  pageSize.value = size
  console.log('页码改变:', page, size)
}

function handleSizeChange(page: number, size: number) {
  current.value = page
  pageSize.value = size
  console.log('每页条数改变:', page, size)
}
</script>

<template>
  <CommonPagination
    :current="current"
    :page-size="pageSize"
    :total="total"
    @change="handleChange"
    @show-size-change="handleSizeChange"
  />
</template>
```

### 简洁模式

适用于移动端或空间有限的场景。

```vue
<template>
  <CommonPagination
    :current="1"
    :page-size="10"
    :total="100"
    simple
    @change="handleChange"
  />
</template>
```

### 自定义配置

可以自定义显示的功能和选项。

```vue
<template>
  <CommonPagination
    :current="1"
    :page-size="20"
    :total="500"
    :page-size-options="[10, 20, 50, 100, 200]"
    :show-size-changer="true"
    :show-quick-jumper="true"
    :show-total="true"
    @change="handleChange"
    @show-size-change="handleSizeChange"
  />
</template>
```

### 禁用状态

在加载数据时可以禁用分页组件。

```vue
<script setup lang="ts">
import { ref } from 'vue'

const loading = ref(false)

async function loadData() {
  loading.value = true
  try {
    // 模拟API请求
    await new Promise(resolve => setTimeout(resolve, 1000))
  }
  finally {
    loading.value = false
  }
}
</script>

<template>
  <CommonPagination
    :current="1"
    :page-size="10"
    :total="100"
    :disabled="loading"
    @change="handleChange"
  />
</template>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| current | 当前页码 | `number` | `1` |
| pageSize | 每页条数 | `number` | `10` |
| total | 总条数 | `number` | `0` |
| pageSizeOptions | 每页条数选项 | `number[]` | `[10, 20, 50, 100]` |
| showSizeChanger | 是否显示每页条数选择器 | `boolean` | `true` |
| showQuickJumper | 是否显示快速跳转 | `boolean` | `true` |
| showTotal | 是否显示总数信息 | `boolean` | `true` |
| simple | 是否简洁模式 | `boolean` | `false` |
| disabled | 是否禁用 | `boolean` | `false` |

### Events

| 事件名 | 说明 | 回调参数 |
| --- | --- | --- |
| change | 页码改变时触发 | `(page: number, pageSize: number)` |
| showSizeChange | 每页条数改变时触发 | `(current: number, size: number)` |

## 使用示例

### 与表格组件配合使用

```vue
<script setup lang="ts">
import { ref } from 'vue'
import CommonPagination from '@/components/CommonPagination/index.vue'
import CommonTable from '@/components/CommonTable/index.vue'

const tableData = ref([])
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
})

async function loadData() {
  try {
    const response = await api.getList({
      pageNo: pagination.value.current,
      pageSize: pagination.value.pageSize,
    })

    tableData.value = response.list
    pagination.value.total = response.total
  }
  catch (error) {
    console.error('加载数据失败:', error)
  }
}

function handlePageChange(page: number, pageSize: number) {
  pagination.value.current = page
  pagination.value.pageSize = pageSize
  loadData()
}

// 初始加载
loadData()
</script>

<template>
  <div>
    <CommonTable
      :data="tableData"
      :columns="columns"
    />

    <CommonPagination
      :current="pagination.current"
      :page-size="pagination.pageSize"
      :total="pagination.total"
      @change="handlePageChange"
      @show-size-change="handlePageChange"
    />
  </div>
</template>
```

## 注意事项

1. **页码从 1 开始** - 组件的页码计算从 1 开始，与后端接口保持一致
2. **事件处理** - `change` 和 `showSizeChange` 事件都会传递当前页码和每页条数
3. **数据同步** - 确保 `current`、`pageSize`、`total` 与实际数据状态保持同步
4. **禁用状态** - 在数据加载时建议设置 `disabled` 为 `true`

## 样式定制

组件使用 CSS 变量，可以通过覆盖变量来自定义样式：

```css
.common-pagination {
  --pagination-primary-color: #409eff;
  --pagination-border-color: #d9d9d9;
  --pagination-hover-color: #409eff;
  --pagination-disabled-color: #ccc;
}
```

## 兼容性

- Vue 3.0+
- 现代浏览器 (Chrome 60+, Firefox 55+, Safari 12+)
- IE 11+ (需要 polyfill)
