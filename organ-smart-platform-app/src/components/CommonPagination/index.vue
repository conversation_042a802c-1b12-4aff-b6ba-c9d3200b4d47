<script setup lang="ts">
import { Pagination } from 'element-ui'
import { computed } from 'vue'

/**
 * 分页组件Props
 */
interface CommonPaginationProps {
  /** 当前页码 */
  current?: number
  /** 每页条数 */
  pageSize?: number
  /** 总条数 */
  total?: number
  /** 每页条数选项 */
  pageSizeOptions?: number[]
  /** 是否显示每页条数选择器 */
  showSizeChanger?: boolean
  /** 是否显示快速跳转 */
  showQuickJumper?: boolean
  /** 是否显示总数信息 */
  showTotal?: boolean
  /** 是否禁用 */
  disabled?: boolean
  /** 分页按钮的数量，当总页数超过该值时会折叠 */
  pagerCount?: number
  /** 只有一页时是否隐藏 */
  hideOnSinglePage?: boolean
}

const props = withDefaults(defineProps<CommonPaginationProps>(), {
  current: 1,
  pageSize: 10,
  total: 0,
  pageSizeOptions: () => [10, 20, 50, 100],
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: true,
  disabled: false,
  pagerCount: 7,
  hideOnSinglePage: false,
})

/**
 * 事件定义
 */
const emit = defineEmits<{
  /** 页码改变事件 */
  (e: 'change', page: number, pageSize: number): void
  /** 每页条数改变事件 */
  (e: 'showSizeChange', current: number, size: number): void
  /** 页码或每页条数改变前的事件，可用于确认操作 */
  (e: 'beforeChange', type: 'page' | 'size', newValue: number, oldValue: number): boolean | void
}>()

// 计算分页组件的布局
const getLayout = computed(() => {
  const layouts = []

  // 使用 Element UI 自带的总数显示
  if (props.showTotal) {
    layouts.push('total')
  }

  if (props.showSizeChanger) {
    layouts.push('sizes')
  }

  layouts.push('prev', 'pager', 'next')

  if (props.showQuickJumper) {
    layouts.push('jumper')
  }
  return layouts.join(', ')
})

// 计算总页数
const totalPages = computed(() => {
  return Math.ceil(props.total / props.pageSize)
})

// 计算当前显示的数据范围
const dataRange = computed(() => {
  const start = (props.current - 1) * props.pageSize + 1
  const end = Math.min(props.current * props.pageSize, props.total)
  return { start, end }
})

/**
 * 处理页码改变
 */
function handleCurrentChange(page: number) {
  // 触发 beforeChange 事件，允许外部组件阻止变更
  const shouldContinue = emit('beforeChange', 'page', page, props.current)
  if (shouldContinue === false) {
    return
  }

  emit('change', page, props.pageSize)
}

/**
 * 处理每页条数改变
 */
function handleSizeChange(size: number) {
  // 触发 beforeChange 事件，允许外部组件阻止变更
  const shouldContinue = emit('beforeChange', 'size', size, props.pageSize)
  if (shouldContinue === false) {
    return
  }
  emit('showSizeChange', props.current, size)
}

/**
 * 跳转到第一页
 */
function goToFirstPage() {
  if (props.current !== 1 && !props.disabled) {
    handleCurrentChange(1)
  }
}

/**
 * 跳转到最后一页
 */
function goToLastPage() {
  if (props.current !== totalPages.value && !props.disabled) {
    handleCurrentChange(totalPages.value)
  }
}

/**
 * 跳转到指定页
 */
function goToPage(page: number) {
  if (page >= 1 && page <= totalPages.value && page !== props.current && !props.disabled) {
    handleCurrentChange(page)
  }
}

/**
 * 获取分页信息
 */
function getPaginationInfo() {
  return {
    current: props.current,
    pageSize: props.pageSize,
    total: props.total,
    totalPages: totalPages.value,
    dataRange: dataRange.value,
  }
}

// 暴露方法给父组件
defineExpose({
  goToFirstPage,
  goToLastPage,
  goToPage,
  getPaginationInfo,
})
</script>

<template>
  <div class="common-pagination">
    <Pagination
      background
      :current-page="current"
      :page-size="pageSize"
      :total="total"
      :page-sizes="pageSizeOptions"
      :pager-count="pagerCount"
      :disabled="disabled"
      :hide-on-single-page="hideOnSinglePage"
      :layout="getLayout"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
  </div>
</template>

<style lang="scss" scoped>
.common-pagination {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 16px 0;
}

// 自定义 Element UI 分页组件样式
:deep(.el-pagination) {
  .el-pagination__total {
    color: #666;
    font-size: 14px;
    margin-right: 16px;
  }

  .el-pagination__sizes {
    .el-select {
      .el-input {
        .el-input__inner {
          height: 32px;
          line-height: 32px;
        }
      }
    }
  }

  .el-pager {
    li {
      min-width: 32px;
      height: 32px;
      line-height: 32px;

      &.active {
        background-color: #409eff;
        color: white;
      }
    }
  }

  .btn-prev,
  .btn-next {
    height: 32px;
    line-height: 32px;
  }
}
</style>
