/**
 * 分页组件Props接口
 */
export interface CommonPaginationProps {
  /** 当前页码 */
  current?: number
  /** 每页条数 */
  pageSize?: number
  /** 总条数 */
  total?: number
  /** 每页条数选项 */
  pageSizeOptions?: number[]
  /** 是否显示每页条数选择器 */
  showSizeChanger?: boolean
  /** 是否显示快速跳转 */
  showQuickJumper?: boolean
  /** 是否显示总数信息 */
  showTotal?: boolean
  /** 是否简洁模式 */
  simple?: boolean
  /** 是否禁用 */
  disabled?: boolean
  /** 分页按钮的数量，当总页数超过该值时会折叠 */
  pagerCount?: number
  /** 只有一页时是否隐藏 */
  hideOnSinglePage?: boolean
}

/**
 * 分页组件事件接口
 */
export interface CommonPaginationEmits {
  (e: 'change', page: number, pageSize: number): void
  (e: 'showSizeChange', current: number, size: number): void
}

/**
 * 分页信息接口
 */
export interface PaginationInfo {
  /** 当前页码 */
  current: number
  /** 每页条数 */
  pageSize: number
  /** 总条数 */
  total: number
  /** 总页数 */
  totalPages: number
}
