<template>
  <div class="tabs-wrapper">
    <div
      v-for="tab in tabs"
      :key="tab.url"
      class="tab-item"
      :class="{ active: tab.isActive() }"
      @click="handleClickTab(tab)"
    >
      <img
        class="icon-img"
        :src="tab.isActive() ? tab.iconHighlight : tab.icon"
        alt=""
      />
      <!-- <i :class="tab.icon" class="icon"></i> -->
      <span class="label">{{ tab.label }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { BIZ_MODULE_ROUTE_PREFIX } from "@/views/canteen/configs/constants";
import IconHomeN from "@/assets/images/canteen/home_n.png";
import IconHomeH from "@/assets/images/canteen/home_h.png";
import IconCodeN from "@/assets/images/canteen/code_n.png";
import IconCodeH from "@/assets/images/canteen/code_h.png";
import IconPersonalN from "@/assets/images/canteen/personal_n.png";
import IconPersonalH from "@/assets/images/canteen/personal_h.png";

/** 当前路由 */
const route = useRoute();
/** 当前路由对象 */
const router = useRouter();

/** 模块路由前缀 */
const ROUTE_PREFIX = BIZ_MODULE_ROUTE_PREFIX;

/** 当前路由 */
const currentRoute = computed(() => route.path);

/** TabBar 列表 */
const tabs = computed(() => {
  return [
    {
      label: "首页",
      url: `${ROUTE_PREFIX}/client/home`,
      icon: IconHomeN,
      iconHighlight: IconHomeH,
      isActive: () => {
        return route.path.startsWith(`${ROUTE_PREFIX}/client/home`);
      },
    },
    {
      label: "就餐码",
      url: `${ROUTE_PREFIX}/client/diningCode`,
      icon: IconCodeN,
      iconHighlight: IconCodeH,
      isActive: () => {
        return route.path.startsWith(`${ROUTE_PREFIX}/client/diningCode`);
      },
    },
    {
      label: "个人中心",
      url: `${ROUTE_PREFIX}/client/personalCenter`,
      icon: IconPersonalN,
      iconHighlight: IconPersonalH,
      isActive: () => {
        return route.path.startsWith(`${ROUTE_PREFIX}/client/personalCenter`);
      },
    },
  ];
});

/** 点击对应的 Tab */
const handleClickTab = (tab: TabBarItem) => {
  if (tab.isActive()) {
    return;
  }
  router.push(`${tab.url}`);
};
</script>

<style scoped lang="scss">
.tabs-wrapper {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: var(--tabbar-height);
  background-color: #ffffff;
  border-top: 1px solid #f1f1f1;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  user-select: none;

  .tab-item {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    cursor: pointer;
    color: #1d1e20;

    &.active {
      color: var(--color-primary);
    }

    .label {
      font-size: 14px;
    }

    .icon-img {
      margin-bottom: 4px;
      width: 24px;
      height: 24px;
    }

    .icon {
      font-size: 24px;
    }
  }
}
</style>
