<script setup lang="ts">
import { ref, computed, watch, nextTick } from "vue";
import ScrollView from "../ScrollView/index.vue";
import type { ScrollViewInstance } from "../ScrollView/types";

/**
 * AutoLoad 组件 - 自动管理加载列表数据
 * 基于 ScrollView 组件实现，支持下拉刷新和上拉加载更多
 */

// 定义组件属性接口
interface Props {
  /** 是否正在加载更多 */
  loading?: boolean;
  /** 是否正在刷新 */
  refreshing?: boolean;
  /** 是否还有更多数据 */
  hasMore?: boolean;
  /** 触发加载更多的距离阈值，默认50px */
  threshold?: number;
  /** 触发下拉刷新的距离阈值，默认45px */
  refreshThreshold?: number;
  /** 是否自动加载，默认true */
  autoLoad?: boolean;
  /** 是否启用下拉刷新，默认true */
  enableRefresh?: boolean;
  /** 是否启用上拉加载更多，默认true */
  enableLoadMore?: boolean;
  /** 加载中的提示文本 */
  loadingText?: string;
  /** 刷新中的提示文本 */
  refreshingText?: string;
  /** 没有更多数据的提示文本 */
  noMoreText?: string;
  /** 加载失败的提示文本 */
  errorText?: string;
  /** 空数据的提示文本 */
  emptyText?: string;
  /** 是否显示空状态 */
  showEmpty?: boolean;
  /** 节流延迟时间，默认200ms */
  throttleDelay?: number;
  /** 下拉刷新时是否禁用触摸事件 */
  refreshingDisableTouch?: boolean;
  /** 是否显示下拉刷新文本，默认false（不显示） */
  showRefresherText?: boolean;
}

// 定义组件事件接口
interface Emits {
  /** 触发加载更多 */
  (e: "load-more"): void;
  /** 触发刷新 */
  (e: "refresh"): void;
}

// 定义加载状态类型
type LoadState =
  | "idle"
  | "loading"
  | "refreshing"
  | "error"
  | "noMore"
  | "empty";

// 定义组件实例方法接口
interface AutoLoadInstance {
  /** 手动触发加载更多 */
  loadMore: () => void;
  /** 手动触发刷新 */
  refresh: () => void;
  /** 完成加载（成功或失败） */
  finishLoad: (success?: boolean) => void;
  /** 完成刷新（成功或失败） */
  finishRefresh: (success?: boolean) => void;
  /** 重置状态 */
  reset: () => void;
  /** 滚动到顶部 */
  scrollToTop: (animated?: boolean) => void;
  /** 滚动到底部 */
  scrollToBottom: (animated?: boolean) => void;
  /** 获取滚动信息 */
  getScrollInfo: () => any;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  refreshing: false,
  hasMore: true,
  threshold: 50,
  refreshThreshold: 45,
  autoLoad: true,
  enableRefresh: true,
  enableLoadMore: true,
  loadingText: "加载中...",
  refreshingText: "刷新中...",
  noMoreText: "没有更多数据了",
  errorText: "加载失败，点击重试",
  emptyText: "暂无数据",
  showEmpty: false,
  throttleDelay: 200,
  refreshingDisableTouch: false,
  showRefresherText: false,
});

const emit = defineEmits<Emits>();

// 组件引用
const scrollViewRef = ref<ScrollViewInstance>();

// 状态管理
const loadState = ref<LoadState>("idle");
const isRefreshTriggered = ref(false);
const lastLoadTime = ref(0);

/**
 * 计算当前状态
 */
const currentState = computed(() => {
  if (props.refreshing || isRefreshTriggered.value) {
    return "refreshing";
  }
  if (props.loading) {
    return "loading";
  }
  if (loadState.value === "error") {
    return "error";
  }
  if (!props.hasMore && !props.loading && !props.refreshing) {
    return "noMore";
  }
  if (props.showEmpty) {
    return "empty";
  }
  return "idle";
});

/**
 * 是否可以加载更多
 */
const canLoadMore = computed(() => {
  return (
    props.enableLoadMore &&
    props.hasMore &&
    !props.loading &&
    !props.refreshing &&
    !isRefreshTriggered.value &&
    loadState.value !== "error"
  );
});

/**
 * 是否可以刷新
 */
const canRefresh = computed(() => {
  return (
    props.enableRefresh &&
    !props.loading &&
    !props.refreshing &&
    !isRefreshTriggered.value
  );
});

/**
 * 节流控制
 */
let loadMoreTimer: number | null = null;

/**
 * 处理滚动到底部事件
 */
function handleScrollToLower() {
  if (!canLoadMore.value || !props.autoLoad) {
    return;
  }

  // 节流控制，避免频繁触发
  if (loadMoreTimer) {
    clearTimeout(loadMoreTimer);
  }

  loadMoreTimer = window.setTimeout(() => {
    const now = Date.now();
    // 防止重复请求，至少间隔200ms
    if (now - lastLoadTime.value < props.throttleDelay) {
      return;
    }

    lastLoadTime.value = now;
    loadState.value = "loading";
    emit("load-more");
  }, 50);
}

/**
 * 处理下拉刷新事件
 */
function handleRefresh() {
  if (!canRefresh.value) {
    return;
  }

  isRefreshTriggered.value = true;
  loadState.value = "refreshing";
  emit("refresh");
}

/**
 * 手动触发加载更多
 */
function loadMore() {
  if (!canLoadMore.value) {
    return;
  }

  loadState.value = "loading";
  emit("load-more");
}

/**
 * 手动触发刷新
 */
function refresh() {
  if (!canRefresh.value) {
    return;
  }

  isRefreshTriggered.value = true;
  loadState.value = "refreshing";
  emit("refresh");
}

/**
 * 完成加载
 */
function finishLoad(success = true) {
  loadState.value = success ? "idle" : "error";

  // 如果是刷新完成，需要停止下拉刷新状态
  if (isRefreshTriggered.value) {
    nextTick(() => {
      scrollViewRef.value?.stopPullRefresh();
      isRefreshTriggered.value = false;
    });
  }
}

/**
 * 完成刷新
 */
function finishRefresh(success = true) {
  finishLoad(success);
}

/**
 * 重置状态
 */
function reset() {
  loadState.value = "idle";
  isRefreshTriggered.value = false;
  if (loadMoreTimer) {
    clearTimeout(loadMoreTimer);
    loadMoreTimer = null;
  }
}

/**
 * 滚动到顶部
 */
function scrollToTop(animated = true) {
  scrollViewRef.value?.scrollTo({ top: 0, animated });
}

/**
 * 滚动到底部
 */
function scrollToBottom(animated = true) {
  const scrollInfo = scrollViewRef.value?.getScrollInfo();
  if (scrollInfo) {
    scrollViewRef.value?.scrollTo({
      top: scrollInfo.scrollHeight - scrollInfo.clientHeight,
      animated,
    });
  }
}

/**
 * 获取滚动信息
 */
function getScrollInfo() {
  return scrollViewRef.value?.getScrollInfo();
}

/**
 * 处理错误重试
 */
function handleErrorRetry() {
  if (loadState.value === "error") {
    loadMore();
  }
}

// 监听 props 变化
watch(
  () => props.loading,
  (newVal) => {
    if (!newVal && loadState.value === "loading") {
      finishLoad(true);
    }
  }
);

watch(
  () => props.refreshing,
  (newVal) => {
    if (
      !newVal &&
      (loadState.value === "refreshing" || isRefreshTriggered.value)
    ) {
      finishRefresh(true);
    }
  }
);

// 暴露方法
defineExpose({
  loadMore,
  refresh,
  finishLoad,
  finishRefresh,
  reset,
  scrollToTop,
  scrollToBottom,
  getScrollInfo,
});
</script>

<template>
  <div class="auto-load">
    <ScrollView
      ref="scrollViewRef"
      :scroll-y="true"
      :lower-threshold="threshold"
      :refresher-enabled="enableRefresh"
      :refresher-threshold="refreshThreshold"
      :refresher-triggered="isRefreshTriggered"
      :refreshing-disable-touch="refreshingDisableTouch"
      :show-refresher-text="showRefresherText"
      @scrolltolower="handleScrollToLower"
      @refresherrefresh="handleRefresh"
    >
      <!-- 自定义下拉刷新指示器 -->
      <template #refresher>
        <div class="auto-load__refresher">
          <slot name="refresher" :state="currentState">
            <div class="auto-load__refresher-default">
              <!-- 只在刷新时显示loading，或者设置了显示文本时显示 -->
              <div
                v-if="currentState === 'refreshing' || showRefresherText"
                class="auto-load__refresher-content"
              >
                <!-- 刷新中显示loading图标 -->
                <div
                  v-if="currentState === 'refreshing'"
                  class="auto-load__refresher-loading"
                >
                  <div class="auto-load__spinner"></div>
                  <span class="auto-load__text">{{ refreshingText }}</span>
                </div>
                <!-- 非刷新状态且设置显示文本时显示下拉提示 -->
                <div
                  v-else-if="
                    showRefresherText &&
                    !props.refreshing &&
                    !isRefreshTriggered
                  "
                  class="auto-load__refresher-text"
                >
                  下拉刷新
                </div>
              </div>
            </div>
          </slot>
        </div>
      </template>

      <!-- 列表内容 -->
      <div class="auto-load__content">
        <slot />
      </div>

      <!-- 底部状态指示器 -->
      <div class="auto-load__footer">
        <slot name="footer" :state="currentState" :on-retry="handleErrorRetry">
          <!-- 加载中状态 -->
          <div v-if="currentState === 'loading'" class="auto-load__loading">
            <div class="auto-load__spinner"></div>
            <span class="auto-load__text">{{ loadingText }}</span>
          </div>

          <!-- 没有更多数据状态 -->
          <div v-else-if="currentState === 'noMore'" class="auto-load__no-more">
            <span class="auto-load__text">{{ noMoreText }}</span>
          </div>

          <!-- 错误状态 -->
          <div
            v-else-if="currentState === 'error'"
            class="auto-load__error"
            @click="handleErrorRetry"
          >
            <span class="auto-load__text">{{ errorText }}</span>
          </div>

          <!-- 空状态 -->
          <div v-else-if="currentState === 'empty'" class="auto-load__empty">
            <slot name="empty">
              <div class="auto-load__empty-default">
                <div class="auto-load__empty-icon">📄</div>
                <span class="auto-load__text">{{ emptyText }}</span>
              </div>
            </slot>
          </div>
        </slot>
      </div>
    </ScrollView>
  </div>
</template>

<style lang="scss" scoped>
.auto-load {
  height: 100%;
  width: 100%;

  &__content {
    min-height: 100%;
  }

  &__refresher {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    &-default {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #666;
      font-size: 14px;
    }

    &-content {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
    }

    &-loading {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }

    &-text {
      color: #666;
      font-size: 14px;
    }
  }

  &__footer {
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #666;
    font-size: 14px;
  }

  &__spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #e5e5e5;
    border-top: 2px solid #409eff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  &__text {
    font-size: 14px;
    color: #666;
  }

  &__no-more {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 14px;
  }

  &__error {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #f56c6c;
    font-size: 14px;
    cursor: pointer;
    padding: 8px 16px;
    border-radius: 4px;
    transition: background-color 0.3s ease;

    &:hover {
      background-color: rgba(245, 108, 108, 0.1);
    }

    &:active {
      background-color: rgba(245, 108, 108, 0.2);
    }
  }

  &__empty {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;

    &-default {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12px;
    }

    &-icon {
      font-size: 48px;
      opacity: 0.5;
    }
  }

  &__pull-text {
    color: #666;
    font-size: 14px;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
