<template>
  <div class="auto-load-example">
    <h2>AutoLoad 组件示例</h2>

    <!-- 基础用法示例 -->
    <div class="example-section">
      <h3>基础用法</h3>
      <div class="auto-load-container">
        <AutoLoad
          :loading="basicLoading"
          :refreshing="basicRefreshing"
          :has-more="basicHasMore"
          :show-empty="basicList.length === 0 && !basicLoading"
          @load-more="handleBasicLoadMore"
          @refresh="handleBasicRefresh"
        >
          <div v-for="item in basicList" :key="item.id" class="list-item">
            <div class="item-content">
              <h4>{{ item.title }}</h4>
              <p>{{ item.description }}</p>
              <small>{{ item.time }}</small>
            </div>
          </div>
        </AutoLoad>
      </div>
    </div>

    <!-- 使用 Hook 示例 -->
    <div class="example-section">
      <h3>使用 Hook 管理数据</h3>
      <div class="auto-load-container">
        <AutoLoad
          :loading="loading"
          :refreshing="refreshing"
          :has-more="hasMore"
          :show-empty="isEmpty"
          @load-more="loadMore"
          @refresh="refresh"
        >
          <div v-for="item in data" :key="item.id" class="list-item">
            <div class="item-content">
              <h4>{{ item.title }}</h4>
              <p>{{ item.description }}</p>
              <small>{{ item.time }}</small>
            </div>
          </div>

          <!-- 自定义空状态 -->
          <template #empty>
            <div class="custom-empty">
              <div class="empty-icon">📋</div>
              <h3>暂无数据</h3>
              <p>当前列表为空，请稍后再试</p>
              <button @click="refresh" class="retry-btn">重新加载</button>
            </div>
          </template>
        </AutoLoad>
      </div>
    </div>

    <!-- 自定义样式示例 -->
    <div class="example-section">
      <h3>自定义样式</h3>
      <div class="auto-load-container">
        <AutoLoad
          ref="customAutoLoadRef"
          :loading="customLoading"
          :refreshing="customRefreshing"
          :has-more="customHasMore"
          @load-more="handleCustomLoadMore"
          @refresh="handleCustomRefresh"
        >
          <div v-for="item in customList" :key="item.id" class="custom-list-item">
            <div class="item-avatar">{{ item.id }}</div>
            <div class="item-info">
              <h4>{{ item.title }}</h4>
              <p>{{ item.description }}</p>
            </div>
          </div>

          <!-- 自定义下拉刷新 -->
          <template #refresher="{ state }">
            <div class="custom-refresher">
              <div v-if="state === 'refreshing'" class="refreshing">
                <div class="spinner"></div>
                <span>正在刷新数据...</span>
              </div>
              <div v-else class="pull-hint">
                ↓ 下拉刷新数据
              </div>
            </div>
          </template>

          <!-- 自定义底部状态 -->
          <template #footer="{ state, onRetry }">
            <div class="custom-footer">
              <div v-if="state === 'loading'" class="loading-state">
                <div class="spinner"></div>
                <span>加载更多数据中...</span>
              </div>
              <div v-else-if="state === 'noMore'" class="no-more-state">
                <span>🎉 已加载全部数据</span>
              </div>
              <div v-else-if="state === 'error'" class="error-state" @click="onRetry">
                <span>❌ 加载失败，点击重试</span>
              </div>
            </div>
          </template>
        </AutoLoad>
      </div>
    </div>

    <!-- 控制按钮 -->
    <div class="controls">
      <button @click="scrollToTop">回到顶部</button>
      <button @click="manualRefresh">手动刷新</button>
      <button @click="addCustomItem">添加项目</button>
      <button @click="clearData">清空数据</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import AutoLoad, { useAutoLoad } from './index'
import type { AutoLoadInstance } from './types'

// 组件引用
const customAutoLoadRef = ref<AutoLoadInstance>()

// 基础用法数据
const basicList = ref<Array<{ id: number; title: string; description: string; time: string }>>([])
const basicLoading = ref(false)
const basicRefreshing = ref(false)
const basicHasMore = ref(true)
const basicPage = ref(1)

// 使用 Hook 管理数据
const {
  data,
  loading,
  refreshing,
  hasMore,
  isEmpty,
  loadMore,
  refresh,
  setData,
  addData,
} = useAutoLoad({
  pageSize: 10,
  loadData: async (page, pageSize) => {
    // 模拟 API 请求
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const startId = (page - 1) * pageSize + 1
    const data = Array.from({ length: pageSize }, (_, i) => ({
      id: startId + i,
      title: `Hook 管理项目 ${startId + i}`,
      description: `这是第 ${page} 页的第 ${i + 1} 个项目`,
      time: new Date().toLocaleString(),
    }))

    return {
      data,
      total: 50, // 模拟总数
    }
  },
  onError: (error) => {
    console.error('加载失败:', error)
  },
})

// 自定义样式数据
const customList = ref<Array<{ id: number; title: string; description: string }>>([])
const customLoading = ref(false)
const customRefreshing = ref(false)
const customHasMore = ref(true)
const customPage = ref(1)

/**
 * 生成模拟数据
 */
function generateMockData(page: number, pageSize: number, prefix: string) {
  const startId = (page - 1) * pageSize + 1
  return Array.from({ length: pageSize }, (_, i) => ({
    id: startId + i,
    title: `${prefix} ${startId + i}`,
    description: `这是第 ${page} 页的第 ${i + 1} 个项目，创建时间：${new Date().toLocaleString()}`,
    time: new Date().toLocaleString(),
  }))
}

/**
 * 基础用法 - 加载更多
 */
async function handleBasicLoadMore() {
  if (basicLoading.value || !basicHasMore.value) return
  
  basicLoading.value = true
  try {
    // 模拟 API 请求
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const newData = generateMockData(basicPage.value, 10, '基础项目')
    basicList.value.push(...newData)
    basicPage.value++
    
    // 模拟没有更多数据
    if (basicPage.value > 5) {
      basicHasMore.value = false
    }
  } finally {
    basicLoading.value = false
  }
}

/**
 * 基础用法 - 刷新
 */
async function handleBasicRefresh() {
  if (basicRefreshing.value) return
  
  basicRefreshing.value = true
  try {
    // 模拟 API 请求
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    const newData = generateMockData(1, 10, '基础项目')
    basicList.value = newData
    basicPage.value = 2
    basicHasMore.value = true
  } finally {
    basicRefreshing.value = false
  }
}

/**
 * 自定义样式 - 加载更多
 */
async function handleCustomLoadMore() {
  if (customLoading.value || !customHasMore.value) return
  
  customLoading.value = true
  try {
    // 模拟 API 请求
    await new Promise(resolve => setTimeout(resolve, 800))
    
    const newData = generateMockData(customPage.value, 8, '自定义项目')
    customList.value.push(...newData)
    customPage.value++
    
    // 模拟没有更多数据
    if (customPage.value > 4) {
      customHasMore.value = false
    }
  } finally {
    customLoading.value = false
  }
}

/**
 * 自定义样式 - 刷新
 */
async function handleCustomRefresh() {
  if (customRefreshing.value) return
  
  customRefreshing.value = true
  try {
    // 模拟 API 请求
    await new Promise(resolve => setTimeout(resolve, 1200))
    
    const newData = generateMockData(1, 8, '自定义项目')
    customList.value = newData
    customPage.value = 2
    customHasMore.value = true
  } finally {
    customRefreshing.value = false
  }
}

/**
 * 滚动到顶部
 */
function scrollToTop() {
  customAutoLoadRef.value?.scrollToTop()
}

/**
 * 手动刷新
 */
function manualRefresh() {
  refresh()
}

/**
 * 添加自定义项目
 */
function addCustomItem() {
  const newItem = {
    id: Date.now(),
    title: `手动添加项目 ${Date.now()}`,
    description: `这是手动添加的项目，时间：${new Date().toLocaleString()}`,
  }
  customList.value.unshift(newItem)
}

/**
 * 清空数据
 */
function clearData() {
  setData([])
}

// 初始化数据
handleBasicLoadMore()
handleCustomLoadMore()
</script>

<style lang="scss" scoped>
.auto-load-example {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;

  h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
  }

  .example-section {
    margin-bottom: 40px;

    h3 {
      margin-bottom: 15px;
      color: #666;
      font-size: 18px;
    }
  }

  .auto-load-container {
    height: 400px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
  }

  .list-item {
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
    background: #fff;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f8f9fa;
    }

    .item-content {
      h4 {
        margin: 0 0 8px 0;
        color: #333;
        font-size: 16px;
      }

      p {
        margin: 0 0 8px 0;
        color: #666;
        font-size: 14px;
        line-height: 1.4;
      }

      small {
        color: #999;
        font-size: 12px;
      }
    }
  }

  .custom-list-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
    background: #fff;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f8f9fa;
    }

    .item-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: linear-gradient(45deg, #409eff, #66b1ff);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      margin-right: 15px;
    }

    .item-info {
      flex: 1;

      h4 {
        margin: 0 0 5px 0;
        color: #333;
        font-size: 16px;
      }

      p {
        margin: 0;
        color: #666;
        font-size: 14px;
      }
    }
  }

  .custom-empty {
    text-align: center;
    padding: 60px 20px;

    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }

    h3 {
      margin: 0 0 8px 0;
      color: #333;
    }

    p {
      margin: 0 0 20px 0;
      color: #666;
    }

    .retry-btn {
      padding: 8px 16px;
      border: 1px solid #409eff;
      background: #409eff;
      color: white;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: #66b1ff;
      }
    }
  }

  .custom-refresher {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #666;

    .refreshing {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .pull-hint {
      font-size: 14px;
    }
  }

  .custom-footer {
    padding: 16px;
    text-align: center;

    .loading-state,
    .error-state {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      cursor: pointer;
    }

    .no-more-state {
      color: #999;
    }

    .error-state {
      color: #f56c6c;

      &:hover {
        background-color: rgba(245, 108, 108, 0.1);
        border-radius: 4px;
        padding: 8px;
      }
    }
  }

  .spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #e5e5e5;
    border-top: 2px solid #409eff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .controls {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 30px;
    flex-wrap: wrap;

    button {
      padding: 10px 20px;
      border: 1px solid #409eff;
      background: #409eff;
      color: white;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: #66b1ff;
        border-color: #66b1ff;
      }

      &:active {
        background: #3a8ee6;
        border-color: #3a8ee6;
      }
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
