/**
 * AutoLoad 组件相关类型定义
 */

import type { Ref } from "vue";
import type { ScrollInfo } from "../ScrollView/types";

/**
 * 加载状态
 */
export type LoadState =
  | "idle"
  | "loading"
  | "refreshing"
  | "error"
  | "noMore"
  | "empty";

/**
 * AutoLoad 组件属性
 */
export interface AutoLoadProps {
  /** 是否正在加载更多 */
  loading?: boolean;
  /** 是否正在刷新 */
  refreshing?: boolean;
  /** 是否还有更多数据 */
  hasMore?: boolean;
  /** 触发加载更多的距离阈值，默认50px */
  threshold?: number;
  /** 触发下拉刷新的距离阈值，默认45px */
  refreshThreshold?: number;
  /** 是否自动加载，默认true */
  autoLoad?: boolean;
  /** 是否启用下拉刷新，默认true */
  enableRefresh?: boolean;
  /** 是否启用上拉加载更多，默认true */
  enableLoadMore?: boolean;
  /** 加载中的提示文本 */
  loadingText?: string;
  /** 刷新中的提示文本 */
  refreshingText?: string;
  /** 没有更多数据的提示文本 */
  noMoreText?: string;
  /** 加载失败的提示文本 */
  errorText?: string;
  /** 空数据的提示文本 */
  emptyText?: string;
  /** 是否显示空状态 */
  showEmpty?: boolean;
  /** 节流延迟时间，默认200ms */
  throttleDelay?: number;
  /** 下拉刷新时是否禁用触摸事件 */
  refreshingDisableTouch?: boolean;
  /** 是否显示下拉刷新文本，默认false（不显示） */
  showRefresherText?: boolean;
}

/**
 * AutoLoad 组件事件
 */
export interface AutoLoadEmits {
  /** 触发加载更多 */
  (e: "load-more"): void;
  /** 触发刷新 */
  (e: "refresh"): void;
  /** 滚动事件 */
  (e: "scroll", scrollInfo: ScrollInfo): void;
}

/**
 * AutoLoad 组件实例方法
 */
export interface AutoLoadInstance {
  /** 手动触发加载更多 */
  loadMore: () => void;
  /** 手动触发刷新 */
  refresh: () => void;
  /** 完成加载（成功或失败） */
  finishLoad: (success?: boolean) => void;
  /** 完成刷新（成功或失败） */
  finishRefresh: (success?: boolean) => void;
  /** 重置状态 */
  reset: () => void;
  /** 滚动到顶部 */
  scrollToTop: (animated?: boolean) => void;
  /** 滚动到底部 */
  scrollToBottom: (animated?: boolean) => void;
  /** 获取滚动信息 */
  getScrollInfo: () => ScrollInfo | undefined;
}

/**
 * 分页组件加载数据函数的参数类型
 * 与全局分页请求参数类型对应，确保类型一致性
 */
export type LoadDataParams<T extends object = object> =
  PaginatedRequestParams<T>;

/**
 * 分页查询返回的数据类型
 * 与全局分页响应数据类型对应，同时保持向后兼容
 */
export interface LoadDataResult<T = any> {
  /** 当前页数据列表 - 与 PaginatedResponseData.list 对应 */
  data?: T[];
  /** 数据列表 - 与 PaginatedResponseData.list 保持一致 */
  list?: T[];
  /** 数据总数 - 与 PaginatedResponseData.total 保持一致 */
  total: number;
  /** 是否还有更多数据 - 用于判断是否显示加载更多 */
  hasMore?: boolean;
}

/**
 * 列表数据管理Hook的选项
 */
export interface UseAutoLoadOptions<T = any> {
  /** 初始数据 */
  initialData?: T[];
  /** 每页数据量 */
  pageSize?: number;
  /** 加载数据的函数 */
  loadData: (params: LoadDataParams) => Promise<LoadDataResult<T>>;
  /** 是否自动加载第一页数据 */
  immediate?: boolean;
  /** 错误处理函数 */
  onError?: (error: any) => void;
  /** 成功处理函数 */
  onSuccess?: (data: T[], isRefresh: boolean) => void;
}

/**
 * 列表数据管理Hook的返回值
 */
export interface UseAutoLoadReturn<T = any> {
  /** 列表数据 */
  data: Ref<T[]>;
  /** 是否正在加载 */
  loading: Ref<boolean>;
  /** 是否正在刷新 */
  refreshing: Ref<boolean>;
  /** 是否还有更多数据 */
  hasMore: Ref<boolean>;
  /** 当前页码 */
  currentPage: Ref<number>;
  /** 总数据量 */
  total: Ref<number>;
  /** 是否为空 */
  isEmpty: Ref<boolean>;
  /** 错误信息 */
  error: Ref<string | null>;
  /** 加载更多 */
  loadMore: () => Promise<void>;
  /** 刷新数据 */
  refresh: () => Promise<void>;
  /** 重置数据 */
  reset: () => void;
  /** 手动设置数据 */
  setData: (data: T[]) => void;
  /** 添加数据 */
  addData: (data: T | T[]) => void;
  /** 移除数据 */
  removeData: (predicate: (item: T, index: number) => boolean) => void;
  /** 更新数据 */
  updateData: (
    predicate: (item: T, index: number) => boolean,
    updater: (item: T) => T
  ) => void;
}
