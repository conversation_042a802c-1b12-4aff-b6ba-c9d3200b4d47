# AutoLoad 组件

AutoLoad 组件是基于 ScrollView 组件实现的自动加载列表组件，支持下拉刷新和上拉加载更多功能。

## 特性

- 🔄 支持下拉刷新
- 📈 支持上拉加载更多
- 🎯 自动管理加载状态
- 🚫 支持空状态显示
- ❌ 支持错误状态处理
- 🎨 支持自定义状态样式
- 🔧 完整的 TypeScript 支持
- 🪝 提供数据管理 Hook

## 基础用法

```vue
<template>
  <AutoLoad
    :loading="loading"
    :refreshing="refreshing"
    :has-more="hasMore"
    :show-empty="isEmpty"
    @load-more="handleLoadMore"
    @refresh="handleRefresh"
  >
    <div v-for="item in list" :key="item.id" class="list-item">
      {{ item.name }}
    </div>
  </AutoLoad>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import AutoLoad from '@/components/AutoLoad'

const list = ref([])
const loading = ref(false)
const refreshing = ref(false)
const hasMore = ref(true)
const isEmpty = computed(() => list.value.length === 0)

async function handleLoadMore() {
  loading.value = true
  try {
    const newData = await fetchData(currentPage.value + 1)
    list.value.push(...newData)
    if (newData.length < pageSize) {
      hasMore.value = false
    }
  } finally {
    loading.value = false
  }
}

async function handleRefresh() {
  refreshing.value = true
  try {
    const newData = await fetchData(1)
    list.value = newData
    hasMore.value = newData.length >= pageSize
  } finally {
    refreshing.value = false
  }
}
</script>
```

## 使用 Hook 简化开发

```vue
<template>
  <AutoLoad
    :loading="loading"
    :refreshing="refreshing"
    :has-more="hasMore"
    :show-empty="isEmpty"
    @load-more="loadMore"
    @refresh="refresh"
  >
    <div v-for="item in data" :key="item.id" class="list-item">
      {{ item.name }}
    </div>
  </AutoLoad>
</template>

<script setup lang="ts">
import AutoLoad, { useAutoLoad } from '@/components/AutoLoad'

// 使用 Hook 管理数据
const {
  data,
  loading,
  refreshing,
  hasMore,
  isEmpty,
  loadMore,
  refresh,
} = useAutoLoad({
  pageSize: 20,
  loadData: async (page, pageSize) => {
    const response = await api.getList({ page, pageSize })
    return {
      data: response.data,
      total: response.total,
    }
  },
  onError: (error) => {
    console.error('加载失败:', error)
  },
})
</script>
```

## 自定义状态样式

```vue
<template>
  <AutoLoad
    :loading="loading"
    :has-more="hasMore"
    @load-more="loadMore"
  >
    <!-- 列表内容 -->
    <div v-for="item in data" :key="item.id">
      {{ item.name }}
    </div>

    <!-- 自定义下拉刷新样式 -->
    <template #refresher="{ state }">
      <div class="custom-refresher">
        <div v-if="state === 'refreshing'" class="loading-spinner">
          刷新中...
        </div>
        <div v-else>
          下拉刷新数据
        </div>
      </div>
    </template>

    <!-- 自定义底部状态 -->
    <template #footer="{ state, onRetry }">
      <div class="custom-footer">
        <div v-if="state === 'loading'" class="loading">
          正在加载更多...
        </div>
        <div v-else-if="state === 'noMore'" class="no-more">
          已加载全部数据
        </div>
        <div v-else-if="state === 'error'" class="error" @click="onRetry">
          加载失败，点击重试
        </div>
      </div>
    </template>

    <!-- 自定义空状态 -->
    <template #empty>
      <div class="custom-empty">
        <img src="/empty.png" alt="暂无数据" />
        <p>暂时没有数据哦</p>
      </div>
    </template>
  </AutoLoad>
</template>
```

## 手动控制

```vue
<template>
  <AutoLoad
    ref="autoLoadRef"
    :auto-load="false"
    :loading="loading"
    :has-more="hasMore"
    @load-more="handleLoadMore"
  >
    <div v-for="item in data" :key="item.id">
      {{ item.name }}
    </div>
  </AutoLoad>

  <div class="actions">
    <button @click="manualLoadMore">手动加载更多</button>
    <button @click="manualRefresh">手动刷新</button>
    <button @click="scrollToTop">回到顶部</button>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import AutoLoad from '@/components/AutoLoad'

const autoLoadRef = ref()

function manualLoadMore() {
  autoLoadRef.value?.loadMore()
}

function manualRefresh() {
  autoLoadRef.value?.refresh()
}

function scrollToTop() {
  autoLoadRef.value?.scrollToTop()
}
</script>
```

## API

### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| loading | boolean | false | 是否正在加载更多 |
| refreshing | boolean | false | 是否正在刷新 |
| hasMore | boolean | true | 是否还有更多数据 |
| threshold | number | 50 | 触发加载更多的距离阈值 |
| refreshThreshold | number | 45 | 触发下拉刷新的距离阈值 |
| autoLoad | boolean | true | 是否自动加载 |
| enableRefresh | boolean | true | 是否启用下拉刷新 |
| enableLoadMore | boolean | true | 是否启用上拉加载更多 |
| loadingText | string | '加载中...' | 加载中的提示文本 |
| refreshingText | string | '刷新中...' | 刷新中的提示文本 |
| noMoreText | string | '没有更多数据了' | 没有更多数据的提示文本 |
| errorText | string | '加载失败，点击重试' | 加载失败的提示文本 |
| emptyText | string | '暂无数据' | 空数据的提示文本 |
| showEmpty | boolean | false | 是否显示空状态 |
| throttleDelay | number | 200 | 节流延迟时间 |

### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| load-more | 触发加载更多 | - |
| refresh | 触发刷新 | - |
| scroll | 滚动事件 | scrollInfo: ScrollInfo |

### Methods

| 方法名 | 说明 | 参数 |
|--------|------|------|
| loadMore | 手动触发加载更多 | - |
| refresh | 手动触发刷新 | - |
| finishLoad | 完成加载 | success?: boolean |
| finishRefresh | 完成刷新 | success?: boolean |
| reset | 重置状态 | - |
| scrollToTop | 滚动到顶部 | animated?: boolean |
| scrollToBottom | 滚动到底部 | animated?: boolean |
| getScrollInfo | 获取滚动信息 | - |

### Slots

| 插槽名 | 说明 | 参数 |
|--------|------|------|
| default | 默认插槽，列表内容 | - |
| refresher | 自定义下拉刷新指示器 | { state } |
| footer | 自定义底部状态 | { state, onRetry } |
| empty | 自定义空状态 | - |

## useAutoLoad Hook

### 参数

```typescript
interface UseAutoLoadOptions<T> {
  initialData?: T[]           // 初始数据
  pageSize?: number          // 每页数据量
  loadData: (page: number, pageSize: number) => Promise<{
    data: T[]
    total?: number
    hasMore?: boolean
  }>                         // 加载数据的函数
  immediate?: boolean        // 是否自动加载第一页数据
  onError?: (error: any) => void      // 错误处理函数
  onSuccess?: (data: T[], isRefresh: boolean) => void  // 成功处理函数
}
```

### 返回值

```typescript
interface UseAutoLoadReturn<T> {
  data: Ref<T[]>            // 列表数据
  loading: Ref<boolean>     // 是否正在加载
  refreshing: Ref<boolean>  // 是否正在刷新
  hasMore: Ref<boolean>     // 是否还有更多数据
  currentPage: Ref<number>  // 当前页码
  total: Ref<number>        // 总数据量
  isEmpty: Ref<boolean>     // 是否为空
  error: Ref<string | null> // 错误信息
  loadMore: () => Promise<void>     // 加载更多
  refresh: () => Promise<void>      // 刷新数据
  reset: () => void                 // 重置数据
  setData: (data: T[]) => void      // 手动设置数据
  addData: (data: T | T[]) => void  // 添加数据
  removeData: (predicate) => void   // 移除数据
  updateData: (predicate, updater) => void  // 更新数据
}
```

## 注意事项

1. 使用 Hook 时，`loadData` 函数需要返回包含 `data` 字段的对象
2. 如果 API 返回总数，建议在 `loadData` 中返回 `total` 字段以便准确判断是否还有更多数据
3. 错误状态下点击重试会自动调用 `loadMore` 方法
4. 下拉刷新完成后会自动重置到第一页
5. 建议在组件卸载时调用 `reset` 方法清理状态
