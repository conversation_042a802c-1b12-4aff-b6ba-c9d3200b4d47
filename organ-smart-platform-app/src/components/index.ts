/**
 * 全局组件注册
 */

import type Vue from "vue";

// 导入组件
import ScrollView from "./ScrollView";
import AutoLoad from "./AutoLoad";

/**
 * 全局组件列表
 */
const components = {
  ScrollView,
  AutoLoad,
};

/**
 * 注册全局组件
 * @param app Vue 实例
 */
export function registerGlobalComponents(app: typeof Vue) {
  Object.entries(components).forEach(([name, component]) => {
    app.component(name, component);
  });
}

/**
 * 按需导出组件
 */
export { ScrollView, AutoLoad };

/**
 * 导出组件类型
 */
export type {
  ScrollViewProps,
  ScrollViewEmits,
  ScrollViewInstance,
  ScrollDetail,
  ScrollToEdgeDetail,
  RefresherPullingDetail,
  ScrollToOptions,
  ScrollInfo,
} from "./ScrollView/types";

/**
 * 导出 Hook
 */
export { useAutoLoad } from "./AutoLoad/useAutoLoad";

/**
 * 默认导出所有组件
 */
export default components;
