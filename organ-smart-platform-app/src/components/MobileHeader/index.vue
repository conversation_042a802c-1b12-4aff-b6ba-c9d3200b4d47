<template>
  <div
    class="mobile-header"
    :style="{
      backgroundColor: bgColor || '#ffffff',
      color: textColor || '#333',
    }"
  >
    <!-- 左侧插槽：默认为返回按钮 -->
    <slot name="left">
      <div class="header-left">
        <i class="el-icon-arrow-left" @click="handleBack"></i>
      </div>
    </slot>

    <!-- 居中标题（绝对定位实现屏幕级居中） -->
    <div class="header-title-center">
      <slot name="title">
        <div class="title-text">{{ titleText }}</div>
      </slot>
    </div>

    <!-- 右侧插槽：可放置更多操作按钮 -->
    <slot name="right">
      <div class="header-right"></div>
    </slot>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";

/** props */
const props = defineProps<{
  title?: string;
  fallbackPath?: string; // 无历史时回退路径
  bgColor?: string;
  textColor?: string;
}>();

/** 路由 */
const route = useRoute();
const router = useRouter();

/** 无历史时回退路径 */
const FALLBACK_PATH = computed(() => props.fallbackPath || "/client/home");

/** 标题 */
const titleText = computed(() => {
  return props.title || (route.meta.title as string) || "";
});

/** 点击返回 */
const handleBack = () => {
  if (window.history.length > 1) {
    router.go(-1);
  } else {
    if (router.currentRoute?.path !== FALLBACK_PATH.value) {
      router.replace(FALLBACK_PATH.value);
    }
  }
};
</script>

<style scoped lang="scss">
// 变量定义
$--safe-area-top: constant(safe-area-inset-top);
$--safe-area-top: env(safe-area-inset-top);

.mobile-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: var(--header-height);
  padding: $--safe-area-top 12px 0;
  // background-color: #ffffff;
  // border-bottom: 1px solid #f1f1f1;
  z-index: 1000;

  // 总高度包含状态栏
  min-height: calc(var(--header-height) + $--safe-area-top);

  .header-left,
  .header-right {
    flex: 0 0 auto;
    width: 30px;
    height: var(--header-height);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .header-left i {
    font-size: 24px;
    // color: #333;
    cursor: pointer;
  }

  // 右侧内容可自定义
  .header-right {
    font-size: 14px;
    // color: #1677ff;
    cursor: pointer;
  }

  // 居中标题（绝对定位）
  .header-title-center {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: none; // 允许穿透点击
    z-index: 1;

    .title-text {
      font-size: 18px;
      font-weight: 500;
      // color: #333;
      max-width: 70%;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      pointer-events: auto; // 标题本身可点击
    }
  }
}
</style>
