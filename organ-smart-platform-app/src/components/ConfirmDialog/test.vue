<template>
  <div class="confirm-dialog-test">
    <h1>ConfirmDialog 组件测试</h1>
    
    <div class="test-section">
      <h2>基础功能测试</h2>
      <button @click="testBasic">基础确认弹框</button>
      <button @click="testWarning">警告类型</button>
      <button @click="testDanger">危险类型</button>
      <button @click="testSuccess">成功类型</button>
    </div>

    <div class="test-section">
      <h2>配置测试</h2>
      <button @click="testCustomText">自定义按钮文本</button>
      <button @click="testNoCancel">只有确认按钮</button>
      <button @click="testNoClose">禁止关闭</button>
      <button @click="testCustomWidth">自定义宽度</button>
    </div>

    <div class="test-section">
      <h2>插槽测试</h2>
      <button @click="testContentSlot">内容插槽</button>
      <button @click="testTitleSlot">标题插槽</button>
      <button @click="testFooterSlot">底部插槽</button>
    </div>

    <!-- 测试结果显示 -->
    <div class="test-result">
      <h3>测试结果：</h3>
      <div class="result-list">
        <div 
          v-for="(result, index) in testResults" 
          :key="index"
          class="result-item"
          :class="result.type"
        >
          {{ result.message }}
        </div>
      </div>
    </div>

    <!-- 基础测试弹框 -->
    <ConfirmDialog
      :visible.sync="basicVisible"
      title="基础测试"
      content="这是一个基础的确认弹框测试"
      @confirm="handleResult('基础弹框', '确认')"
      @cancel="handleResult('基础弹框', '取消')"
    />

    <!-- 警告类型弹框 -->
    <ConfirmDialog
      :visible.sync="warningVisible"
      title="警告测试"
      content="这是一个警告类型的确认弹框"
      confirm-type="warning"
      @confirm="handleResult('警告弹框', '确认')"
      @cancel="handleResult('警告弹框', '取消')"
    />

    <!-- 危险类型弹框 -->
    <ConfirmDialog
      :visible.sync="dangerVisible"
      title="危险操作"
      content="这是一个危险类型的确认弹框"
      confirm-type="danger"
      @confirm="handleResult('危险弹框', '确认')"
      @cancel="handleResult('危险弹框', '取消')"
    />

    <!-- 成功类型弹框 -->
    <ConfirmDialog
      :visible.sync="successVisible"
      title="成功提示"
      content="这是一个成功类型的确认弹框"
      confirm-type="success"
      @confirm="handleResult('成功弹框', '确认')"
      @cancel="handleResult('成功弹框', '取消')"
    />

    <!-- 自定义按钮文本弹框 -->
    <ConfirmDialog
      :visible.sync="customTextVisible"
      title="自定义按钮"
      content="这个弹框使用了自定义的按钮文本"
      confirm-text="同意"
      cancel-text="拒绝"
      @confirm="handleResult('自定义按钮', '同意')"
      @cancel="handleResult('自定义按钮', '拒绝')"
    />

    <!-- 只有确认按钮弹框 -->
    <ConfirmDialog
      :visible.sync="noCancelVisible"
      title="只有确认按钮"
      content="这个弹框只显示确认按钮"
      :show-cancel="false"
      confirm-text="知道了"
      @confirm="handleResult('只有确认按钮', '知道了')"
    />

    <!-- 禁止关闭弹框 -->
    <ConfirmDialog
      :visible.sync="noCloseVisible"
      title="禁止关闭"
      content="这个弹框禁止通过点击遮罩或ESC键关闭"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      @confirm="handleResult('禁止关闭', '确认')"
      @cancel="handleResult('禁止关闭', '取消')"
    />

    <!-- 自定义宽度弹框 -->
    <ConfirmDialog
      :visible.sync="customWidthVisible"
      title="自定义宽度"
      content="这个弹框使用了自定义的宽度设置"
      width="600px"
      @confirm="handleResult('自定义宽度', '确认')"
      @cancel="handleResult('自定义宽度', '取消')"
    />

    <!-- 内容插槽弹框 -->
    <ConfirmDialog
      :visible.sync="contentSlotVisible"
      title="内容插槽测试"
      @confirm="handleResult('内容插槽', '确认')"
      @cancel="handleResult('内容插槽', '取消')"
    >
      <div class="custom-content">
        <p>这是通过插槽自定义的内容</p>
        <ul>
          <li>支持HTML内容</li>
          <li>可以包含任意组件</li>
          <li>样式完全可控</li>
        </ul>
      </div>
    </ConfirmDialog>

    <!-- 标题插槽弹框 -->
    <ConfirmDialog
      :visible.sync="titleSlotVisible"
      @confirm="handleResult('标题插槽', '确认')"
      @cancel="handleResult('标题插槽', '取消')"
    >
      <template #title>
        <div class="custom-title">
          <span class="icon">🎯</span>
          <span>自定义标题插槽</span>
        </div>
      </template>
      <p>这个弹框使用了自定义的标题插槽</p>
    </ConfirmDialog>

    <!-- 底部插槽弹框 -->
    <ConfirmDialog
      :visible.sync="footerSlotVisible"
      title="底部插槽测试"
      content="这个弹框使用了自定义的底部插槽"
    >
      <template #footer>
        <div class="custom-footer">
          <button @click="handleCustomFooter('选项1')">选项1</button>
          <button @click="handleCustomFooter('选项2')">选项2</button>
          <button @click="handleCustomFooter('选项3')">选项3</button>
          <button @click="footerSlotVisible = false">关闭</button>
        </div>
      </template>
    </ConfirmDialog>
  </div>
</template>

<script setup lang="ts">
import ConfirmDialog from './index.vue';

// 弹框显示状态
const basicVisible = ref(false);
const warningVisible = ref(false);
const dangerVisible = ref(false);
const successVisible = ref(false);
const customTextVisible = ref(false);
const noCancelVisible = ref(false);
const noCloseVisible = ref(false);
const customWidthVisible = ref(false);
const contentSlotVisible = ref(false);
const titleSlotVisible = ref(false);
const footerSlotVisible = ref(false);

// 测试结果
interface TestResult {
  message: string;
  type: 'success' | 'info' | 'warning';
}

const testResults = ref<TestResult[]>([]);

// 测试方法
const testBasic = () => {
  basicVisible.value = true;
  addResult('开始测试基础确认弹框', 'info');
};

const testWarning = () => {
  warningVisible.value = true;
  addResult('开始测试警告类型弹框', 'info');
};

const testDanger = () => {
  dangerVisible.value = true;
  addResult('开始测试危险类型弹框', 'info');
};

const testSuccess = () => {
  successVisible.value = true;
  addResult('开始测试成功类型弹框', 'info');
};

const testCustomText = () => {
  customTextVisible.value = true;
  addResult('开始测试自定义按钮文本', 'info');
};

const testNoCancel = () => {
  noCancelVisible.value = true;
  addResult('开始测试只有确认按钮', 'info');
};

const testNoClose = () => {
  noCloseVisible.value = true;
  addResult('开始测试禁止关闭弹框', 'warning');
};

const testCustomWidth = () => {
  customWidthVisible.value = true;
  addResult('开始测试自定义宽度', 'info');
};

const testContentSlot = () => {
  contentSlotVisible.value = true;
  addResult('开始测试内容插槽', 'info');
};

const testTitleSlot = () => {
  titleSlotVisible.value = true;
  addResult('开始测试标题插槽', 'info');
};

const testFooterSlot = () => {
  footerSlotVisible.value = true;
  addResult('开始测试底部插槽', 'info');
};

// 处理测试结果
const handleResult = (testName: string, action: string) => {
  addResult(`${testName} - 用户点击了: ${action}`, 'success');
  // 关闭对应的弹框
  closeAllDialogs();
};

const handleCustomFooter = (option: string) => {
  addResult(`底部插槽 - 用户选择了: ${option}`, 'success');
  footerSlotVisible.value = false;
};

// 添加测试结果
const addResult = (message: string, type: TestResult['type']) => {
  testResults.value.unshift({
    message: `[${new Date().toLocaleTimeString()}] ${message}`,
    type
  });
  
  // 限制结果数量
  if (testResults.value.length > 20) {
    testResults.value = testResults.value.slice(0, 20);
  }
};

// 关闭所有弹框
const closeAllDialogs = () => {
  basicVisible.value = false;
  warningVisible.value = false;
  dangerVisible.value = false;
  successVisible.value = false;
  customTextVisible.value = false;
  noCancelVisible.value = false;
  noCloseVisible.value = false;
  customWidthVisible.value = false;
  contentSlotVisible.value = false;
  titleSlotVisible.value = false;
  footerSlotVisible.value = false;
};
</script>

<style scoped lang="scss">
.confirm-dialog-test {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;

  h1 {
    text-align: center;
    color: #1d1e20;
    margin-bottom: 30px;
  }

  .test-section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #e8e8e8;
    border-radius: 8px;

    h2 {
      margin: 0 0 15px 0;
      color: #383838;
      font-size: 16px;
    }

    button {
      margin: 5px 10px 5px 0;
      padding: 8px 16px;
      border: 1px solid #409eff;
      background: white;
      color: #409eff;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.2s ease;

      &:hover {
        background: #409eff;
        color: white;
      }
    }
  }

  .test-result {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;

    h3 {
      margin: 0 0 15px 0;
      color: #1d1e20;
    }

    .result-list {
      max-height: 300px;
      overflow-y: auto;

      .result-item {
        padding: 8px 12px;
        margin-bottom: 5px;
        border-radius: 4px;
        font-size: 14px;
        font-family: monospace;

        &.success {
          background: #f0f9ff;
          color: #059669;
          border-left: 3px solid #059669;
        }

        &.info {
          background: #f0f9ff;
          color: #0369a1;
          border-left: 3px solid #0369a1;
        }

        &.warning {
          background: #fffbeb;
          color: #d97706;
          border-left: 3px solid #d97706;
        }
      }
    }
  }

  // 自定义内容样式
  .custom-content {
    p {
      margin: 0 0 10px 0;
      color: #666;
    }

    ul {
      margin: 0;
      padding-left: 20px;
      color: #666;

      li {
        margin-bottom: 5px;
      }
    }
  }

  .custom-title {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #1d1e20;
    font-weight: 600;

    .icon {
      font-size: 20px;
    }
  }

  .custom-footer {
    display: flex;
    gap: 10px;
    justify-content: flex-end;

    button {
      padding: 8px 16px;
      border: 1px solid #ddd;
      background: white;
      color: #666;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;

      &:hover {
        border-color: #409eff;
        color: #409eff;
      }

      &:last-child {
        background: #f56c6c;
        color: white;
        border-color: #f56c6c;

        &:hover {
          opacity: 0.8;
        }
      }
    }
  }
}
</style>
