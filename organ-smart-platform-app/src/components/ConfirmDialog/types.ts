/**
 * 确认弹框组件类型定义
 */

/**
 * 确认弹框组件Props接口
 */
export interface IConfirmDialogProps {
  /** 是否显示弹框 */
  visible: boolean;
  /** 弹框标题 */
  title?: string;
  /** 弹框内容文本 */
  content?: string;
  /** 确认按钮文本 */
  confirmText?: string;
  /** 取消按钮文本 */
  cancelText?: string;
  /** 确认按钮类型 */
  confirmType?: 'primary' | 'success' | 'warning' | 'danger' | 'info';
  /** 是否显示取消按钮 */
  showCancel?: boolean;
  /** 是否显示确认按钮 */
  showConfirm?: boolean;
  /** 弹框宽度 */
  width?: string;
  /** 是否可以通过点击遮罩层关闭 */
  closeOnClickModal?: boolean;
  /** 是否可以通过按下ESC键关闭 */
  closeOnPressEscape?: boolean;
  /** 自定义CSS类名 */
  customClass?: string;
  /** 是否居中显示 */
  center?: boolean;
  /** 是否显示关闭按钮 */
  showClose?: boolean;
}

/**
 * 确认弹框组件事件接口
 */
export interface IConfirmDialogEmits {
  /** 弹框显示状态变化事件 */
  (e: 'update:visible', visible: boolean): void;
  /** 确认按钮点击事件 */
  (e: 'confirm'): void;
  /** 取消按钮点击事件 */
  (e: 'cancel'): void;
  /** 弹框关闭事件 */
  (e: 'close'): void;
}

/**
 * 插槽接口
 */
export interface IConfirmDialogSlots {
  /** 标题插槽 */
  title?: any;
  /** 内容插槽 */
  default?: any;
  /** 底部操作区插槽 */
  footer?: any;
}
