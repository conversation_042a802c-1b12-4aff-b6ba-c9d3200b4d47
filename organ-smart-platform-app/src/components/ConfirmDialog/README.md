# ConfirmDialog 确认弹框组件

一个功能完整的确认弹框组件，支持自定义标题、内容、按钮样式，并提供灵活的插槽支持。

## 特性

- 🎯 **多种确认类型**：支持 primary、success、warning、danger、info 五种按钮类型
- 📍 **灵活配置**：可自定义标题、内容、按钮文本、宽度等
- 🎭 **插槽支持**：提供标题、内容、底部操作区三个插槽
- 🔒 **交互控制**：支持禁用遮罩点击关闭、ESC键关闭等
- 📱 **响应式设计**：完美适配移动端和PC端
- 🎨 **样式美观**：参考项目设计风格，与现有组件保持一致
- ⌨️ **键盘支持**：支持ESC键关闭弹框
- 🎬 **动画效果**：内置淡入动画效果

## 基础用法

### 1. 简单确认弹框

```vue
<template>
  <div>
    <button @click="showDialog">删除记录</button>
    
    <ConfirmDialog
      :visible.sync="dialogVisible"
      title="删除确认"
      content="确定要删除这条记录吗？删除后无法恢复。"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup lang="ts">
import ConfirmDialog from '@/components/ConfirmDialog';

const dialogVisible = ref(false);

const showDialog = () => {
  dialogVisible.value = true;
};

const handleConfirm = () => {
  console.log('确认删除');
  dialogVisible.value = false;
};

const handleCancel = () => {
  console.log('取消删除');
};
</script>
```

### 2. 不同类型的确认弹框

```vue
<template>
  <div>
    <!-- 警告类型 -->
    <ConfirmDialog
      :visible.sync="warningVisible"
      title="操作警告"
      content="此操作可能会影响系统性能，确定要继续吗？"
      confirm-type="warning"
      @confirm="handleWarningConfirm"
    />
    
    <!-- 危险类型 -->
    <ConfirmDialog
      :visible.sync="dangerVisible"
      title="危险操作"
      content="此操作不可逆转，确定要执行吗？"
      confirm-type="danger"
      confirm-text="强制执行"
      @confirm="handleDangerConfirm"
    />
  </div>
</template>
```

### 3. 自定义配置

```vue
<template>
  <ConfirmDialog
    :visible.sync="customVisible"
    title="保存文档"
    content="文档已修改，是否保存更改？"
    confirm-text="保存"
    cancel-text="不保存"
    confirm-type="success"
    width="500px"
    :close-on-click-modal="false"
    @confirm="handleSave"
    @cancel="handleDiscard"
  />
</template>
```

## 插槽用法

### 1. 自定义内容插槽

```vue
<template>
  <ConfirmDialog
    :visible.sync="slotVisible"
    title="用户信息"
    @confirm="handleConfirm"
  >
    <div class="user-info">
      <div class="avatar">👤</div>
      <div class="details">
        <h4>张三</h4>
        <p>邮箱: <EMAIL></p>
        <p>部门: 技术部</p>
      </div>
    </div>
    <p class="warning">确定要删除此用户吗？</p>
  </ConfirmDialog>
</template>
```

### 2. 自定义标题插槽

```vue
<template>
  <ConfirmDialog :visible.sync="titleSlotVisible">
    <template #title>
      <div class="custom-title">
        <span class="icon">⚠️</span>
        <span>重要提醒</span>
      </div>
    </template>
    
    <p>这是一个重要的操作确认。</p>
  </ConfirmDialog>
</template>
```

### 3. 自定义底部操作区插槽

```vue
<template>
  <ConfirmDialog
    :visible.sync="footerSlotVisible"
    title="批量操作"
    content="已选择 5 个项目，请选择要执行的操作："
  >
    <template #footer>
      <div class="custom-actions">
        <button @click="footerSlotVisible = false">取消</button>
        <button @click="handleBatchEdit">批量编辑</button>
        <button @click="handleBatchDelete">批量删除</button>
      </div>
    </template>
  </ConfirmDialog>
</template>
```

## API 文档

### Props

| 参数 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| visible | 是否显示弹框 | boolean | — | false |
| title | 弹框标题 | string | — | '确认操作' |
| content | 弹框内容文本 | string | — | '确定要执行此操作吗？' |
| confirmText | 确认按钮文本 | string | — | '确定' |
| cancelText | 取消按钮文本 | string | — | '取消' |
| confirmType | 确认按钮类型 | string | primary/success/warning/danger/info | 'primary' |
| showCancel | 是否显示取消按钮 | boolean | — | true |
| showConfirm | 是否显示确认按钮 | boolean | — | true |
| width | 弹框宽度 | string | — | '400px' |
| closeOnClickModal | 是否可以通过点击遮罩层关闭 | boolean | — | true |
| closeOnPressEscape | 是否可以通过按下ESC键关闭 | boolean | — | true |
| customClass | 自定义CSS类名 | string | — | '' |
| center | 是否居中显示 | boolean | — | true |
| showClose | 是否显示关闭按钮 | boolean | — | true |

### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| update:visible | 弹框显示状态变化时触发 | (visible: boolean) |
| confirm | 点击确认按钮时触发 | — |
| cancel | 点击取消按钮时触发 | — |
| close | 弹框关闭时触发 | — |

### Slots

| 插槽名 | 说明 |
|--------|------|
| title | 自定义标题内容 |
| default | 自定义弹框内容 |
| footer | 自定义底部操作区 |

## 样式定制

组件使用CSS变量来支持主题定制：

```css
:root {
  --color-primary: #409eff; /* 主色调 */
}
```

## 注意事项

1. **Vue 版本兼容性**：组件基于 Vue 2.7 + Composition API 开发
2. **TypeScript 支持**：提供完整的类型定义
3. **响应式设计**：在移动端会自动调整布局和样式
4. **键盘交互**：支持 ESC 键关闭弹框（可通过 `closeOnPressEscape` 控制）
5. **事件处理**：确认操作后需要手动关闭弹框（设置 `visible` 为 `false`）

## 示例页面

可以查看 `example.vue` 文件获取完整的使用示例，包含各种配置和插槽用法的演示。
