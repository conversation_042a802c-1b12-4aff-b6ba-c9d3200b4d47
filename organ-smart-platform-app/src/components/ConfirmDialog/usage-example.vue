<template>
  <div class="usage-example">
    <h2>ConfirmDialog 使用示例</h2>
    
    <!-- 模拟一个数据列表 -->
    <div class="data-list">
      <div class="list-header">
        <h3>用户列表</h3>
        <button class="btn-primary" @click="showBatchDeleteDialog">
          批量删除
        </button>
      </div>
      
      <div class="list-content">
        <div 
          v-for="user in userList" 
          :key="user.id"
          class="list-item"
        >
          <div class="user-info">
            <span class="name">{{ user.name }}</span>
            <span class="email">{{ user.email }}</span>
          </div>
          <div class="actions">
            <button 
              class="btn-edit" 
              @click="editUser(user)"
            >
              编辑
            </button>
            <button 
              class="btn-delete" 
              @click="showDeleteDialog(user)"
            >
              删除
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 删除单个用户确认弹框 -->
    <ConfirmDialog
      :visible.sync="deleteDialogVisible"
      title="删除用户"
      :content="`确定要删除用户 ${selectedUser?.name} 吗？删除后无法恢复。`"
      confirm-type="danger"
      confirm-text="删除"
      @confirm="handleDeleteUser"
      @cancel="handleCancelDelete"
    />

    <!-- 批量删除确认弹框 -->
    <ConfirmDialog
      :visible.sync="batchDeleteDialogVisible"
      title="批量删除"
      confirm-type="danger"
      confirm-text="确认删除"
      @confirm="handleBatchDelete"
    >
      <div class="batch-delete-content">
        <p>确定要删除以下用户吗？此操作不可撤销。</p>
        <div class="user-preview">
          <div 
            v-for="user in selectedUsers" 
            :key="user.id"
            class="preview-item"
          >
            <span>{{ user.name }}</span>
            <span class="email">{{ user.email }}</span>
          </div>
        </div>
      </div>
    </ConfirmDialog>

    <!-- 编辑用户弹框（使用自定义底部插槽） -->
    <ConfirmDialog
      :visible.sync="editDialogVisible"
      title="编辑用户信息"
      width="500px"
    >
      <div class="edit-form">
        <div class="form-item">
          <label>姓名：</label>
          <input 
            v-model="editForm.name" 
            type="text" 
            class="form-input"
          />
        </div>
        <div class="form-item">
          <label>邮箱：</label>
          <input 
            v-model="editForm.email" 
            type="email" 
            class="form-input"
          />
        </div>
        <div class="form-item">
          <label>部门：</label>
          <select v-model="editForm.department" class="form-select">
            <option value="技术部">技术部</option>
            <option value="产品部">产品部</option>
            <option value="运营部">运营部</option>
          </select>
        </div>
      </div>
      
      <template #footer>
        <div class="edit-actions">
          <button class="btn-cancel" @click="handleCancelEdit">
            取消
          </button>
          <button class="btn-save" @click="handleSaveEdit">
            保存
          </button>
        </div>
      </template>
    </ConfirmDialog>

    <!-- 操作成功提示弹框 -->
    <ConfirmDialog
      :visible.sync="successDialogVisible"
      title="操作成功"
      :content="successMessage"
      :show-cancel="false"
      confirm-text="知道了"
      confirm-type="success"
      @confirm="handleSuccessConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import ConfirmDialog from './index.vue';

// 用户数据接口
interface User {
  id: number;
  name: string;
  email: string;
  department: string;
}

// 模拟用户数据
const userList = ref<User[]>([
  { id: 1, name: '张三', email: '<EMAIL>', department: '技术部' },
  { id: 2, name: '李四', email: '<EMAIL>', department: '产品部' },
  { id: 3, name: '王五', email: '<EMAIL>', department: '运营部' },
  { id: 4, name: '赵六', email: '<EMAIL>', department: '技术部' },
]);

// 弹框显示状态
const deleteDialogVisible = ref(false);
const batchDeleteDialogVisible = ref(false);
const editDialogVisible = ref(false);
const successDialogVisible = ref(false);

// 选中的用户
const selectedUser = ref<User | null>(null);
const selectedUsers = ref<User[]>([]);
const successMessage = ref('');

// 编辑表单
const editForm = ref({
  name: '',
  email: '',
  department: '',
});

/**
 * 显示删除单个用户确认弹框
 */
const showDeleteDialog = (user: User) => {
  selectedUser.value = user;
  deleteDialogVisible.value = true;
};

/**
 * 显示批量删除确认弹框
 */
const showBatchDeleteDialog = () => {
  // 模拟选中前两个用户
  selectedUsers.value = userList.value.slice(0, 2);
  batchDeleteDialogVisible.value = true;
};

/**
 * 编辑用户
 */
const editUser = (user: User) => {
  selectedUser.value = user;
  editForm.value = { ...user };
  editDialogVisible.value = true;
};

/**
 * 处理删除用户确认
 */
const handleDeleteUser = () => {
  if (selectedUser.value) {
    const index = userList.value.findIndex(u => u.id === selectedUser.value!.id);
    if (index > -1) {
      userList.value.splice(index, 1);
      successMessage.value = `用户 ${selectedUser.value.name} 已成功删除`;
      successDialogVisible.value = true;
    }
  }
  deleteDialogVisible.value = false;
  selectedUser.value = null;
};

/**
 * 处理取消删除
 */
const handleCancelDelete = () => {
  selectedUser.value = null;
};

/**
 * 处理批量删除确认
 */
const handleBatchDelete = () => {
  const deletedNames = selectedUsers.value.map(u => u.name);
  selectedUsers.value.forEach(user => {
    const index = userList.value.findIndex(u => u.id === user.id);
    if (index > -1) {
      userList.value.splice(index, 1);
    }
  });
  
  successMessage.value = `已成功删除 ${deletedNames.join('、')} 等 ${selectedUsers.value.length} 个用户`;
  successDialogVisible.value = true;
  batchDeleteDialogVisible.value = false;
  selectedUsers.value = [];
};

/**
 * 处理保存编辑
 */
const handleSaveEdit = () => {
  if (selectedUser.value) {
    const index = userList.value.findIndex(u => u.id === selectedUser.value!.id);
    if (index > -1) {
      userList.value[index] = { ...selectedUser.value, ...editForm.value };
      successMessage.value = `用户 ${editForm.value.name} 的信息已更新`;
      successDialogVisible.value = true;
    }
  }
  editDialogVisible.value = false;
  selectedUser.value = null;
};

/**
 * 处理取消编辑
 */
const handleCancelEdit = () => {
  editDialogVisible.value = false;
  selectedUser.value = null;
  editForm.value = { name: '', email: '', department: '' };
};

/**
 * 处理成功提示确认
 */
const handleSuccessConfirm = () => {
  successDialogVisible.value = false;
};
</script>

<style scoped lang="scss">
.usage-example {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;

  h2 {
    color: #1d1e20;
    margin-bottom: 30px;
    text-align: center;
  }

  .data-list {
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    overflow: hidden;

    .list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      background: #f8f9fa;
      border-bottom: 1px solid #e8e8e8;

      h3 {
        margin: 0;
        color: #1d1e20;
        font-size: 16px;
      }

      .btn-primary {
        padding: 8px 16px;
        background: #f56c6c;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;

        &:hover {
          opacity: 0.8;
        }
      }
    }

    .list-content {
      .list-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .user-info {
          .name {
            font-weight: 500;
            color: #1d1e20;
            margin-right: 12px;
          }

          .email {
            color: #666;
            font-size: 14px;
          }
        }

        .actions {
          display: flex;
          gap: 8px;

          button {
            padding: 6px 12px;
            border: 1px solid;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s ease;

            &.btn-edit {
              background: white;
              color: #409eff;
              border-color: #409eff;

              &:hover {
                background: #409eff;
                color: white;
              }
            }

            &.btn-delete {
              background: white;
              color: #f56c6c;
              border-color: #f56c6c;

              &:hover {
                background: #f56c6c;
                color: white;
              }
            }
          }
        }
      }
    }
  }

  // 批量删除内容样式
  .batch-delete-content {
    .user-preview {
      margin-top: 16px;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 6px;
      max-height: 200px;
      overflow-y: auto;

      .preview-item {
        display: flex;
        justify-content: space-between;
        padding: 8px 0;
        border-bottom: 1px solid #e8e8e8;

        &:last-child {
          border-bottom: none;
        }

        .email {
          color: #666;
          font-size: 14px;
        }
      }
    }
  }

  // 编辑表单样式
  .edit-form {
    .form-item {
      margin-bottom: 16px;

      label {
        display: block;
        margin-bottom: 6px;
        color: #1d1e20;
        font-weight: 500;
      }

      .form-input,
      .form-select {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;

        &:focus {
          outline: none;
          border-color: #409eff;
        }
      }
    }
  }

  .edit-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;

    button {
      padding: 10px 20px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.2s ease;

      &.btn-cancel {
        background: #f5f5f5;
        color: #666;

        &:hover {
          background: #e8e8e8;
        }
      }

      &.btn-save {
        background: #67c23a;
        color: white;

        &:hover {
          opacity: 0.8;
        }
      }
    }
  }
}
</style>
