<template>
  <div class="confirm-dialog-example">
    <h2>确认弹框组件示例</h2>
    
    <div class="example-section">
      <h3>基础用法</h3>
      <div class="button-group">
        <button class="demo-btn" @click="showBasicDialog">基础确认弹框</button>
        <button class="demo-btn" @click="showWarningDialog">警告确认弹框</button>
        <button class="demo-btn" @click="showDangerDialog">危险确认弹框</button>
      </div>
    </div>

    <div class="example-section">
      <h3>自定义配置</h3>
      <div class="button-group">
        <button class="demo-btn" @click="showCustomDialog">自定义按钮文本</button>
        <button class="demo-btn" @click="showNoCloseDialog">禁止点击遮罩关闭</button>
        <button class="demo-btn" @click="showOnlyConfirmDialog">只显示确认按钮</button>
      </div>
    </div>

    <div class="example-section">
      <h3>插槽用法</h3>
      <div class="button-group">
        <button class="demo-btn" @click="showSlotDialog">自定义内容插槽</button>
        <button class="demo-btn" @click="showFooterSlotDialog">自定义底部插槽</button>
      </div>
    </div>

    <!-- 基础确认弹框 -->
    <ConfirmDialog
      :visible.sync="basicDialogVisible"
      title="删除确认"
      content="确定要删除这条记录吗？删除后无法恢复。"
      @confirm="handleBasicConfirm"
      @cancel="handleBasicCancel"
    />

    <!-- 警告确认弹框 -->
    <ConfirmDialog
      :visible.sync="warningDialogVisible"
      title="操作警告"
      content="此操作可能会影响系统性能，确定要继续吗？"
      confirm-type="warning"
      @confirm="handleWarningConfirm"
    />

    <!-- 危险确认弹框 -->
    <ConfirmDialog
      :visible.sync="dangerDialogVisible"
      title="危险操作"
      content="此操作不可逆转，确定要执行吗？"
      confirm-type="danger"
      confirm-text="强制执行"
      @confirm="handleDangerConfirm"
    />

    <!-- 自定义按钮文本弹框 -->
    <ConfirmDialog
      :visible.sync="customDialogVisible"
      title="保存文档"
      content="文档已修改，是否保存更改？"
      confirm-text="保存"
      cancel-text="不保存"
      confirm-type="success"
      @confirm="handleCustomConfirm"
      @cancel="handleCustomCancel"
    />

    <!-- 禁止点击遮罩关闭弹框 -->
    <ConfirmDialog
      :visible.sync="noCloseDialogVisible"
      title="重要提醒"
      content="请仔细阅读以下内容，必须做出选择才能继续。"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      @confirm="handleNoCloseConfirm"
    />

    <!-- 只显示确认按钮弹框 -->
    <ConfirmDialog
      :visible.sync="onlyConfirmDialogVisible"
      title="操作完成"
      content="数据已成功保存到服务器。"
      :show-cancel="false"
      confirm-text="知道了"
      confirm-type="success"
      @confirm="handleOnlyConfirmConfirm"
    />

    <!-- 自定义内容插槽弹框 -->
    <ConfirmDialog
      :visible.sync="slotDialogVisible"
      title="用户信息"
      @confirm="handleSlotConfirm"
    >
      <div class="custom-content">
        <div class="user-info">
          <div class="avatar">👤</div>
          <div class="info">
            <h4>张三</h4>
            <p>邮箱: <EMAIL></p>
            <p>部门: 技术部</p>
            <p>职位: 前端工程师</p>
          </div>
        </div>
        <p class="tip">确定要删除此用户吗？</p>
      </div>
    </ConfirmDialog>

    <!-- 自定义底部插槽弹框 -->
    <ConfirmDialog
      :visible.sync="footerSlotDialogVisible"
      title="批量操作"
      content="已选择 5 个项目，请选择要执行的操作："
    >
      <template #footer>
        <div class="custom-footer">
          <button class="demo-btn secondary" @click="footerSlotDialogVisible = false">
            取消
          </button>
          <button class="demo-btn warning" @click="handleBatchEdit">
            批量编辑
          </button>
          <button class="demo-btn danger" @click="handleBatchDelete">
            批量删除
          </button>
        </div>
      </template>
    </ConfirmDialog>
  </div>
</template>

<script setup lang="ts">
import ConfirmDialog from './index.vue';

// 弹框显示状态
const basicDialogVisible = ref(false);
const warningDialogVisible = ref(false);
const dangerDialogVisible = ref(false);
const customDialogVisible = ref(false);
const noCloseDialogVisible = ref(false);
const onlyConfirmDialogVisible = ref(false);
const slotDialogVisible = ref(false);
const footerSlotDialogVisible = ref(false);

// 显示弹框方法
const showBasicDialog = () => {
  basicDialogVisible.value = true;
};

const showWarningDialog = () => {
  warningDialogVisible.value = true;
};

const showDangerDialog = () => {
  dangerDialogVisible.value = true;
};

const showCustomDialog = () => {
  customDialogVisible.value = true;
};

const showNoCloseDialog = () => {
  noCloseDialogVisible.value = true;
};

const showOnlyConfirmDialog = () => {
  onlyConfirmDialogVisible.value = true;
};

const showSlotDialog = () => {
  slotDialogVisible.value = true;
};

const showFooterSlotDialog = () => {
  footerSlotDialogVisible.value = true;
};

// 事件处理方法
const handleBasicConfirm = () => {
  console.log('基础确认弹框 - 确认');
  basicDialogVisible.value = false;
};

const handleBasicCancel = () => {
  console.log('基础确认弹框 - 取消');
};

const handleWarningConfirm = () => {
  console.log('警告确认弹框 - 确认');
  warningDialogVisible.value = false;
};

const handleDangerConfirm = () => {
  console.log('危险确认弹框 - 确认');
  dangerDialogVisible.value = false;
};

const handleCustomConfirm = () => {
  console.log('自定义弹框 - 保存');
  customDialogVisible.value = false;
};

const handleCustomCancel = () => {
  console.log('自定义弹框 - 不保存');
};

const handleNoCloseConfirm = () => {
  console.log('禁止关闭弹框 - 确认');
  noCloseDialogVisible.value = false;
};

const handleOnlyConfirmConfirm = () => {
  console.log('只有确认按钮弹框 - 确认');
  onlyConfirmDialogVisible.value = false;
};

const handleSlotConfirm = () => {
  console.log('插槽弹框 - 确认');
  slotDialogVisible.value = false;
};

const handleBatchEdit = () => {
  console.log('批量编辑');
  footerSlotDialogVisible.value = false;
};

const handleBatchDelete = () => {
  console.log('批量删除');
  footerSlotDialogVisible.value = false;
};
</script>

<style scoped lang="scss">
.confirm-dialog-example {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;

  h2 {
    color: #1d1e20;
    margin-bottom: 30px;
    text-align: center;
  }

  .example-section {
    margin-bottom: 30px;

    h3 {
      color: #383838;
      margin-bottom: 15px;
      font-size: 16px;
    }

    .button-group {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;
    }
  }

  .demo-btn {
    padding: 10px 16px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: #fff;
    color: #666;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;

    &:hover {
      border-color: var(--color-primary, #409eff);
      color: var(--color-primary, #409eff);
    }

    &.secondary {
      background: #f5f5f5;
      border-color: #ddd;
    }

    &.warning {
      background: #e6a23c;
      color: #fff;
      border-color: #e6a23c;
    }

    &.danger {
      background: #f56c6c;
      color: #fff;
      border-color: #f56c6c;
    }
  }

  // 自定义内容样式
  .custom-content {
    .user-info {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 8px;
      margin-bottom: 16px;

      .avatar {
        width: 48px;
        height: 48px;
        background: #e9ecef;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
      }

      .info {
        flex: 1;

        h4 {
          margin: 0 0 8px 0;
          color: #1d1e20;
          font-size: 16px;
        }

        p {
          margin: 4px 0;
          color: #666;
          font-size: 14px;
        }
      }
    }

    .tip {
      color: #f56c6c;
      font-weight: 500;
      margin: 0;
    }
  }

  // 自定义底部样式
  .custom-footer {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
  }
}
</style>
