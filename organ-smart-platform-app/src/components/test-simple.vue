<template>
  <div class="test-simple">
    <h2>简单组件测试</h2>
    
    <!-- ScrollView 基础测试 -->
    <div class="test-section">
      <h3>ScrollView 基础测试</h3>
      <div class="scroll-container">
        <ScrollView :scroll-y="true" @scroll="handleScroll">
          <div v-for="item in scrollList" :key="item" class="scroll-item">
            ScrollView 项目 {{ item }}
          </div>
        </ScrollView>
      </div>
    </div>

    <!-- AutoLoad 基础测试 -->
    <div class="test-section">
      <h3>AutoLoad 基础测试</h3>
      <div class="auto-container">
        <AutoLoad
          :loading="loading"
          :has-more="hasMore"
          @load-more="handleLoadMore"
        >
          <div v-for="item in autoList" :key="item.id" class="auto-item">
            <h4>{{ item.title }}</h4>
            <p>{{ item.description }}</p>
          </div>
        </AutoLoad>
      </div>
    </div>

    <!-- useAutoLoad Hook 测试 -->
    <div class="test-section">
      <h3>useAutoLoad Hook 测试</h3>
      <div class="hook-container">
        <AutoLoad
          :loading="hookLoading"
          :refreshing="hookRefreshing"
          :has-more="hookHasMore"
          :show-empty="hookIsEmpty"
          @load-more="hookLoadMore"
          @refresh="hookRefresh"
        >
          <div v-for="item in hookData" :key="item.id" class="hook-item">
            <h4>{{ item.title }}</h4>
            <p>{{ item.description }}</p>
          </div>
        </AutoLoad>
      </div>
      
      <div class="controls">
        <button @click="hookRefresh">刷新</button>
        <button @click="hookReset">重置</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ScrollView from './ScrollView/index.vue'
import AutoLoad from './AutoLoad/index.vue'
import { useAutoLoad } from './AutoLoad/useAutoLoad'

// ScrollView 测试数据
const scrollList = ref(Array.from({ length: 50 }, (_, i) => i + 1))

// AutoLoad 测试数据
const autoList = ref([
  { id: 1, title: '项目 1', description: '这是第一个项目' },
  { id: 2, title: '项目 2', description: '这是第二个项目' },
])
const loading = ref(false)
const hasMore = ref(true)
let page = 1

// useAutoLoad Hook 测试
const {
  data: hookData,
  loading: hookLoading,
  refreshing: hookRefreshing,
  hasMore: hookHasMore,
  isEmpty: hookIsEmpty,
  loadMore: hookLoadMore,
  refresh: hookRefresh,
  reset: hookReset,
} = useAutoLoad({
  pageSize: 5,
  immediate: false,
  loadData: async (pageNum, pageSize) => {
    // 模拟 API 请求
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const startId = (pageNum - 1) * pageSize + 1
    const data = Array.from({ length: pageSize }, (_, i) => ({
      id: startId + i,
      title: `Hook 项目 ${startId + i}`,
      description: `这是通过 Hook 管理的第 ${pageNum} 页第 ${i + 1} 个项目`,
    }))

    return {
      data,
      total: 25, // 模拟总数
    }
  },
  onError: (error) => {
    console.error('Hook 加载失败:', error)
  },
})

/**
 * ScrollView 事件处理
 */
function handleScroll(event: any) {
  console.log('ScrollView 滚动:', event.detail)
}

/**
 * AutoLoad 加载更多
 */
async function handleLoadMore() {
  if (loading.value || !hasMore.value) return
  
  loading.value = true
  try {
    // 模拟 API 请求
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    page++
    const newData = Array.from({ length: 5 }, (_, i) => ({
      id: (page - 1) * 5 + i + 1,
      title: `项目 ${(page - 1) * 5 + i + 1}`,
      description: `这是第 ${page} 页的第 ${i + 1} 个项目`,
    }))
    
    autoList.value.push(...newData)
    
    // 模拟没有更多数据
    if (page >= 5) {
      hasMore.value = false
    }
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.test-simple {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;

  h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
  }

  .test-section {
    margin-bottom: 40px;

    h3 {
      margin-bottom: 15px;
      color: #666;
      font-size: 18px;
    }
  }

  .scroll-container,
  .auto-container,
  .hook-container {
    height: 300px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 15px;
  }

  .scroll-item,
  .auto-item,
  .hook-item {
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
    background: #fff;

    &:hover {
      background-color: #f8f9fa;
    }

    h4 {
      margin: 0 0 8px 0;
      color: #333;
    }

    p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
  }

  .controls {
    display: flex;
    gap: 10px;
    justify-content: center;

    button {
      padding: 8px 16px;
      border: 1px solid #409eff;
      background: #409eff;
      color: white;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: #66b1ff;
      }
    }
  }
}
</style>
