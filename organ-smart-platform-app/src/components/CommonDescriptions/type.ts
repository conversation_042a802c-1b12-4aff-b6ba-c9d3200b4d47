/**
 * CommonDescriptions 组件的列配置类型
 * 注意：这个类型定义需要与组件内部的类型保持完全一致
 */
export interface CommonDescriptionsColumn {
  label: string
  prop: string
  /** 每条数据占据的比例 0~24 默认24 */
  span?: number
  /** 格式化内容 */
  formatter?: (value: any, data: Record<string, any>) => any
}

export type CommonDescriptionsColumns = Array<CommonDescriptionsColumn>

/**
 * CommonDescriptions 组件的 Props 类型
 * 注意：Vue 2 项目中，组件的 props 类型只能在组件内部声明
 * 这里的类型定义仅供外部引用时的类型提示使用
 */
export interface CommonDescriptionsProps {
  /** 数据源 */
  data?: Record<string, any>
  /** 数据展示格式配置 */
  columns: CommonDescriptionsColumns
  /** 每条数据占据的比例 0~24, 0 则不显示该项 */
  snap?: number
  /** labelWidth */
  labelWidth?: number | string
}
