<script setup lang="ts">
import { Col } from 'element-ui'
import { ArraySum } from '@/utils/array'
import { getCssUnit } from '@/utils/css'
import { isNumber } from '@/utils/number'
import { firstDefined } from '@/utils/object'

/** CommonDescriptions组件Props  */
interface CommonDescriptionsProps {
  /** 数据源 */
  data?: Record<string, any>
  /** 数据展示格式配置 */
  columns: CommonDescriptionsColumns
  /** 每条数据占据的比例 0~24, 0 则不显示该项 */
  snap?: number
  /** labelWidth */
  labelWidth?: number | string
}

interface CommonDescriptionsColumn {
  label: string
  prop: string
  /** 每条数据占据的比例 0~24 默认24 */
  span?: number
  /** 格式化内容 */
  formatter?: (value: any, data: Record<string, any>) => any
}

export type CommonDescriptionsColumns = Array<CommonDescriptionsColumn>

const props = withDefaults(defineProps<CommonDescriptionsProps>(), {
  data: () => ({}),
  labelWidth: 200,
})

const groupedColumns = computed(() => {
  return columnsGrouping(formatterColumns(props.columns))
})

/** 初始化 columns 过滤 span为0项，并设置span默认值 */
function formatterColumns(columns: CommonDescriptionsProps['columns']) {
  return columns.filter((item: CommonDescriptionsColumn) => item.span !== 0).map((item: CommonDescriptionsColumn) => ({ ...item, span: (isNumber(props.snap) ? props.snap : undefined) || item.span || 24 }))
}

/** 根据 span 进行分组 */
function columnsGrouping(columns: ReturnType<typeof formatterColumns>) {
  return columns.reduce((pre: CommonDescriptionsColumns[], cur: CommonDescriptionsColumn & { span: number }) => {
    const last = pre[pre.length - 1]
    if (last?.length) {
      const lastSpanSum = ArraySum(last, (prev, curr) => {
        return prev + curr.span
      })
      if (lastSpanSum + cur.span <= 24) {
        last.push(cur)
      }
      else {
        pre.push([cur])
      }
    }
    else {
      pre.push([cur])
    }
    return pre
  }, [] as ReturnType<typeof formatterColumns>[])
}
</script>

<template>
  <div class="common-descriptions">
    <div v-for="(rows, index) in groupedColumns" :key="index" class="row">
      <Col
        v-for="col in rows" :key="col.prop" class="col"
        :span="col.span"
      >
        <div
          class="col-label" :style="{
            width: getCssUnit(props.labelWidth),
          }"
        >
          <slot
            :name="`label-${col.prop}`" v-bind="col" :data="data"
            :value="data[col.prop]"
          >
            {{ col.label }}
          </slot>
        </div>
        <div class="col-prop">
          <slot
            :name="`prop-${col.prop}`" v-bind="col" :data="data"
            :value="data[col.prop]"
          >
            {{ firstDefined(col?.formatter?.(data[col.prop], data), data[col.prop], '-') }}
          </slot>
        </div>
      </Col>
    </div>
  </div>
</template>

<style scoped>
.common-descriptions {
  border: 1px solid #EBECEF;
  border-bottom: none;
  border-left: none;
  color: #383838;
}

.common-descriptions .row {
  display: flex;
  border-bottom: 1px solid #EBECEF;
}

.common-descriptions .row :deep(.col) {
  display: flex;
}

.common-descriptions .row :deep(.col) .col-label {
  display: flex;
  line-height: 1.5em;
  padding: 20px 20px;
  background-color: #edf2ff;
  border-right: 1px solid #EBECEF;
  border-left: 1px solid #EBECEF;
}

.common-descriptions .row :deep(.col) .col-prop {
  flex: 1;
  color: #666666;
  line-height: 24px;
  padding: 20px 20px;
  align-content: center;
  word-wrap: break-word;
  /* white-space: pre-wrap; */
  overflow: hidden;
}
</style>
