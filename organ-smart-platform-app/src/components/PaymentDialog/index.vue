<script setup lang="ts">
import type { PurchaseMembershipVO } from '@/api/order'
import { <PERSON>ton, Dialog } from 'element-ui'
import QRCode from 'qrcode'
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'
import { createPurchaseOrder, getOrderStatus, OrderStatus } from '@/api/order'
import { formatNumberWithCommas } from '@/utils/number'

interface Props {
  visible: boolean
  packageId: string
  userId?: string
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'paymentSuccess', orderInfo: PurchaseMembershipVO): void
  (e: 'close'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref(false)
const error = ref('')
const orderInfo = ref<PurchaseMembershipVO | null>(null)
const pollingTimer = ref<NodeJS.Timeout | null>(null)
const qrCodeCanvas = ref<HTMLCanvasElement>()
const pollingFailureCount = ref(0) // 轮询失败次数计数

// 屏幕尺寸检测
const isMobile = ref(false)

// 检测屏幕尺寸
function checkScreenSize() {
  isMobile.value = window.innerWidth <= 768
}

// 计算弹窗宽度
const dialogWidth = computed(() => {
  return isMobile.value ? '95%' : '500px'
})

// 计算弹窗类名
const dialogClass = computed(() => {
  return isMobile.value ? 'payment-dialog-mobile' : 'payment-dialog-desktop'
})

// 监听窗口大小变化
onMounted(() => {
  checkScreenSize()
  window.addEventListener('resize', checkScreenSize)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize)
  // 确保组件卸载时停止轮询
  stopPolling()
})

// 监听弹窗显示状态
watch(() => props.visible, (newVisible) => {
  if (newVisible && props.packageId) {
    createOrder()
  }
  else {
    // 弹窗关闭时立即停止轮询并清理状态
    stopPolling()
    clearDialogState()
  }
})

// 清理弹窗状态
function clearDialogState() {
  loading.value = false
  error.value = ''
  orderInfo.value = null
  pollingFailureCount.value = 0
}

// 生成二维码
function generateQRCode(url: string) {
  if (!qrCodeCanvas.value)
    return

  try {
    const qrSize = isMobile.value ? 160 : 200
    QRCode.toCanvas(qrCodeCanvas.value, url, {
      width: qrSize,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF',
      },
    })
  }
  catch (err) {
    console.error('生成二维码失败:', err)
  }
}

// 创建订单
async function createOrder() {
  loading.value = true
  error.value = ''
  orderInfo.value = null

  try {
    const result = await createPurchaseOrder({
      packageId: props.packageId,
      userId: props.userId,
    })

    orderInfo.value = result

    // 生成二维码
    if (result.paymentUrl) {
      nextTick(() => {
        generateQRCode(result.paymentUrl!)
      })
    }

    // 如果订单状态是待支付，开始轮询
    if (result.orderStatus === OrderStatus.Pending) {
      startPolling()
    }
    else if (result.orderStatus === OrderStatus.Paid) {
      // 如果已经支付，直接触发成功事件
      emit('paymentSuccess', result)
    }
  }
  catch (err: any) {
    error.value = err.message || '创建订单失败'
  }
  finally {
    loading.value = false
  }
}

// 开始轮询订单状态
function startPolling() {
  if (!orderInfo.value?.orderNo)
    return

  // 开始新轮询前先停止之前的轮询
  stopPolling()

  pollingTimer.value = setInterval(async () => {
    // 检查弹窗是否仍然可见，如果不可见则停止轮询
    if (!props.visible) {
      stopPolling()
      return
    }

    try {
      const result = await getOrderStatus(orderInfo.value!.orderNo!)

      if (result.orderStatus === OrderStatus.Paid) {
        // 支付成功
        console.log('PaymentDialog检测到支付成功')
        stopPolling()
        const paymentResult = {
          ...orderInfo.value!,
          orderStatus: result.orderStatus,
          orderStatusDesc: result.orderStatusDesc,
          paymentTime: result.paymentTime,
        }
        emit('paymentSuccess', paymentResult)
        handleClose()
      }
      else if (result.orderStatus === OrderStatus.Cancelled || result.orderStatus === OrderStatus.Refunded) {
        // 订单取消或退款
        stopPolling()
        orderInfo.value = {
          ...orderInfo.value!,
          orderStatus: result.orderStatus,
          orderStatusDesc: result.orderStatusDesc,
        }
      }
    }
    catch (err) {
      console.error('轮询订单状态失败:', err)
      pollingFailureCount.value++

      // 如果连续失败超过5次，停止轮询
      if (pollingFailureCount.value >= 5) {
        console.warn('轮询失败次数过多，停止轮询')
        stopPolling()
        error.value = '网络连接异常，请手动刷新状态'
      }
    }
  }, 3000) // 每3秒轮询一次
}

// 停止轮询
function stopPolling() {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value)
    pollingTimer.value = null
    console.log('轮询已停止')
  }
}

// 手动刷新状态
async function handleRefresh() {
  if (!orderInfo.value?.orderNo)
    return

  try {
    const result = await getOrderStatus(orderInfo.value.orderNo)
    orderInfo.value = {
      ...orderInfo.value,
      orderStatus: result.orderStatus,
      orderStatusDesc: result.orderStatusDesc,
      paymentTime: result.paymentTime,
    }

    if (result.orderStatus === OrderStatus.Paid) {
      emit('paymentSuccess', orderInfo.value)
      handleClose()
    }
  }
  catch (err: any) {
    error.value = err.message || '刷新状态失败'
  }
}

// 关闭弹窗
function handleClose() {
  console.log('关闭弹窗，停止轮询')
  stopPolling()
  clearDialogState()
  emit('update:visible', false)
  emit('close')
}

// 获取状态样式类
function getStatusClass(status?: OrderStatus) {
  switch (status) {
    case OrderStatus.Paid:
      return 'status-success'
    case OrderStatus.Pending:
      return 'status-warning'
    case OrderStatus.Cancelled:
    case OrderStatus.Refunded:
      return 'status-danger'
    default:
      return ''
  }
}
</script>

<template>
  <Dialog
    :visible="visible"
    title="支付订单"
    :width="dialogWidth"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :custom-class="dialogClass"
    @close="handleClose"
  >
    <div class="payment-dialog">
      <!-- 订单信息 -->
      <div v-if="orderInfo" class="order-info">
        <div class="order-item">
          <span class="label">订单号：</span>
          <span class="value">{{ orderInfo.orderNo }}</span>
        </div>
        <div class="order-item">
          <span class="label">套餐名称：</span>
          <span class="value">{{ orderInfo.packageInfo?.packageName }}</span>
        </div>
        <div class="order-item">
          <span class="label">支付金额：</span>
          <span class="value amount">¥{{ formatNumberWithCommas(orderInfo.actualAmount, 2) }}</span>
        </div>
        <div class="order-item">
          <span class="label">订单状态：</span>
          <span class="value" :class="getStatusClass(orderInfo.orderStatus)">
            {{ orderInfo.orderStatusDesc }}
          </span>
        </div>
      </div>

      <!-- 二维码区域 -->
      <div v-if="orderInfo?.paymentUrl" class="qr-code-section">
        <div class="qr-code-title">请使用支付宝或微信扫码支付</div>
        <div class="qr-code-container">
          <canvas
            ref="qrCodeCanvas"
            class="qr-code"
          />
        </div>
        <div class="qr-code-tips">
          <p>扫码后请在手机上完成支付</p>
          <p>支付完成后页面将自动关闭</p>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-section">
        <i class="el-icon-loading" />
        <span>正在创建订单...</span>
      </div>

      <!-- 错误信息 -->
      <div v-if="error" class="error-section">
        <i class="el-icon-warning" />
        <span>{{ error }}</span>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <Button @click="handleClose">关闭</Button>
        <Button v-if="orderInfo && orderInfo.orderStatus === 'PENDING'" type="primary" @click="handleRefresh">
          刷新状态
        </Button>
      </div>
    </template>
  </Dialog>
</template>

<style lang="scss" scoped>
.payment-dialog {
  .order-info {
    margin-bottom: 20px;

    .order-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .label {
        color: #666;
        font-size: 14px;
      }

      .value {
        color: #333;
        font-size: 14px;
        font-weight: 500;

        &.amount {
          color: #f56c6c;
          font-size: 16px;
          font-weight: 600;
        }

        &.status-success {
          color: #67c23a;
        }

        &.status-warning {
          color: #e6a23c;
        }

        &.status-danger {
          color: #f56c6c;
        }
      }
    }
  }

  .qr-code-section {
    text-align: center;

    .qr-code-title {
      font-size: 16px;
      color: #333;
      margin-bottom: 20px;
      font-weight: 500;
    }

    .qr-code-container {
      display: flex;
      justify-content: center;
      margin-bottom: 20px;

      .qr-code {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        background: white;
      }
    }

    .qr-code-tips {
      color: #666;
      font-size: 12px;
      line-height: 1.5;

      p {
        margin: 4px 0;
      }
    }
  }

  .loading-section,
  .error-section {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
    color: #666;

    i {
      margin-right: 8px;
      font-size: 18px;
    }
  }

  .error-section {
    color: #f56c6c;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 移动端样式
:deep(.payment-dialog-mobile) {
  .el-dialog {
    margin-top: 10vh !important;

    .el-dialog__header {
      padding: 15px;

      .el-dialog__title {
        font-size: 16px;
      }
    }

    .el-dialog__body {
      padding: 15px;
      max-height: 60vh;
      overflow-y: auto;
    }

    .el-dialog__footer {
      padding: 10px 15px;
    }
  }
}

// 移动端样式调整
@media screen and (max-width: 768px) {
  .payment-dialog {
    .order-info {
      .order-item {
        flex-direction: column;
        align-items: flex-start;
        padding: 10px 0;

        .label {
          margin-bottom: 5px;
        }
      }
    }

    .qr-code-section {
      .qr-code-title {
        font-size: 14px;
      }

      .qr-code-container {
        .qr-code {
          max-width: 100%;
        }
      }
    }

    .dialog-footer {
      justify-content: center;

      button {
        flex: 1;
      }
    }
  }
}
</style>
