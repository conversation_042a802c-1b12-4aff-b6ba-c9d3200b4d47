<script setup lang="ts">
import type { RegionConfig, TableRow } from './types'

// Props 定义
interface Props {
  /** 指标标题 */
  indicatorTitle?: string
  /** 地区标题 */
  regionTitle?: string
  /** 地区配置 */
  regions?: RegionConfig[]
  /** 表格数据 */
  tableData?: TableRow[]
}

// 使用 withDefaults 设置默认值
withDefaults(defineProps<Props>(), {
  indicatorTitle: '指标（分）',
  regionTitle: '地区',
  regions: () => [],
  tableData: () => [],
})
</script>

<template>
  <div class="data-table">
    <table>
      <thead>
        <tr>
          <th rowspan="2" class="first-column">{{ indicatorTitle }}</th>
          <th :colspan="regions.length">{{ regionTitle }}</th>
        </tr>
        <tr>
          <th v-for="region in regions" :key="region.key">{{ region.label }}</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(row, index) in tableData" :key="index">
          <td class="first-column">{{ row.name }}</td>
          <td v-for="region in regions" :key="region.key">{{ row[region.key] || '-' }}</td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<style scoped>
.data-table {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', Arial, sans-serif;
  width: 100%;
  margin: 0 auto;
  background: #ffffff;
}

table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #c8d4e6;
  background: #ffffff;
}

th, td {
  padding: 16px 20px;
  text-align: center;
  border: 1px solid #c8d4e6;
  font-size: 14px;
  color: #666666;
  line-height: 1.5;
  height: 50px;
  vertical-align: middle;
}

th {
  background-color: #F7FAFF;
  font-weight: 400;
  color: #666666;
}

.first-column {
  text-align: center;
  font-weight: 400;
  color: #666666;
  width: 150px;
  background-color: #F7FAFF !important;
}
/*
tbody tr:nth-child(even) {
  background-color: #fafbfc;
}

tbody tr:nth-child(odd) {
  background-color: #ffffff;
} */

tbody td.first-column {
  background-color: #f0f4f8;
}
</style>
