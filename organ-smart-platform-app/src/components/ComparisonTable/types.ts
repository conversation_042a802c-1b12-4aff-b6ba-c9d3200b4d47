/**
 * 表格行数据接口
 */
export interface TableRow {
  /** 指标名称 */
  name: string
  /** 动态字段，对应地区的key */
  [key: string]: string
}

/**
 * 地区配置接口
 */
export interface RegionConfig {
  /** 地区字段key */
  key: string
  /** 地区显示标签 */
  label: string
}

/**
 * 对比表格组件 Props
 */
export interface ComparisonTableProps {
  /** 指标标题 */
  indicatorTitle?: string
  /** 地区标题 */
  regionTitle?: string
  /** 地区配置 */
  regions?: RegionConfig[]
  /** 表格数据 */
  tableData?: TableRow[]
}
