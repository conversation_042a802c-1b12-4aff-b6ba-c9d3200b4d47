# ScrollView 组件

ScrollView 组件是参考 uni-app 的 ScrollView 组件实现的滚动容器，支持横向和纵向滚动，下拉刷新等功能。

## 特性

- 🚀 支持横向和纵向滚动
- 🔄 支持下拉刷新
- 📱 支持触摸手势
- 🎯 支持滚动到指定位置
- 📏 支持自定义滚动阈值
- 🎨 支持自定义样式
- 💫 支持滚动动画
- 🔧 完整的 TypeScript 支持

## 基础用法

```vue
<template>
  <ScrollView
    :scroll-y="true"
    :refresher-enabled="true"
    @scroll="handleScroll"
    @refresherrefresh="handleRefresh"
    @scrolltolower="handleScrollToLower"
  >
    <div v-for="item in list" :key="item.id" class="list-item">
      {{ item.name }}
    </div>
  </ScrollView>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ScrollView from '@/components/ScrollView'

const list = ref([
  { id: 1, name: '项目 1' },
  { id: 2, name: '项目 2' },
  // ...
])

function handleScroll(event) {
  console.log('滚动事件:', event.detail)
}

function handleRefresh() {
  // 处理下拉刷新
  setTimeout(() => {
    // 刷新完成后需要手动停止
    scrollViewRef.value?.stopPullRefresh()
  }, 2000)
}

function handleScrollToLower() {
  // 处理滚动到底部
  console.log('滚动到底部')
}
</script>
```

## 横向滚动

```vue
<template>
  <ScrollView
    :scroll-x="true"
    :scroll-y="false"
    style="height: 200px;"
  >
    <div style="display: flex; width: 1000px;">
      <div v-for="item in items" :key="item" class="item">
        {{ item }}
      </div>
    </div>
  </ScrollView>
</template>
```

## 自定义下拉刷新

```vue
<template>
  <ScrollView
    :refresher-enabled="true"
    :refresher-triggered="isRefreshing"
    @refresherrefresh="handleRefresh"
  >
    <template #refresher>
      <div class="custom-refresher">
        <div v-if="isRefreshing" class="loading">
          刷新中...
        </div>
        <div v-else>
          下拉刷新
        </div>
      </div>
    </template>
    
    <!-- 内容 -->
    <div>内容区域</div>
  </ScrollView>
</template>
```

## API

### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| scrollX | boolean | false | 允许横向滚动 |
| scrollY | boolean | true | 允许纵向滚动 |
| upperThreshold | number | 50 | 距顶部/左边多远时触发 scrolltoupper 事件 |
| lowerThreshold | number | 50 | 距底部/右边多远时触发 scrolltolower 事件 |
| scrollTop | number | 0 | 设置竖向滚动条位置 |
| scrollLeft | number | 0 | 设置横向滚动条位置 |
| scrollIntoView | string | '' | 值为某子元素id，滚动到该元素 |
| scrollWithAnimation | boolean | false | 在设置滚动条位置时使用动画过渡 |
| enableBackToTop | boolean | false | iOS点击顶部状态栏时，滚动条返回顶部 |
| enableFlexGrow | boolean | false | 启用 flexbox 布局 |
| scrollAnchoring | boolean | false | 开启 scroll anchoring 特性 |
| refresherEnabled | boolean | false | 开启自定义下拉刷新 |
| refresherThreshold | number | 45 | 设置自定义下拉刷新阈值 |
| refresherDefaultStyle | string | 'black' | 设置下拉刷新默认样式 |
| refresherBackground | string | '#FFF' | 设置下拉刷新区域背景颜色 |
| refresherTriggered | boolean | false | 设置当前下拉刷新状态 |
| enhanced | boolean | false | 启用 enhanced 特性 |
| bounces | boolean | true | 是否开启回弹效果 |
| showScrollbar | boolean | true | 是否显示滚动条 |
| pagingEnabled | boolean | false | 分页滚动 |
| fastDeceleration | boolean | false | 控制快速滚动时的阻尼回弹效果 |

### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| scroll | 滚动时触发 | event: { detail: ScrollDetail } |
| scrolltoupper | 滚动到顶部/左边时触发 | event: { detail: { direction } } |
| scrolltolower | 滚动到底部/右边时触发 | event: { detail: { direction } } |
| refresherpulling | 下拉刷新控件被下拉 | event: { detail: { deltaY } } |
| refresherrefresh | 下拉刷新被触发 | - |
| refresherrestore | 下拉刷新被复位 | - |
| refresherabort | 下拉刷新被中止 | - |

### Methods

| 方法名 | 说明 | 参数 |
|--------|------|------|
| scrollTo | 滚动到指定位置 | options: { top?, left?, animated? } |
| scrollIntoView | 滚动到指定元素 | selector: string |
| stopPullRefresh | 停止下拉刷新 | - |
| getScrollInfo | 获取滚动信息 | - |

### Slots

| 插槽名 | 说明 |
|--------|------|
| default | 默认插槽，滚动内容 |
| refresher | 自定义下拉刷新指示器 |

## 注意事项

1. 使用横向滚动时，需要确保内容宽度大于容器宽度
2. 下拉刷新需要手动调用 `stopPullRefresh()` 方法来停止刷新状态
3. 在移动端使用时，建议设置 `touch-action: none` 来防止默认的滚动行为
4. 如果需要监听滚动事件，建议使用节流来优化性能
