/**
 * ScrollView 组件相关类型定义
 */

/**
 * 滚动事件详情
 */
export interface ScrollDetail {
  /** 垂直滚动条位置 */
  scrollTop: number;
  /** 水平滚动条位置 */
  scrollLeft: number;
  /** 滚动区域的高度 */
  scrollHeight: number;
  /** 滚动区域的宽度 */
  scrollWidth: number;
  /** 水平滚动增量 */
  deltaX: number;
  /** 垂直滚动增量 */
  deltaY: number;
}

/**
 * 滚动到顶部/底部事件详情
 */
export interface ScrollToEdgeDetail {
  /** 滚动方向 */
  direction: "top" | "bottom" | "left" | "right";
}

/**
 * 下拉刷新拖拽事件详情
 */
export interface RefresherPullingDetail {
  /** 下拉距离 */
  deltaY: number;
}

/**
 * ScrollView 组件属性
 */
export interface ScrollViewProps {
  /** 允许横向滚动 */
  scrollX?: boolean;
  /** 允许纵向滚动 */
  scrollY?: boolean;
  /** 距顶部/左边多远时，触发 scrolltoupper 事件 */
  upperThreshold?: number;
  /** 距底部/右边多远时，触发 scrolltolower 事件 */
  lowerThreshold?: number;
  /** 设置竖向滚动条位置 */
  scrollTop?: number;
  /** 设置横向滚动条位置 */
  scrollLeft?: number;
  /** 值应为某子元素id（id不能以数字开头）。设置哪个方向可滚动，则在哪个方向滚动到该元素 */
  scrollIntoView?: string;
  /** 在设置滚动条位置时使用动画过渡 */
  scrollWithAnimation?: boolean;
  /** iOS点击顶部状态栏、安卓双击标题栏时，滚动条返回顶部，只支持竖向 */
  enableBackToTop?: boolean;
  /** 启用 flexbox 布局。开启后，当前节点声明了 display: flex 就会成为 flex container，并作用于其孩子节点 */
  enableFlexGrow?: boolean;
  /** 开启 scroll anchoring 特性，即控制滚动位置不随内容变化而抖动，仅在 iOS 下生效，安卓下可参考 CSS overflow-anchor 属性 */
  scrollAnchoring?: boolean;
  /** 开启自定义下拉刷新 */
  refresherEnabled?: boolean;
  /** 设置自定义下拉刷新阈值 */
  refresherThreshold?: number;
  /** 设置自定义下拉刷新默认样式，支持设置 black | white | none， none 表示不使用默认样式 */
  refresherDefaultStyle?: "black" | "white" | "none";
  /** 设置自定义下拉刷新区域背景颜色 */
  refresherBackground?: string;
  /** 设置当前下拉刷新状态，true 表示下拉刷新已经被触发，false 表示下拉刷新未被触发 */
  refresherTriggered?: boolean;
  /** 启用 enhanced 特性 */
  enhanced?: boolean;
  /** 是否开启回弹效果 */
  bounces?: boolean;
  /** 是否显示滚动条 */
  showScrollbar?: boolean;
  /** 分页滚动，仅支持竖向 */
  pagingEnabled?: boolean;
  /** 控制是否出现快速滚动时的阻尼回弹效果 */
  fastDeceleration?: boolean;
  /** 下拉刷新时是否禁用触摸事件 */
  refreshingDisableTouch?: boolean;
  /** 是否显示下拉刷新文本，默认false（不显示） */
  showRefresherText?: boolean;
}

/**
 * ScrollView 组件事件
 */
export interface ScrollViewEmits {
  /** 滚动时触发 */
  (e: "scroll", event: { detail: ScrollDetail }): void;
  /** 滚动到顶部/左边时触发 */
  (e: "scrolltoupper", event: { detail: ScrollToEdgeDetail }): void;
  /** 滚动到底部/右边时触发 */
  (e: "scrolltolower", event: { detail: ScrollToEdgeDetail }): void;
  /** 自定义下拉刷新控件被下拉 */
  (e: "refresherpulling", event: { detail: RefresherPullingDetail }): void;
  /** 自定义下拉刷新被触发 */
  (e: "refresherrefresh"): void;
  /** 自定义下拉刷新被复位 */
  (e: "refresherrestore"): void;
  /** 自定义下拉刷新被中止 */
  (e: "refresherabort"): void;
}

/**
 * 滚动选项
 */
export interface ScrollToOptions {
  /** 滚动到的垂直位置 */
  top?: number;
  /** 滚动到的水平位置 */
  left?: number;
  /** 是否使用动画 */
  animated?: boolean;
}

/**
 * 滚动信息
 */
export interface ScrollInfo {
  /** 垂直滚动条位置 */
  scrollTop: number;
  /** 水平滚动条位置 */
  scrollLeft: number;
  /** 滚动区域的高度 */
  scrollHeight: number;
  /** 滚动区域的宽度 */
  scrollWidth: number;
  /** 可视区域高度 */
  clientHeight: number;
  /** 可视区域宽度 */
  clientWidth: number;
}

/**
 * ScrollView 组件实例方法
 */
export interface ScrollViewInstance {
  /** 滚动到指定位置 */
  scrollTo: (options: ScrollToOptions) => void;
  /** 滚动到指定元素 */
  scrollIntoView: (selector: string) => void;
  /** 停止下拉刷新 */
  stopPullRefresh: () => void;
  /** 获取滚动信息 */
  getScrollInfo: () => ScrollInfo;
}
