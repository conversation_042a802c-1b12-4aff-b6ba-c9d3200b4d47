<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from "vue";
import { deviceUtils } from "@/utils/device";

/**
 * ScrollView 组件 - 参考 uni-app ScrollView 实现
 * 支持横向和纵向滚动，下拉刷新，上拉加载等功能
 */

// 定义组件属性接口
interface Props {
  /** 允许横向滚动 */
  scrollX?: boolean;
  /** 允许纵向滚动 */
  scrollY?: boolean;
  /** 距顶部/左边多远时，触发 scrolltoupper 事件 */
  upperThreshold?: number;
  /** 距底部/右边多远时，触发 scrolltolower 事件 */
  lowerThreshold?: number;
  /** 设置竖向滚动条位置 */
  scrollTop?: number;
  /** 设置横向滚动条位置 */
  scrollLeft?: number;
  /** 值应为某子元素id，设置哪个方向可滚动，则在哪个方向滚动到该元素 */
  scrollIntoView?: string;
  /** 在设置滚动条位置时使用动画过渡 */
  scrollWithAnimation?: boolean;
  /** iOS点击顶部状态栏、安卓双击标题栏时，滚动条返回顶部，只支持竖向 */
  enableBackToTop?: boolean;
  /** 启用 flexbox 布局 */
  enableFlexGrow?: boolean;
  /** 开启 scroll anchoring 特性 */
  scrollAnchoring?: boolean;
  /** 开启自定义下拉刷新 */
  refresherEnabled?: boolean;
  /** 设置自定义下拉刷新阈值 */
  refresherThreshold?: number;
  /** 设置自定义下拉刷新默认样式 */
  refresherDefaultStyle?: "black" | "white" | "none";
  /** 设置自定义下拉刷新区域背景颜色 */
  refresherBackground?: string;
  /** 设置当前下拉刷新状态 */
  refresherTriggered?: boolean;
  /** 启用 enhanced 特性 */
  enhanced?: boolean;
  /** 是否开启回弹效果 */
  bounces?: boolean;
  /** 是否显示滚动条 */
  showScrollbar?: boolean;
  /** 分页滚动，仅支持竖向 */
  pagingEnabled?: boolean;
  /** 控制是否出现快速滚动时的阻尼回弹效果 */
  fastDeceleration?: boolean;
  /** 下拉刷新时是否禁用触摸事件 */
  refreshingDisableTouch?: boolean;
  /** 是否显示下拉刷新文本，默认false（不显示） */
  showRefresherText?: boolean;
}

// 定义组件事件接口
interface Emits {
  /** 滚动时触发 */
  (e: "scroll", event: { detail: any }): void;
  /** 滚动到顶部/左边时触发 */
  (e: "scrolltoupper", event: { detail: { direction: string } }): void;
  /** 滚动到底部/右边时触发 */
  (e: "scrolltolower", event: { detail: { direction: string } }): void;
  /** 自定义下拉刷新控件被下拉 */
  (e: "refresherpulling", event: { detail: { deltaY: number } }): void;
  /** 自定义下拉刷新被触发 */
  (e: "refresherrefresh"): void;
  /** 自定义下拉刷新被复位 */
  (e: "refresherrestore"): void;
  /** 自定义下拉刷新被中止 */
  (e: "refresherabort"): void;
}

// 定义组件实例方法接口
interface ScrollViewInstance {
  /** 滚动到指定位置 */
  scrollTo: (options: {
    top?: number;
    left?: number;
    animated?: boolean;
  }) => void;
  /** 滚动到指定元素 */
  scrollIntoView: (selector: string) => void;
  /** 停止下拉刷新 */
  stopPullRefresh: () => void;
  /** 获取滚动信息 */
  getScrollInfo: () => any;
}

const props = withDefaults(defineProps<Props>(), {
  scrollX: false,
  scrollY: true,
  upperThreshold: 50,
  lowerThreshold: 50,
  scrollTop: 0,
  scrollLeft: 0,
  scrollIntoView: "",
  scrollWithAnimation: false,
  enableBackToTop: false,
  enableFlexGrow: false,
  scrollAnchoring: false,
  refresherEnabled: false,
  refresherThreshold: 45,
  refresherDefaultStyle: "black",
  refresherBackground: "#FFF",
  refresherTriggered: false,
  enhanced: false,
  bounces: true,
  showScrollbar: deviceUtils.isPC(), // PC端默认显示，移动端默认不显示
  pagingEnabled: false,
  fastDeceleration: false,
  refreshingDisableTouch: false,
  showRefresherText: false,
});

const emit = defineEmits<Emits>();

// 组件引用
const scrollViewRef = ref<HTMLElement>();
const scrollContentRef = ref<HTMLElement>();
const refresherRef = ref<HTMLElement>();

// 状态管理
const isRefreshing = ref(false);
const refresherHeight = ref(0);
const startY = ref(0);
const currentY = ref(0);
const isDragging = ref(false);
const scrollInfo = ref({
  scrollTop: 0,
  scrollLeft: 0,
  scrollHeight: 0,
  scrollWidth: 0,
  clientHeight: 0,
  clientWidth: 0,
});

// 节流控制
let scrollTimer: number | null = null;
let upperTimer: number | null = null;
let lowerTimer: number | null = null;

/**
 * 计算样式
 */
const scrollViewStyle = computed(() => {
  const style: Record<string, any> = {
    position: "relative",
    overflow: "hidden",
    height: "100%",
    width: "100%",
  };

  if (!props.bounces) {
    style.overscrollBehavior = "none";
  }

  return style;
});

const scrollContentStyle = computed(() => {
  const style: Record<string, any> = {
    height: "100%",
    width: "100%",
    overflowX: props.scrollX ? "auto" : "hidden",
    overflowY: props.scrollY ? "auto" : "hidden",
    WebkitOverflowScrolling: "touch",
  };

  if (!props.showScrollbar) {
    style.scrollbarWidth = "none";
    style.msOverflowStyle = "none";
    style["-webkit-scrollbar"] = "none";
  }

  if (props.enableFlexGrow) {
    style.display = "flex";
    style.flexDirection = "column";
  }

  if (props.pagingEnabled) {
    style.scrollSnapType = props.scrollX ? "x mandatory" : "y mandatory";
  }

  return style;
});

const refresherStyle = computed(() => {
  return {
    position: "absolute" as const,
    top: `-${refresherHeight.value}px`,
    left: 0,
    right: 0,
    height: `${refresherHeight.value}px`,
    background: props.refresherBackground,
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    transition: isDragging.value ? "none" : "transform 0.3s ease",
    transform: `translateY(${
      isRefreshing.value ? refresherHeight.value : 0
    }px)`,
    zIndex: 1,
  };
});

/**
 * 处理滚动事件
 */
function handleScroll(event: Event) {
  const target = event.target as HTMLElement;
  const {
    scrollTop,
    scrollLeft,
    scrollHeight,
    scrollWidth,
    clientHeight,
    clientWidth,
  } = target;

  // 更新滚动信息
  scrollInfo.value = {
    scrollTop,
    scrollLeft,
    scrollHeight,
    scrollWidth,
    clientHeight,
    clientWidth,
  };

  // 节流处理滚动事件
  if (scrollTimer) {
    clearTimeout(scrollTimer);
  }
  scrollTimer = window.setTimeout(() => {
    emit("scroll", {
      detail: {
        scrollTop,
        scrollLeft,
        scrollHeight,
        scrollWidth,
        deltaX: 0,
        deltaY: 0,
      },
    });
  }, 16); // 约60fps

  // 检查是否触顶
  if (scrollTop <= props.upperThreshold) {
    if (upperTimer) {
      clearTimeout(upperTimer);
    }
    upperTimer = window.setTimeout(() => {
      emit("scrolltoupper", {
        detail: {
          direction: "top",
        },
      });
    }, 50);
  }

  // 检查是否触底
  const distanceToBottom = scrollHeight - scrollTop - clientHeight;
  if (distanceToBottom <= props.lowerThreshold) {
    if (lowerTimer) {
      clearTimeout(lowerTimer);
    }
    lowerTimer = window.setTimeout(() => {
      emit("scrolltolower", {
        detail: {
          direction: "bottom",
        },
      });
    }, 50);
  }
}

/**
 * 处理触摸开始事件
 */
function handleTouchStart(event: TouchEvent) {
  if (!props.refresherEnabled || scrollInfo.value.scrollTop > 0) {
    return;
  }

  // 如果正在刷新且禁用触摸，则阻止事件
  if (isRefreshing.value && props.refreshingDisableTouch) {
    event.preventDefault();
    event.stopPropagation();
    return;
  }

  startY.value = event.touches[0].clientY;
  isDragging.value = true;
}

/**
 * 处理触摸移动事件
 */
function handleTouchMove(event: TouchEvent) {
  if (
    !props.refresherEnabled ||
    !isDragging.value ||
    scrollInfo.value.scrollTop > 0
  ) {
    return;
  }

  // 如果正在刷新且禁用触摸，则阻止事件
  if (isRefreshing.value && props.refreshingDisableTouch) {
    event.preventDefault();
    event.stopPropagation();
    return;
  }

  currentY.value = event.touches[0].clientY;
  const deltaY = currentY.value - startY.value;

  if (deltaY > 0) {
    event.preventDefault();
    const pullDistance = Math.min(deltaY * 0.5, props.refresherThreshold * 2);
    refresherHeight.value = pullDistance;

    if (pullDistance >= props.refresherThreshold && !isRefreshing.value) {
      emit("refresherpulling", {
        detail: {
          deltaY: pullDistance,
        },
      });
    }
  }
}

/**
 * 处理触摸结束事件
 */
function handleTouchEnd() {
  if (!props.refresherEnabled || !isDragging.value) {
    return;
  }

  isDragging.value = false;

  if (
    refresherHeight.value >= props.refresherThreshold &&
    !isRefreshing.value
  ) {
    // 触发刷新
    isRefreshing.value = true;
    refresherHeight.value = props.refresherThreshold;
    emit("refresherrefresh");
  } else {
    // 回弹
    refresherHeight.value = 0;
  }
}

/**
 * 滚动到指定位置
 */
function scrollTo(options: {
  top?: number;
  left?: number;
  animated?: boolean;
}) {
  const element = scrollContentRef.value;
  if (!element) return;

  const { top = 0, left = 0, animated = props.scrollWithAnimation } = options;

  if (animated) {
    element.scrollTo({
      top,
      left,
      behavior: "smooth",
    });
  } else {
    element.scrollTop = top;
    element.scrollLeft = left;
  }
}

/**
 * 滚动到指定元素
 */
function scrollIntoView(selector: string) {
  if (!selector || !scrollContentRef.value) return;

  const element = scrollContentRef.value.querySelector(selector);
  if (element) {
    element.scrollIntoView({
      behavior: props.scrollWithAnimation ? "smooth" : "auto",
      block: "start",
    });
  }
}

/**
 * 停止下拉刷新
 */
function stopPullRefresh() {
  isRefreshing.value = false;
  refresherHeight.value = 0;
}

/**
 * 获取滚动信息
 */
function getScrollInfo() {
  return { ...scrollInfo.value };
}

// 监听 props 变化
watch(
  () => props.scrollTop,
  (newVal) => {
    if (newVal !== scrollInfo.value.scrollTop) {
      scrollTo({ top: newVal });
    }
  }
);

watch(
  () => props.scrollLeft,
  (newVal) => {
    if (newVal !== scrollInfo.value.scrollLeft) {
      scrollTo({ left: newVal });
    }
  }
);

watch(
  () => props.scrollIntoView,
  (newVal) => {
    if (newVal) {
      nextTick(() => {
        scrollIntoView(newVal);
      });
    }
  }
);

watch(
  () => props.refresherTriggered,
  (newVal) => {
    if (!newVal && isRefreshing.value) {
      stopPullRefresh();
    }
  }
);

// 组件挂载
onMounted(() => {
  const element = scrollContentRef.value;
  if (element) {
    element.addEventListener("scroll", handleScroll, { passive: true });

    if (props.refresherEnabled) {
      element.addEventListener("touchstart", handleTouchStart, {
        passive: true,
      });
      element.addEventListener("touchmove", handleTouchMove, {
        passive: false,
      });
      element.addEventListener("touchend", handleTouchEnd, { passive: true });
    }

    // 初始化滚动位置
    if (props.scrollTop > 0 || props.scrollLeft > 0) {
      nextTick(() => {
        scrollTo({ top: props.scrollTop, left: props.scrollLeft });
      });
    }
  }
});

// 组件卸载
onUnmounted(() => {
  const element = scrollContentRef.value;
  if (element) {
    element.removeEventListener("scroll", handleScroll);
    element.removeEventListener("touchstart", handleTouchStart);
    element.removeEventListener("touchmove", handleTouchMove);
    element.removeEventListener("touchend", handleTouchEnd);
  }

  if (scrollTimer) clearTimeout(scrollTimer);
  if (upperTimer) clearTimeout(upperTimer);
  if (lowerTimer) clearTimeout(lowerTimer);
});

// 暴露方法
defineExpose({
  scrollTo,
  scrollIntoView,
  stopPullRefresh,
  getScrollInfo,
});
</script>

<template>
  <div ref="scrollViewRef" class="scroll-view" :style="scrollViewStyle">
    <!-- 下拉刷新指示器 -->
    <div
      v-if="refresherEnabled"
      ref="refresherRef"
      class="scroll-view__refresher"
      :style="refresherStyle"
    >
      <slot name="refresher">
        <div class="scroll-view__refresher-default">
          <!-- 只在刷新时显示loading，或者设置了显示文本时显示 -->
          <div
            v-if="isRefreshing || showRefresherText"
            class="scroll-view__refresher-content"
          >
            <!-- 刷新中显示loading图标 -->
            <div v-if="isRefreshing" class="scroll-view__refresher-loading">
              <div class="scroll-view__spinner"></div>
              <span class="scroll-view__text">刷新中...</span>
            </div>
            <!-- 非刷新状态且设置显示文本时显示下拉提示 -->
            <div
              v-else-if="showRefresherText && !isRefreshing"
              class="scroll-view__refresher-text"
            >
              下拉刷新
            </div>
          </div>
        </div>
      </slot>
    </div>

    <!-- 滚动内容 -->
    <div
      ref="scrollContentRef"
      class="scroll-view__content"
      :style="scrollContentStyle"
    >
      <slot />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.scroll-view {
  position: relative;
  overflow: hidden;

  &__refresher {
    &-default {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: #666;
      font-size: 14px;
    }

    &-content {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
    }

    &-loading {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }

    &-text {
      color: #666;
      font-size: 14px;
    }

    &-icon {
      font-size: 18px;
      transition: transform 0.3s ease;

      &.is-refreshing {
        animation: rotate 1s linear infinite;
      }
    }
  }

  &__spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #e5e5e5;
    border-top: 2px solid #666;
    border-radius: 50%;
    animation: rotate 1s linear infinite;
  }

  &__text {
    color: #666;
    font-size: 14px;
  }

  &__content {
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.2);
      border-radius: 3px;

      &:hover {
        background: rgba(0, 0, 0, 0.3);
      }
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
