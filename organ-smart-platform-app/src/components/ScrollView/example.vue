<template>
  <div class="scroll-view-example">
    <h2>ScrollView 组件示例</h2>
    
    <!-- 基础滚动示例 -->
    <div class="example-section">
      <h3>基础滚动</h3>
      <div class="scroll-container">
        <ScrollView
          :scroll-y="true"
          :lower-threshold="50"
          @scroll="handleScroll"
          @scrolltolower="handleScrollToLower"
        >
          <div v-for="item in basicList" :key="item" class="list-item">
            基础列表项 {{ item }}
          </div>
        </ScrollView>
      </div>
    </div>

    <!-- 下拉刷新示例 -->
    <div class="example-section">
      <h3>下拉刷新</h3>
      <div class="scroll-container">
        <ScrollView
          ref="refreshScrollRef"
          :scroll-y="true"
          :refresher-enabled="true"
          :refresher-triggered="isRefreshing"
          @refresherrefresh="handleRefresh"
        >
          <div v-for="item in refreshList" :key="item.id" class="list-item">
            {{ item.name }} - {{ item.time }}
          </div>
        </ScrollView>
      </div>
    </div>

    <!-- 横向滚动示例 -->
    <div class="example-section">
      <h3>横向滚动</h3>
      <div class="scroll-container horizontal">
        <ScrollView
          :scroll-x="true"
          :scroll-y="false"
        >
          <div class="horizontal-content">
            <div v-for="item in horizontalList" :key="item" class="horizontal-item">
              横向项目 {{ item }}
            </div>
          </div>
        </ScrollView>
      </div>
    </div>

    <!-- 控制按钮 -->
    <div class="controls">
      <button @click="scrollToTop">滚动到顶部</button>
      <button @click="scrollToBottom">滚动到底部</button>
      <button @click="addItems">添加项目</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import ScrollView from './index.vue'
import type { ScrollViewInstance } from './types'

// 组件引用
const refreshScrollRef = ref<ScrollViewInstance>()

// 数据
const basicList = ref<number[]>([])
const refreshList = ref<Array<{ id: number; name: string; time: string }>>([])
const horizontalList = ref<number[]>([])
const isRefreshing = ref(false)

/**
 * 初始化数据
 */
function initData() {
  // 基础列表
  basicList.value = Array.from({ length: 50 }, (_, i) => i + 1)
  
  // 刷新列表
  refreshList.value = Array.from({ length: 20 }, (_, i) => ({
    id: i + 1,
    name: `刷新项目 ${i + 1}`,
    time: new Date().toLocaleTimeString(),
  }))
  
  // 横向列表
  horizontalList.value = Array.from({ length: 20 }, (_, i) => i + 1)
}

/**
 * 处理滚动事件
 */
function handleScroll(event: any) {
  console.log('滚动事件:', event.detail)
}

/**
 * 处理滚动到底部
 */
function handleScrollToLower() {
  console.log('滚动到底部')
  // 模拟加载更多
  const currentLength = basicList.value.length
  const newItems = Array.from({ length: 10 }, (_, i) => currentLength + i + 1)
  basicList.value.push(...newItems)
}

/**
 * 处理下拉刷新
 */
function handleRefresh() {
  console.log('开始刷新')
  isRefreshing.value = true
  
  // 模拟刷新数据
  setTimeout(() => {
    refreshList.value = Array.from({ length: 20 }, (_, i) => ({
      id: i + 1,
      name: `刷新项目 ${i + 1}`,
      time: new Date().toLocaleTimeString(),
    }))
    
    isRefreshing.value = false
    refreshScrollRef.value?.stopPullRefresh()
  }, 2000)
}

/**
 * 滚动到顶部
 */
function scrollToTop() {
  refreshScrollRef.value?.scrollTo({ top: 0, animated: true })
}

/**
 * 滚动到底部
 */
function scrollToBottom() {
  const scrollInfo = refreshScrollRef.value?.getScrollInfo()
  if (scrollInfo) {
    refreshScrollRef.value?.scrollTo({
      top: scrollInfo.scrollHeight - scrollInfo.clientHeight,
      animated: true,
    })
  }
}

/**
 * 添加项目
 */
function addItems() {
  const currentLength = refreshList.value.length
  const newItems = Array.from({ length: 5 }, (_, i) => ({
    id: currentLength + i + 1,
    name: `新增项目 ${currentLength + i + 1}`,
    time: new Date().toLocaleTimeString(),
  }))
  refreshList.value.push(...newItems)
}

// 组件挂载时初始化数据
onMounted(() => {
  initData()
})
</script>

<style lang="scss" scoped>
.scroll-view-example {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;

  h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
  }

  .example-section {
    margin-bottom: 40px;

    h3 {
      margin-bottom: 15px;
      color: #666;
      font-size: 18px;
    }
  }

  .scroll-container {
    height: 300px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;

    &.horizontal {
      height: 120px;
    }
  }

  .list-item {
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
    background: #fff;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f8f9fa;
    }

    &:last-child {
      border-bottom: none;
    }
  }

  .horizontal-content {
    display: flex;
    height: 100%;
    width: max-content;
  }

  .horizontal-item {
    min-width: 150px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-right: 1px solid #f0f0f0;
    background: #fff;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f8f9fa;
    }

    &:last-child {
      border-right: none;
    }
  }

  .controls {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 30px;

    button {
      padding: 10px 20px;
      border: 1px solid #409eff;
      background: #409eff;
      color: white;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: #66b1ff;
        border-color: #66b1ff;
      }

      &:active {
        background: #3a8ee6;
        border-color: #3a8ee6;
      }
    }
  }
}
</style>
