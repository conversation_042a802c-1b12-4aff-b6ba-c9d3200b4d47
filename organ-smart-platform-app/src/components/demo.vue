<template>
  <div class="demo">
    <h1>ScrollView 和 AutoLoad 组件演示</h1>
    
    <!-- ScrollView 演示 -->
    <div class="demo-section">
      <h2>ScrollView 演示</h2>
      <div class="scroll-demo">
        <ScrollView :scroll-y="true" style="height: 300px;">
          <div v-for="i in 50" :key="i" class="item">
            ScrollView 项目 {{ i }}
          </div>
        </ScrollView>
      </div>
    </div>

    <!-- AutoLoad 演示 -->
    <div class="demo-section">
      <h2>AutoLoad 演示</h2>
      <div class="auto-demo">
        <AutoLoad
          :loading="loading"
          :has-more="hasMore"
          @load-more="loadMore"
          style="height: 300px;"
        >
          <div v-for="item in list" :key="item.id" class="item">
            <h3>{{ item.title }}</h3>
            <p>{{ item.description }}</p>
          </div>
        </AutoLoad>
      </div>
    </div>

    <!-- Hook 演示 -->
    <div class="demo-section">
      <h2>useAutoLoad Hook 演示</h2>
      <div class="hook-demo">
        <AutoLoad
          :loading="hookLoading"
          :refreshing="hookRefreshing"
          :has-more="hookHasMore"
          @load-more="hookLoadMore"
          @refresh="hookRefresh"
          style="height: 300px;"
        >
          <div v-for="item in hookData" :key="item.id" class="item">
            <h3>{{ item.title }}</h3>
            <p>{{ item.description }}</p>
          </div>
        </AutoLoad>
      </div>
      <button @click="hookRefresh" style="margin-top: 10px;">刷新数据</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ScrollView from './ScrollView/index.vue'
import AutoLoad from './AutoLoad/index.vue'
import { useAutoLoad } from './AutoLoad/useAutoLoad'

// AutoLoad 基础演示
const list = ref([
  { id: 1, title: '项目 1', description: '这是第一个项目的描述' },
  { id: 2, title: '项目 2', description: '这是第二个项目的描述' },
])
const loading = ref(false)
const hasMore = ref(true)
let currentPage = 1

async function loadMore() {
  if (loading.value) return
  
  loading.value = true
  
  // 模拟 API 请求
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  currentPage++
  const newItems = Array.from({ length: 5 }, (_, i) => ({
    id: (currentPage - 1) * 5 + i + 1,
    title: `项目 ${(currentPage - 1) * 5 + i + 1}`,
    description: `这是第 ${currentPage} 页第 ${i + 1} 个项目的描述`,
  }))
  
  list.value.push(...newItems)
  
  // 模拟没有更多数据
  if (currentPage >= 5) {
    hasMore.value = false
  }
  
  loading.value = false
}

// Hook 演示
const {
  data: hookData,
  loading: hookLoading,
  refreshing: hookRefreshing,
  hasMore: hookHasMore,
  loadMore: hookLoadMore,
  refresh: hookRefresh,
} = useAutoLoad({
  pageSize: 5,
  immediate: true,
  loadData: async (page, pageSize) => {
    // 模拟 API 请求
    await new Promise(resolve => setTimeout(resolve, 800))
    
    const startId = (page - 1) * pageSize + 1
    const data = Array.from({ length: pageSize }, (_, i) => ({
      id: startId + i,
      title: `Hook 项目 ${startId + i}`,
      description: `这是通过 Hook 管理的第 ${page} 页第 ${i + 1} 个项目`,
    }))

    return {
      data,
      total: 30, // 模拟总数
    }
  },
  onError: (error) => {
    console.error('加载失败:', error)
  },
})
</script>

<style lang="scss" scoped>
.demo {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;

  h1 {
    text-align: center;
    color: #333;
    margin-bottom: 40px;
  }

  .demo-section {
    margin-bottom: 40px;

    h2 {
      color: #666;
      margin-bottom: 20px;
      font-size: 20px;
    }
  }

  .scroll-demo,
  .auto-demo,
  .hook-demo {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
  }

  .item {
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
    background: #fff;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f8f9fa;
    }

    &:last-child {
      border-bottom: none;
    }

    h3 {
      margin: 0 0 8px 0;
      color: #333;
      font-size: 16px;
    }

    p {
      margin: 0;
      color: #666;
      font-size: 14px;
      line-height: 1.4;
    }
  }

  button {
    padding: 10px 20px;
    border: 1px solid #409eff;
    background: #409eff;
    color: white;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: #66b1ff;
      border-color: #66b1ff;
    }

    &:active {
      background: #3a8ee6;
      border-color: #3a8ee6;
    }
  }
}
</style>
