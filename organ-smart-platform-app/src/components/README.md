# 组件库

本项目新增了两个核心组件：ScrollView 和 AutoLoad，用于提升移动端和桌面端的滚动体验。

## 🚀 新增组件

### ScrollView 组件

参考 uni-app 的 ScrollView 组件实现，提供了完整的滚动功能：

- ✅ 支持横向和纵向滚动
- ✅ 支持下拉刷新
- ✅ 支持触摸手势
- ✅ 支持滚动到指定位置
- ✅ 支持自定义滚动阈值
- ✅ 支持滚动动画
- ✅ 完整的 TypeScript 支持

**位置**: `src/components/ScrollView/`

**主要文件**:

- `index.vue` - 组件主文件
- `types.ts` - TypeScript 类型定义
- `index.ts` - 导出文件
- `README.md` - 详细使用文档
- `example.vue` - 使用示例

### AutoLoad 组件

基于 ScrollView 组件实现的自动加载列表组件：

- ✅ 支持下拉刷新
- ✅ 支持上拉加载更多
- ✅ 自动管理加载状态
- ✅ 支持空状态显示
- ✅ 支持错误状态处理
- ✅ 支持自定义状态样式
- ✅ 提供数据管理 Hook
- ✅ 完整的 TypeScript 支持

**位置**: `src/components/AutoLoad/`

**主要文件**:

- `index.vue` - 组件主文件
- `types.ts` - TypeScript 类型定义
- `useAutoLoad.ts` - 数据管理 Hook
- `index.ts` - 导出文件
- `README.md` - 详细使用文档
- `example.vue` - 使用示例

## 📦 技术栈适配

组件完全适配当前项目的技术栈：

- **Vue 2.7** - 使用 Composition API
- **TypeScript** - 完整的类型支持
- **Element UI** - 样式风格保持一致
- **Webpack** - 支持模块化导入

## 🔧 安装和使用

### 1. 按需导入

```vue
<template>
  <ScrollView :scroll-y="true" @scroll="handleScroll">
    <div>内容</div>
  </ScrollView>
</template>

<script setup lang="ts">
import ScrollView from "@/components/ScrollView";

function handleScroll(event) {
  console.log("滚动事件:", event.detail);
}
</script>
```

### 2. 全局注册（推荐）

在 `main.ts` 中注册：

```typescript
import { registerGlobalComponents } from "@/components";

// 注册全局组件
registerGlobalComponents(Vue);
```

然后在任何组件中直接使用：

```vue
<template>
  <AutoLoad
    :loading="loading"
    :has-more="hasMore"
    @load-more="loadMore"
    @refresh="refresh"
  >
    <div v-for="item in list" :key="item.id">
      {{ item.name }}
    </div>
  </AutoLoad>
</template>
```

### 3. 使用 Hook 简化开发

```vue
<template>
  <AutoLoad
    :loading="loading"
    :refreshing="refreshing"
    :has-more="hasMore"
    :show-empty="isEmpty"
    @load-more="loadMore"
    @refresh="refresh"
  >
    <div v-for="item in data" :key="item.id">
      {{ item.name }}
    </div>
  </AutoLoad>
</template>

<script setup lang="ts">
import { useAutoLoad } from "@/components/AutoLoad";

const { data, loading, refreshing, hasMore, isEmpty, loadMore, refresh } =
  useAutoLoad({
    pageSize: 20,
    loadData: async (page, pageSize) => {
      const response = await api.getList({ page, pageSize });
      return {
        data: response.data,
        total: response.total,
      };
    },
  });
</script>
```

## 🧪 测试页面

创建了完整的测试页面来验证组件功能：

**位置**: `src/views/test-components.vue`

包含以下测试场景：

- ScrollView 基础滚动功能
- ScrollView 下拉刷新功能
- ScrollView 横向滚动功能
- AutoLoad 基础用法
- AutoLoad Hook 用法
- 自定义样式和状态

## 📚 文档

每个组件都提供了详细的文档：

- **API 文档** - 完整的属性、事件、方法说明
- **使用示例** - 多种使用场景的代码示例
- **类型定义** - TypeScript 类型支持
- **注意事项** - 使用时的注意点和最佳实践

## 🔄 迁移指南

### 从现有 CommonScroll 迁移到 ScrollView

```vue
<!-- 旧的 CommonScroll -->
<CommonScroll @reach-bottom="loadMore">
  <div>内容</div>
</CommonScroll>

<!-- 新的 ScrollView -->
<ScrollView @scrolltolower="loadMore">
  <div>内容</div>
</ScrollView>
```

### 从手动管理列表到 AutoLoad

```vue
<!-- 手动管理 -->
<template>
  <div>
    <div v-for="item in list" :key="item.id">{{ item.name }}</div>
    <div v-if="loading">加载中...</div>
  </div>
</template>

<!-- 使用 AutoLoad -->
<template>
  <AutoLoad :loading="loading" @load-more="loadMore">
    <div v-for="item in list" :key="item.id">{{ item.name }}</div>
  </AutoLoad>
</template>
```

## 🎯 最佳实践

1. **性能优化**: 使用 `v-memo` 或 `v-once` 优化大列表渲染
2. **错误处理**: 在 `useAutoLoad` 中提供 `onError` 回调
3. **用户体验**: 合理设置 `threshold` 和 `throttleDelay` 参数
4. **样式定制**: 使用插槽自定义加载状态和空状态
5. **类型安全**: 充分利用 TypeScript 类型定义

## 🐛 问题反馈

如果在使用过程中遇到问题，请检查：

1. Vue 版本是否为 2.7+
2. TypeScript 配置是否正确
3. 组件导入路径是否正确
4. 是否正确处理异步操作

## 🔮 未来计划

- [ ] 支持虚拟滚动优化大列表性能
- [ ] 添加更多滚动动画效果
- [ ] 支持更多自定义配置选项
- [ ] 添加单元测试覆盖
- [ ] 性能监控和优化建议
