<script setup lang="ts">
import { Empty } from 'element-ui'
import CommonScroll from '@/components/CommonScroll/index.vue'
import { useResettableRef } from '@/hooks/useResettable'
import { getCssUnit } from '@/utils/css'

interface ListResponseData extends PaginatedResponseData {
  pageNo: number
  pageSize: number
}

/**
 * 表格列配置接口
 */
interface TableColumn {
  /** 列标题 */
  label: string
  /** 数据字段名 */
  prop: string
  /** 对齐方式 */
  align?: 'left' | 'center' | 'right'
  /** 自定义渲染函数 */
  formatter?: (row: any, column: TableColumn, cellValue: any, index: number) => string
}

interface CommonMobileTableProps {
  /** 数据获取函数 */
  fetch: (params: PaginatedRequestParams) => Promise<PaginatedResponseData>
  /** 列配置 */
  columns: TableColumn[]
  /** labelWidth */
  labelWidth?: number | string
  /** 是否需要显示操作栏目 */
  footer?: boolean

}

const props = withDefaults(defineProps<CommonMobileTableProps>(), {
  labelWidth: 100,
  footer: true,
})

const tableData = ref<ListResponseData>({
  list: [],
  pageNo: 1,
  pageSize: 10,
  total: 0,
})

const [extraParams, resetExtraParams] = useResettableRef({})

/** 每次数据加载都会触发 */
const isLoading = ref(false)
/** 通过 initFetchData加载会触发 */
const isInitLoading = ref(false)

/** 是否还有更多数据 */
const hasMore = computed(() => {
  const { list, total, pageNo, pageSize } = tableData.value
  // 如果没有数据或者当前数据量小于总数且不是因为页面大小限制
  return total > 0 && list.length < total && list.length >= pageNo * pageSize - pageSize
})

/** 是否正在加载更多数据 */
const isLoadingMore = ref(false)

/** 防抖定时器 */
let loadMoreTimer: NodeJS.Timeout | null = null

/**
 * 处理滑动到底部事件
 */
function handleReachBottom() {
  // 防止重复触发
  if (isLoading.value || isLoadingMore.value || !hasMore.value) {
    return
  }

  // 防抖处理，避免快速滑动时重复请求
  if (loadMoreTimer) {
    clearTimeout(loadMoreTimer)
  }

  loadMoreTimer = setTimeout(() => {
    const nextPage = tableData.value.pageNo + 1
    fetchMoreData(nextPage)
  }, 300) // 300ms 防抖延迟
}

/**
 * 获取单元格值
 */
function getCellValue(row: any, column: TableColumn, index: number) {
  const cellValue = row[column.prop]

  if (column.formatter) {
    return column.formatter(row, column, cellValue, index)
  }

  return cellValue || '-'
}

/** 公共的请求函数 - 重置数据 */
async function fetchData(pageNo: number = 1) {
  isLoading.value = true

  try {
    const params = {
      pageNo,
      pageSize: tableData.value.pageSize,
      ...extraParams.value,
    }

    const res = await props.fetch(params)

    tableData.value = {
      ...res,
      pageNo: params.pageNo,
      pageSize: params.pageSize,
    }
  }
  catch (error) {
    console.error('获取数据失败:', error)
  }
  finally {
    nextTick(() => {
      isLoading.value = false
      isInitLoading.value = false
    })
  }
}

/** 加载更多数据 - 追加数据 */
async function fetchMoreData(pageNo: number) {
  // 设置加载更多状态
  isLoadingMore.value = true
  isLoading.value = true

  try {
    const params = {
      pageNo,
      pageSize: tableData.value.pageSize,
      ...extraParams.value,
    }

    const res = await props.fetch(params)

    // 验证返回的数据
    if (!res || !Array.isArray(res.list)) {
      throw new Error('返回数据格式错误')
    }

    // 只有当返回的数据不为空时才追加
    if (res.list.length > 0) {
      // 追加新数据到现有列表，避免重复数据
      const existingIds = new Set(tableData.value.list.map((item: any) => item.id))
      const newItems = res.list.filter((item: any) => !existingIds.has(item.id))

      tableData.value.list.push(...newItems)
      tableData.value.total = res.total
      tableData.value.pageNo = pageNo

      console.log(`成功加载第 ${pageNo} 页数据，新增 ${newItems.length} 条记录`)
    }
    else {
      console.log(`第 ${pageNo} 页没有更多数据`)
    }
  }
  catch (error) {
    console.error('加载更多数据失败:', error)

    // 可以在这里添加用户友好的错误提示
    // 例如：Message.error('加载失败，请重试')
  }
  finally {
    isLoading.value = false
    isLoadingMore.value = false

    // 清理防抖定时器
    if (loadMoreTimer) {
      clearTimeout(loadMoreTimer)
      loadMoreTimer = null
    }
  }
}

/** 初始化请求 */
function initFetchData(params?: Record<string, any>) {
  // 清理之前的防抖定时器
  if (loadMoreTimer) {
    clearTimeout(loadMoreTimer)
    loadMoreTimer = null
  }

  // 重置加载状态
  isLoadingMore.value = false

  if (params) {
    extraParams.value = params
  }

  isInitLoading.value = true
  return fetchData(1)
}

// 组件卸载时清理定时器
onUnmounted(() => {
  if (loadMoreTimer) {
    clearTimeout(loadMoreTimer)
    loadMoreTimer = null
  }
})

// 初始化数据
initFetchData()

// 暴露方法给父组件
defineExpose({
  initFetchData,
  fetchData,
})
</script>

<template>
  <div class="common-mobile-table">
    <slot name="header" :init-fetch-data="initFetchData" />
    <div v-loading="isInitLoading && !tableData.list.length" class="scroll-view flex-1 overflow-hidden">
      <CommonScroll
        :disabled="isLoadingMore || !hasMore"
        :threshold="100"
        @reach-bottom="handleReachBottom"
      >
        <div v-for="(item, index) in tableData.list" :key="item.id || index" class="table-item">
          <div v-for="(row, rowIndex) in columns" :key="row.prop" class="row flex">
            <div
              class="label" :style="{
                width: getCssUnit(labelWidth),
              }"
            >
              {{ row.label }}
            </div>
            <div class="value flex-1">
              <slot :name="row.prop" :row="item">
                {{ getCellValue(item, row, rowIndex) }}
              </slot>
            </div>
          </div>
          <div v-if="footer" class="footer flex">
            <slot name="footer" :row="item">-</slot>
          </div>
        </div>

        <!-- 加载更多状态 -->
        <div v-if="isLoadingMore && tableData.list.length" class="loading-more">
          <div class="loading-text">正在加载更多...</div>
        </div>

        <!-- 没有更多数据 -->
        <div v-if="!hasMore && tableData.list.length && !isLoadingMore" class="no-more">
          <div class="no-more-text">没有更多数据了</div>
        </div>

        <!-- 空状态 -->
        <Empty v-if="!isInitLoading && !isLoading && !tableData.list.length" description="暂无数据" />
      </CommonScroll>
    </div>
  </div>
</template>

<style scoped lang="scss">
.common-mobile-table {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .scroll-view {
    margin-top: 10px;
    .table-item {
      +.table-item {
        margin-top: 10px;
      }
      margin-left: 20px;
      margin-right: 20px;
      padding: 10px 14px;
      border-radius: 6px;
      background-color: #ffffff;
      .row {
        margin-top: 12px;
        justify-content: flex-start;
        .label {
          padding: 5px 0px;
          color: #808080;
          line-height: 1;
          overflow: hidden;
        }
        .value {
          padding: 5px 0px;
          color: #383838;
          line-height: 1;
          word-wrap: break-word;
          align-content: center;
          overflow: hidden;
        }
      }
      .footer {
        justify-content: flex-end;
        margin-top: 6px;
        padding: 5px 0px;
        border-top: 1px solid #F1F6FF;
      }
    }

    // 加载更多状态样式
    .loading-more {
      padding: 20px;
      text-align: center;
      background-color: #fafafa;
      margin: 10px 20px;
      border-radius: 6px;

      .loading-text {
        color: #666;
        font-size: 14px;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;

        &::after {
          content: '';
          display: inline-block;
          width: 16px;
          height: 16px;
          border: 2px solid #e0e0e0;
          border-top-color: #1677ff;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
      }
    }

    // 没有更多数据状态样式
    .no-more {
      padding: 20px;
      text-align: center;
      margin: 10px 20px;

      .no-more-text {
        color: #999;
        font-size: 13px;
        position: relative;
        display: inline-block;
        padding: 0 20px;

        &::before,
        &::after {
          content: '';
          position: absolute;
          top: 50%;
          width: 40px;
          height: 1px;
          background-color: #e0e0e0;
          transform: translateY(-50%);
        }

        &::before {
          left: -50px;
        }

        &::after {
          right: -50px;
        }
      }
    }
  }
}

// 旋转动画
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
