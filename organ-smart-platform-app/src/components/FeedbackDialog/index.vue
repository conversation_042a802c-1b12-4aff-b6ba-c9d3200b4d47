<script setup lang="ts">
import { Button, Dialog, Input, Message, Option, Select } from 'element-ui'
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
import { createFeedback } from '@/api/feedback'
import { useResettableRef } from '@/hooks/useResettable'

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 反馈类型选项
const feedbackTypeOptions = [
  { value: 'BUG', label: 'Bug反馈' },
  { value: 'SUGGESTION', label: '功能建议' },
  { value: 'COMPLAINT', label: '投诉建议' },
  { value: 'OTHER', label: '其他' },
]

// 表单数据
const [formData, resetFormData] = useResettableRef({
  content: '',
  contactPhone: '',
  feedbackType: '',
})

// 加载状态
const loading = ref(false)

// 响应式窗口宽度
const windowWidth = ref(window.innerWidth)

// 检测是否为移动端
const isMobile = computed(() => {
  return windowWidth.value <= 768
})

// 监听窗口大小变化
function handleResize() {
  windowWidth.value = window.innerWidth
}

// 生命周期
onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  // 确保恢复背景滚动
  document.body.style.overflow = ''
})

// 计算属性：表单是否有效
const isFormValid = computed(() => {
  return formData.value.content.trim().length > 0
})

// 监听弹窗显示状态，重置表单
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm()
    // 移动端防止背景滚动
    if (isMobile.value) {
      document.body.style.overflow = 'hidden'
    }
  }
  else {
    // 恢复背景滚动
    if (isMobile.value) {
      document.body.style.overflow = ''
    }
  }
})

// 重置表单
function resetForm() {
  resetFormData()
}

// 关闭弹窗
function handleClose() {
  emit('update:visible', false)
}

// 取消
function handleCancel() {
  handleClose()
}

// 提交反馈
async function handleSubmit() {
  if (!isFormValid.value) {
    Message.warning('请输入您的意见或建议')
    return
  }

  loading.value = true

  try {
    const params = {
      title: '意见反馈',
      content: formData.value.content.trim(),
      feedbackType: formData.value.feedbackType,
      contactPhone: formData.value.contactPhone.trim() || undefined,
    }
    await createFeedback(params)
    Message.success('感谢您的反馈，我们会认真处理')
    emit('success')
    handleClose()
  }
  catch (error: any) {
    Message.error(error.message || '提交失败，请稍后重试')
  }
  finally {
    loading.value = false
  }
}
</script>

<template>
  <Dialog
    :visible="visible"
    title=""
    :width="isMobile ? 'calc(100vw - 20px)' : '480px'"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :custom-class="isMobile ? 'feedback-dialog mobile-feedback-dialog' : 'feedback-dialog'"
    :center="!isMobile"
    :top="isMobile ? '10px' : '15vh'"
    @close="handleClose"
  >
    <div class="feedback-content">
      <!-- 标题和装饰图标 -->
      <div class="header">
        <h3 class="title">感谢您的意见反馈</h3>
        <div class="decoration-icon">
          <svg
            width="80" height="80" viewBox="0 0 80 80"
            fill="none"
          >
            <defs>
              <linearGradient
                id="bgGradient" x1="0%" y1="0%"
                x2="100%" y2="100%"
              >
                <stop offset="0%" style="stop-color:#E3F2FD;stop-opacity:1" />
                <stop offset="100%" style="stop-color:#BBDEFB;stop-opacity:1" />
              </linearGradient>
            </defs>
            <circle
              cx="40" cy="40" r="35"
              fill="url(#bgGradient)" opacity="0.3"
            />
            <circle
              cx="40" cy="40" r="25"
              fill="#90CAF9" opacity="0.5"
            />
            <circle
              cx="40" cy="40" r="15"
              fill="#73B9FF" opacity="0.8"
            />
            <text
              x="40" y="48" text-anchor="middle"
              fill="white" font-size="20" font-weight="bold"
            >反</text>
          </svg>
        </div>
      </div>

      <!-- 反馈类型选择 -->
      <div class="feedback-type-section">
        <label class="form-label">反馈类型</label>
        <Select
          v-model="formData.feedbackType"
          placeholder="请选择反馈类型"
          style="width: 100%"
        >
          <Option
            v-for="option in feedbackTypeOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </Select>
      </div>

      <!-- 输入框 -->
      <div class="input-section">
        <label class="form-label">反馈内容</label>
        <Input
          v-model="formData.content"
          type="textarea"
          :rows="6"
          placeholder="请输入您的意见或建议"
          :maxlength="500"
          show-word-limit
          resize="none"
        />
      </div>

      <!-- 联系方式（可选） -->
      <div class="contact-section">
        <label class="form-label">联系电话（可选）</label>
        <Input
          v-model="formData.contactPhone"
          placeholder="请输入联系电话"
          :maxlength="20"
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <Button @click="handleCancel">取消</Button>
        <Button
          type="primary"
          :loading="loading"
          :disabled="!isFormValid"
          @click="handleSubmit"
        >
          {{ loading ? '提交中...' : '提交' }}
        </Button>
      </div>
    </template>
  </Dialog>
</template>

<style lang="scss" scoped>
:deep(.feedback-dialog) {
  border-radius: 12px;
  overflow: hidden;

  .el-dialog__header {
    padding: 0;
    border-bottom: none;
  }

  .el-dialog__body {
    padding: 30px 30px 20px;
    background: linear-gradient(135deg, #f8fbff 0%, #ffffff 100%);
  }

  .el-dialog__footer {
    padding: 0 30px 30px;
    text-align: right;
    background: linear-gradient(135deg, #f8fbff 0%, #ffffff 100%);
  }
}

// 移动端样式
:deep(.mobile-feedback-dialog) {
  margin: 10px !important;
  max-height: calc(100vh - 20px) !important;
  width: calc(100vw - 20px) !important;
  max-width: calc(100vw - 20px) !important;

  .el-dialog__body {
    padding: 20px 20px 15px;
    max-height: calc(100vh - 160px);
    overflow-y: auto;
    -webkit-overflow-scrolling: touch; // iOS 平滑滚动
  }

  .el-dialog__footer {
    padding: 0 20px 20px;
    position: sticky;
    bottom: 0;
    background: linear-gradient(135deg, #f8fbff 0%, #ffffff 100%);
    border-top: 1px solid #f0f0f0;
    margin-top: 10px;
  }

  // 确保在小屏幕上完全可见
  @media (max-height: 600px) {
    margin: 5px !important;
    max-height: calc(100vh - 10px) !important;

    .el-dialog__body {
      padding: 15px 15px 10px;
      max-height: calc(100vh - 140px);
    }

    .el-dialog__footer {
      padding: 0 15px 15px;
    }
  }
}

.feedback-content {
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30px;

    .title {
      margin: 0;
      font-size: 20px;
      font-weight: 500;
      color: #333;
    }

    .decoration-icon {
      flex-shrink: 0;
    }

    // 移动端样式
    @media (max-width: 768px) {
      flex-direction: column;
      text-align: center;
      margin-bottom: 20px;

      .title {
        font-size: 18px;
        margin-bottom: 10px;
      }

      .decoration-icon {
        svg {
          width: 60px;
          height: 60px;
        }
      }
    }
  }

  .form-label {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
  }

  .feedback-type-section {
    margin-bottom: 20px;

    :deep(.el-select) {
      width: 100%;
    }

    :deep(.el-input__inner) {
      border-radius: 12px;
      border: 1px solid #E0E6ED;
      font-size: 14px;
      background-color: #fafbfc;
      transition: all 0.3s ease;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
        background-color: #ffffff;
      }
    }

    :deep(.el-select-dropdown__item) {
      font-size: 14px;
    }
  }

  .input-section {
    margin-bottom: 20px;

    :deep(.el-textarea) {
      .el-textarea__inner {
        border: 1px solid #E0E6ED;
        border-radius: 12px;
        padding: 16px;
        font-size: 14px;
        line-height: 1.6;
        resize: none;
        background-color: #fafbfc;
        transition: all 0.3s ease;

        &:focus {
          border-color: #73B9FF;
          background-color: #ffffff;
          box-shadow: 0 0 0 2px rgba(115, 185, 255, 0.1);
        }

        &::placeholder {
          color: #B8BCC8;
        }

        // 移动端样式
        @media (max-width: 768px) {
          padding: 12px;
          font-size: 16px; // 防止iOS缩放
          min-height: 120px;
        }
      }
    }
  }

  .contact-section {
    margin-bottom: 20px;

    :deep(.el-input) {
      .el-input__inner {
        border: 1px solid #E0E6ED;
        border-radius: 12px;
        padding: 0 16px;
        height: 44px;
        font-size: 14px;
        background-color: #fafbfc;
        transition: all 0.3s ease;

        &:focus {
          border-color: #73B9FF;
          background-color: #ffffff;
          box-shadow: 0 0 0 2px rgba(115, 185, 255, 0.1);
        }

        &::placeholder {
          color: #B8BCC8;
        }

        // 移动端样式
        @media (max-width: 768px) {
          padding: 0 12px;
          height: 48px;
          font-size: 16px; // 防止iOS缩放
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  .el-button {
    min-width: 88px;
    height: 40px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;

    &:not(.el-button--primary) {
      color: #666;
      border-color: #E0E6ED;
      background-color: #fff;

      &:hover {
        color: #73B9FF;
        border-color: #73B9FF;
        background-color: #f8fbff;
      }
    }

    &.el-button--primary {
      background: linear-gradient(135deg, #73B9FF 0%, #409EFF 100%);
      border-color: #73B9FF;
      box-shadow: 0 2px 8px rgba(115, 185, 255, 0.3);

      &:hover:not(:disabled) {
        background: linear-gradient(135deg, #409EFF 0%, #1890FF 100%);
        border-color: #409EFF;
        box-shadow: 0 4px 12px rgba(115, 185, 255, 0.4);
        transform: translateY(-1px);
      }

      &:disabled {
        background: #C0C4CC;
        border-color: #C0C4CC;
        cursor: not-allowed;
        box-shadow: none;
        transform: none;
      }
    }

    // 移动端样式
    @media (max-width: 768px) {
      min-width: 100px;
      height: 44px;
      font-size: 16px;
    }
  }

  // 移动端按钮布局
  @media (max-width: 768px) {
    gap: 16px;
    justify-content: center;
  }
}
</style>
