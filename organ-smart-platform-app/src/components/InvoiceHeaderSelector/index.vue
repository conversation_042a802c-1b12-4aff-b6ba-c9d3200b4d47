<script setup lang="ts">
import type { HeaderType } from '@/api/invoice/header'
import { Button, Dialog, Tag } from 'element-ui'
import { computed, ref, watch, withDefaults } from 'vue'
import { getInvoiceHeaderPage, HeaderType as HeaderTypeEnum } from '@/api/invoice/header'
import InvoiceHeaderForm from '@/components/InvoiceHeaderForm/index.vue'
import { useRequest } from '@/hooks/useRequest'

interface Props {
  visible: boolean
  orderId?: string // 订单ID，用于开票
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'confirm', headerId: number): void // 确认选择发票抬头
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  orderId: undefined,
})
const emit = defineEmits<Emits>()

// 弹窗状态
const dialogVisible = computed({
  get: () => props.visible,
  set: value => emit('update:visible', value),
})

// 选中的发票抬头ID
const selectedHeaderId = ref<number | null>(null)

// 新增发票抬头弹窗状态
const addHeaderDialogVisible = ref(false)

// 获取发票抬头列表
const { data: headerList, loading, run: fetchHeaders } = useRequest(getInvoiceHeaderPage, {
  manual: true,
  defaultParams: [{ pageNo: 1, pageSize: 100 }], // 获取所有发票抬头
})

// 监听弹窗显示状态，显示时获取数据
watch(() => props.visible, (visible) => {
  if (visible) {
    fetchHeaders({ pageNo: 1, pageSize: 100 })
    selectedHeaderId.value = null // 重置选择
  }
})

// 监听发票抬头列表变化，自动选中默认抬头
watch(() => headerList.value?.list, (list) => {
  if (list && list.length > 0) {
    // 查找默认发票抬头
    const defaultHeader = list.find(header => header.isDefault)
    if (defaultHeader) {
      selectedHeaderId.value = defaultHeader.id
    }
    else {
      // 如果没有默认抬头，选择第一个
      selectedHeaderId.value = list[0].id
    }
  }
}, { immediate: true })

// 处理确认选择
function handleConfirm() {
  if (!selectedHeaderId.value) {
    return
  }
  emit('confirm', selectedHeaderId.value)
  dialogVisible.value = false
}

// 处理取消
function handleCancel() {
  dialogVisible.value = false
}

// 处理添加抬头
function handleAddHeader() {
  addHeaderDialogVisible.value = true
}

// 新增发票抬头成功回调
function handleAddHeaderSuccess() {
  // 重新获取发票抬头列表
  fetchHeaders({ pageNo: 1, pageSize: 100 })
}

// 抬头类型映射
const headerTypeMap = {
  [HeaderTypeEnum.Personal]: '个人',
  [HeaderTypeEnum.Company]: '企业',
}

// 格式化抬头类型
function formatHeaderType(type: HeaderType) {
  return headerTypeMap[type] || type
}
</script>

<template>
  <div>
    <!-- 发票抬头选择弹窗 -->
    <Dialog
      :visible="dialogVisible"
      title="发票抬头"
      width="600px"
      :close-on-click-modal="false"
      @close="handleCancel"
    >
      <div class="header-selector">
        <!-- 添加抬头按钮 -->
        <div class="add-header-btn">
          <Button type="text" @click="handleAddHeader">
            + 添加抬头
          </Button>
        </div>

        <!-- 发票抬头列表 -->
        <div v-loading="loading" class="header-list">
          <div
            v-for="header in headerList?.list || []"
            :key="header.id"
            class="header-item"
            :class="{ selected: selectedHeaderId === header.id }"
            @click="selectedHeaderId = header.id"
          >
            <div class="header-content">
              <div class="header-info">
                <div class="header-name">
                  发票抬头：{{ header.headerName }}
                  <Tag
                    v-if="header.isDefault" type="success" size="mini"
                    style="margin-left: 8px;"
                  >
                    默认
                  </Tag>
                </div>
                <div v-if="header.taxNumber" class="header-tax-number">
                  税号：{{ header.taxNumber }}
                </div>
              </div>
              <div class="header-type">
                {{ formatHeaderType(header.headerType) }}
              </div>
            </div>
            <!-- 自定义Radio圆圈 -->
            <div class="custom-radio" :class="{ checked: selectedHeaderId === header.id }">
              <div class="radio-inner" />
            </div>
          </div>

          <!-- 空状态 -->
          <div v-if="!loading && (!headerList?.list || headerList.list.length === 0)" class="empty-state">
            <p>暂无发票抬头</p>
            <Button type="primary" @click="handleAddHeader">
              添加发票抬头
            </Button>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <Button @click="handleCancel">取消</Button>
          <Button
            type="primary"
            :disabled="!selectedHeaderId"
            @click="handleConfirm"
          >
            确定
          </Button>
        </div>
      </template>
    </Dialog>

    <!-- 新增发票抬头弹窗 -->
    <InvoiceHeaderForm
      :visible="addHeaderDialogVisible"
      @update:visible="addHeaderDialogVisible = $event"
      @success="handleAddHeaderSuccess"
    />
  </div>
</template>

<style lang="scss" scoped>
.header-selector {
  .add-header-btn {
    text-align: right;
    margin-bottom: 16px;

    .el-button--text {
      color: #409EFF;
      font-size: 14px;
    }
  }

  .header-list {
    min-height: 200px;
    max-height: 400px;
    overflow-y: auto;

    .header-item {
      border: 1px solid #E4E7ED;
      border-radius: 8px;
      margin-bottom: 12px;
      padding: 16px;
      transition: all 0.3s;
      cursor: pointer;
      position: relative;

      &:hover {
        border-color: #409EFF;
        background-color: #F0F9FF;
      }

      &.selected {
        border-color: #409EFF;
        background-color: #F0F9FF;
      }

      .header-content {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding-right: 40px; // 为右侧的radio按钮留出空间

        .header-info {
          flex: 1;

          .header-name {
            font-size: 16px;
            font-weight: 500;
            color: #303133;
            line-height: 1;
          }

          .header-tax-number {
            font-size: 14px;
            color: #606266;
            margin-top: 8px;
            line-height: 1;
          }
        }

        .header-type {
          font-size: 12px;
          color: #909399;
          background-color: #F5F7FA;
          padding: 4px 8px;
          border-radius: 4px;
          white-space: nowrap;
        }
      }

      // 自定义Radio样式
      .custom-radio {
        position: absolute;
        top: 50%;
        right: 16px;
        transform: translateY(-50%);
        width: 16px;
        height: 16px;
        border: 2px solid #DCDFE6;
        border-radius: 50%;
        background-color: #FFFFFF;
        transition: all 0.3s;
        cursor: pointer;

        .radio-inner {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: #409EFF;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%) scale(0);
          transition: transform 0.3s;
        }

        &.checked {
          border-color: #409EFF;

          .radio-inner {
            transform: translate(-50%, -50%) scale(1);
          }
        }

        &:hover {
          border-color: #409EFF;
        }
      }
    }

    .empty-state {
      text-align: center;
      padding: 40px 0;
      color: #909399;

      p {
        margin-bottom: 16px;
        font-size: 14px;
      }
    }
  }
}

.dialog-footer {
  text-align: right;

  .el-button + .el-button {
    margin-left: 10px;
  }
}
</style>
