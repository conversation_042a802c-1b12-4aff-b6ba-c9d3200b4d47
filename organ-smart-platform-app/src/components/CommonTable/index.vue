<script setup lang="ts">
import type { Table } from 'element-ui'
import type { CommonTablePropsColumn } from './type'
import CommonPagination from '@/components/CommonPagination/index.vue'
import { useResettableRef } from '@/hooks/useResettable'

// Omit
/** Function that formats content */
interface CommonTableProps extends Omit<Partial<Table>, 'data' | 'height'> {
  /** 数据获取函数 */
  fetch: (params: PaginatedRequestParams) => Promise<PaginatedResponseData>
  /** 列配置 */
  columns: CommonTablePropsColumn[]
}

interface ListResponseData extends PaginatedResponseData {
  pageNo: number
  pageSize: number
}

const props = defineProps<CommonTableProps>()
const attrs = useAttrs()

const withDefaultsProps = {
  border: true,
  // 表头样式配置
  headerCellStyle: {
    backgroundColor: '#F0F6FD',
    color: '#383838',
  },
}

const loading = ref(false)

const tableData = ref<ListResponseData>({
  list: [],
  pageNo: 1,
  pageSize: 10,
  total: 0,
})

/** 用于保存外部如果有可能传递而外参数的情况 */
const [extraParams, resetExtraParams] = useResettableRef({})

/**
 * 获取单元格值
 */
function getCellValue(row: any, column: CommonTablePropsColumn) {
  const cellValue = row[column.prop]
  return cellValue || '-'
}

/** 获取数据的api */
async function fetchData(params: PaginatedRequestParams) {
  try {
    loading.value = true
    const response = await props.fetch({
      ...params,
      ...extraParams.value,
    })
    tableData.value.list = response.list
    tableData.value.total = response.total
    tableData.value.pageNo = params.pageNo || 1
    tableData.value.pageSize = params.pageSize || 10
  }
  catch (error) {
    console.error('获取表格数据失败:', error)
    tableData.value = {
      list: [],
      pageNo: 1,
      pageSize: 10,
      total: 0,
    }
  }
  finally {
    loading.value = false
  }
}

/**
 * 初始化请求 - 重置到第一页并加载数据
 */
function initializeRequest(params?: Record<string, any>) {
  if (params) {
    extraParams.value = params
  }
  else {
    resetExtraParams()
  }
  return fetchData({
    pageNo: 1,
    pageSize: tableData.value.pageSize || 10,
    ...extraParams.value,
  })
}

/**
 * 刷新当前列表 - 保持当前页码和页大小，重新加载数据
 */
async function refresh() {
  const params: PaginatedRequestParams = {
    pageNo: tableData.value.pageNo || 1,
    pageSize: tableData.value.pageSize || 10,
    ...extraParams.value,

  }
  return fetchData(params)
}

/**
 * 处理页码变化
 */
async function handlePageChange(page: number, pageSize: number) {
  const params: PaginatedRequestParams = {
    pageNo: page,
    pageSize,
    ...extraParams.value,
  }
  await fetchData(params)
}

/**
 * 处理每页条数变化
 * 当每页条数改变时，重置到第1页并重新请求数据
 */
async function handlePageSizeChange(_currentPage: number, newPageSize: number) {
  // 更新表格数据中的页面大小
  tableData.value.pageSize = newPageSize
  const params: PaginatedRequestParams = {
    pageNo: 1,
    pageSize: newPageSize,
    ...extraParams.value,
  }

  await fetchData(params)
}

initializeRequest()

defineExpose({
  refresh,
  initializeRequest,
  resetExtraParams,
})
</script>

<template>
  <div class="common-table">
    <slot name="header" :initialize-request="initializeRequest" />

    <div class="table">
      <el-table
        style="width: 100%;"
        height="100%" :data="tableData.list" v-bind="{
          ...withDefaultsProps,
          ...attrs,
        }"
      >
        <!-- 数据列 -->
        <ElTableColumn
          v-for="column in columns"
          v-bind="column"
          :key="column.prop"
        >
          <template v-if="!column.formatter" #default="{ row, $index }">
            <slot
              :name="column.prop"
              :prop="column.prop"
              :row="row"
              :column="column"
              :index="$index"
            >
              {{ getCellValue(row, column) }}
            </slot>
          </template>
        </ElTableColumn>
      </el-table>
    </div>
    <div class="pagination">
      <CommonPagination
        :current="tableData.pageNo"
        :page-size="tableData.pageSize"
        :total="tableData.total"
        :disabled="loading"
        @change="handlePageChange"
        @show-size-change="handlePageSizeChange"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.common-table {
  width: 100%;
  height: 100%;
  flex: 1;
  display: flex;
  overflow: hidden;
  flex-direction: column;
  .table {
    flex: 1;
    overflow: hidden;
  }
}
</style>
