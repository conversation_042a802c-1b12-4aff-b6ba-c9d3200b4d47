# CommonTable 通用表格组件

一个基于原生HTML表格封装的Vue 3组件，提供完整的表格展示功能，无需依赖第三方UI库。

## 何时使用

- 需要展示结构化数据时
- 需要对数据进行排序、筛选等操作时
- 需要自定义单元格内容时
- 需要轻量级表格解决方案时

## 代码演示

### 基础用法

最简单的用法，传入数据和列配置即可。

```vue
<script setup lang="ts">
import { ref } from 'vue'
import CommonTable from '@/components/CommonTable/index.vue'

const tableData = ref([
  { id: 1, name: '张三', age: 25, city: '北京' },
  { id: 2, name: '李四', age: 30, city: '上海' },
])

const tableColumns = ref([
  { label: 'ID', prop: 'id', width: 80 },
  { label: '姓名', prop: 'name', width: 120 },
  { label: '年龄', prop: 'age', width: 80, align: 'center' },
  { label: '城市', prop: 'city' },
])
</script>

<template>
  <CommonTable
    :data="tableData"
    :columns="tableColumns"
  />
</template>
```

### 带序号和排序

支持显示序号列和列排序功能。

```vue
<script setup lang="ts">
const products = ref([
  { name: '苹果', price: 5.5, stock: 100, category: '水果' },
  { name: '香蕉', price: 3.2, stock: 80, category: '水果' },
  { name: '牛奶', price: 12.5, stock: 50, category: '饮品' },
])

const productColumns = ref([
  { label: '商品名称', prop: 'name', sortable: true },
  {
    label: '价格',
    prop: 'price',
    width: 100,
    align: 'right',
    sortable: true,
    formatter: (row, column, cellValue) => `¥${cellValue}`
  },
  { label: '库存', prop: 'stock', width: 100, align: 'center', sortable: true },
  { label: '分类', prop: 'category', width: 120, align: 'center' },
])
</script>

<template>
  <CommonTable
    :data="products"
    :columns="productColumns"
    :show-index="true"
    :stripe="true"
    :border="true"
  />
</template>
```

### 自定义单元格

使用插槽自定义单元格内容。

```vue
<script setup lang="ts">
const orders = ref([
  { id: 1, orderNo: 'ORD001', amount: 299.99, status: 'completed' },
  { id: 2, orderNo: 'ORD002', amount: 199.99, status: 'pending' },
  { id: 3, orderNo: 'ORD003', amount: 399.99, status: 'cancelled' },
])

const orderColumns = ref([
  { label: '订单号', prop: 'orderNo', width: 120 },
  {
    label: '金额',
    prop: 'amount',
    width: 120,
    align: 'right',
    formatter: (row, column, cellValue) => `¥${cellValue.toFixed(2)}`
  },
  { label: '状态', prop: 'status', width: 120, align: 'center' },
  { label: '操作', prop: 'actions', width: 150, align: 'center' },
])

function getStatusClass(status: string) {
  const classMap = {
    completed: 'status-success',
    pending: 'status-warning',
    cancelled: 'status-danger',
  }
  return classMap[status] || ''
}

function getStatusText(status: string) {
  const textMap = {
    completed: '已完成',
    pending: '待处理',
    cancelled: '已取消',
  }
  return textMap[status] || status
}

function handleView(row: any) {
  console.log('查看订单:', row)
}

function handleCancel(row: any) {
  console.log('取消订单:', row)
}
</script>

<template>
  <CommonTable
    :data="orders"
    :columns="orderColumns"
    :show-index="true"
  >
    <!-- 状态列自定义 -->
    <template #status="{ row }">
      <span :class="getStatusClass(row.status)">
        {{ getStatusText(row.status) }}
      </span>
    </template>

    <!-- 操作列自定义 -->
    <template #actions="{ row }">
      <button
        class="btn-primary"
        :disabled="row.status === 'cancelled'"
        @click="handleView(row)"
      >
        查看
      </button>
      <button
        v-if="row.status === 'pending'"
        class="btn-danger"
        @click="handleCancel(row)"
      >
        取消
      </button>
    </template>
  </CommonTable>
</template>

<style scoped>
.status-success {
  color: #67c23a;
  background: #f0f9ff;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.status-warning {
  color: #e6a23c;
  background: #fdf6ec;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.status-danger {
  color: #f56c6c;
  background: #fef0f0;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.btn-primary, .btn-danger {
  padding: 4px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  margin-right: 8px;
}

.btn-primary {
  background: #409eff;
  color: white;
}

.btn-danger {
  background: #f56c6c;
  color: white;
}

.btn-primary:disabled {
  background: #c0c4cc;
  cursor: not-allowed;
}
</style>
```

### 加载状态

支持显示加载状态。

```vue
<script setup lang="ts">
import { onMounted, ref } from 'vue'

const tableData = ref([])
const loading = ref(false)

const columns = ref([
  { label: 'ID', prop: 'id', width: 80, align: 'center' },
  { label: '标题', prop: 'title' },
  { label: '作者', prop: 'author', width: 120 },
  {
    label: '创建时间',
    prop: 'createTime',
    width: 180,
    formatter: (row, column, cellValue) => {
      return new Date(cellValue).toLocaleString()
    }
  },
])

async function fetchData() {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 模拟返回数据
    tableData.value = [
      { id: 1, title: '文章1', author: '张三', createTime: new Date() },
      { id: 2, title: '文章2', author: '李四', createTime: new Date() },
      { id: 3, title: '文章3', author: '王五', createTime: new Date() },
    ]
  }
  catch (error) {
    console.error('加载数据失败:', error)
    tableData.value = []
  }
  finally {
    loading.value = false
  }
}

function refreshData() {
  fetchData()
}

onMounted(() => {
  fetchData()
})
</script>

<template>
  <div>
    <div class="toolbar">
      <button :disabled="loading" @click="refreshData">
        {{ loading ? '加载中...' : '刷新数据' }}
      </button>
    </div>

    <CommonTable
      :data="tableData"
      :columns="columns"
      :loading="loading"
      empty-text="暂无数据，点击刷新按钮加载"
    />
  </div>
</template>

<style scoped>
.toolbar {
  margin-bottom: 16px;
}

.toolbar button {
  padding: 8px 16px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.toolbar button:disabled {
  background: #c0c4cc;
  cursor: not-allowed;
}
</style>
```

### 自定义分页

使用分页插槽自定义分页区域。

```vue
<template>
  <CommonTable
    :data="pagedData"
    :columns="columns"
    :loading="loading"
  >
    <template #pagination>
      <div class="pagination-wrapper">
        <div class="pagination-info">
          共 {{ total }} 条记录，第 {{ currentPage }} / {{ totalPages }} 页
        </div>
        <div class="pagination-controls">
          <button
            :disabled="currentPage <= 1"
            class="page-btn"
            @click="prevPage"
          >
            上一页
          </button>
          <span class="page-numbers">
            <button
              v-for="page in visiblePages"
              :key="page"
              class="page-btn" :class="[{ active: page === currentPage }]"
              @click="goToPage(page)"
            >
              {{ page }}
            </button>
          </span>
          <button
            :disabled="currentPage >= totalPages"
            class="page-btn"
            @click="nextPage"
          >
            下一页
          </button>
        </div>
      </div>
    </template>
  </CommonTable>
</template>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| data | 表格数据 | `Array` | `[]` |
| columns | 列配置 | `TableColumn[]` | `[]` |
| border | 是否显示边框 | `boolean` | `true` |
| stripe | 是否显示斑马纹 | `boolean` | `true` |
| showHeader | 是否显示表头 | `boolean` | `true` |
| showIndex | 是否显示序号列 | `boolean` | `false` |
| loading | 加载状态 | `boolean` | `false` |
| emptyText | 空数据提示文本 | `string` | `'暂无数据'` |

### TableColumn

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| label | 列标题 | `string` | - |
| prop | 数据字段名 | `string` | - |
| width | 列宽度 | `string \| number` | - |
| align | 对齐方式 | `'left' \| 'center' \| 'right'` | `'left'` |
| sortable | 是否可排序 | `boolean` | `false` |
| formatter | 自定义渲染函数 | `Function` | - |

### Slots

| 插槽名 | 说明 | 参数 |
|--------|------|------|
| [prop] | 自定义列内容 | `{ row, column, index }` |
| pagination | 自定义分页区域 | - |

## 主题定制

组件使用CSS变量，可以通过覆盖变量来定制样式：

```css
.common-table {
  --border-color: #ebeef5;
  --header-bg: #f5f7fa;
  --hover-bg: #f5f7fa;
  --stripe-bg: #fafafa;
  --text-color: #333;
  --secondary-text-color: #909399;
}
```

## 注意事项

1. **数据格式**: 确保传入的数据是数组格式
2. **列配置**: `prop` 字段必须与数据对象的属性名对应
3. **排序功能**: 仅支持简单的字符串和数字排序
4. **插槽命名**: 插槽名称必须与列的 `prop` 字段一致
5. **性能优化**: 大数据量时建议使用虚拟滚动或分页

## 类型定义

### TableColumn

```typescript
interface TableColumn {
  /** 列标题 */
  label: string
  /** 数据字段名 */
  prop: string
  /** 列宽度 */
  width?: string | number
  /** 对齐方式 */
  align?: 'left' | 'center' | 'right'
  /** 是否可排序 */
  sortable?: boolean
  /** 自定义渲染函数 */
  formatter?: (row: any, column: TableColumn, cellValue: any, index: number) => string
}
```

### CommonTableProps

```typescript
interface CommonTableProps {
  /** 表格数据 */
  data?: any[]
  /** 列配置 */
  columns?: TableColumn[]
  /** 是否显示边框 */
  border?: boolean
  /** 是否显示斑马纹 */
  stripe?: boolean
  /** 是否显示表头 */
  showHeader?: boolean
  /** 是否显示序号列 */
  showIndex?: boolean
  /** 加载状态 */
  loading?: boolean
  /** 空数据提示文本 */
  emptyText?: string
}
```

## 最佳实践

### 性能优化

```vue
<!-- 大数据量时使用固定高度容器 -->
<div style="height: 400px;">
  <CommonTable
    :data="largeData"
    :columns="columns"
  />
</div>
```

### 响应式设计

```vue
<!-- 移动端适配 -->
<CommonTable
  :data="data"
  :columns="mobileColumns"
  :show-index="false"
  class="mobile-table"
/>

<style>
@media (max-width: 768px) {
  .mobile-table {
    font-size: 12px;
  }

  .mobile-table .table-cell {
    padding: 8px 4px;
  }
}
</style>
```

### 错误处理

```vue
<CommonTable
  :data="data || []"
  :columns="columns"
  :loading="isLoading"
  empty-text="加载失败，请重试"
/>
```

## 常见问题

### Q: 如何实现复杂的单元格内容？
A: 使用具名插槽，可以在插槽中放置任何 Vue 组件。

```vue
<template v-slot:status="{ row }">
  <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
    {{ row.status }}
  </el-tag>
</template>
```

### Q: 如何处理大量数据？
A: 建议使用分页或虚拟滚动，组件本身不处理数据分页。

### Q: 如何自定义样式？
A: 可以通过 CSS 变量或直接覆盖类名来自定义样式。

### Q: 排序功能支持哪些数据类型？
A: 目前支持字符串和数字的简单排序，复杂排序需要自行实现。

## 兼容性

- Vue 3.0+
- 现代浏览器 (Chrome 60+, Firefox 55+, Safari 12+)
- IE 11+ (需要 polyfill)

## 更新日志

### v1.0.0 (2024-01-07)
- 🎉 初始版本发布
- 📊 基于原生HTML表格的Vue 3组件
- 🚀 支持基础表格展示功能
- 🔧 完整的TypeScript类型定义
- 🎨 现代化的UI设计风格
- ✅ 支持排序、自定义渲染、插槽等高级功能
- 📱 响应式设计支持
- 🎯 无第三方依赖，轻量级实现

## 贡献

如果您发现了 bug 或有功能建议，欢迎提交 issue 或 pull request。

### 开发规范
- 使用 TypeScript 编写
- 添加完整的类型定义
- 编写单元测试
- 更新相关文档
- 遵循代码风格规范

## 许可证

MIT License
