import Vue from "vue";
import ToastComponent from "./Toast.vue";
import { ToastOptions, ToastInstance, ToastType, ToastPosition } from "./types";

/**
 * Toast实例类
 */
class ToastInstanceImpl implements ToastInstance {
  private vm: Vue | null = null;
  private container: HTMLElement | null = null;

  constructor(options: ToastOptions) {
    this.create(options);
  }

  /**
   * 创建Toast实例
   */
  private create(options: ToastOptions): void {
    // 创建容器元素
    this.container = document.createElement("div");
    document.body.appendChild(this.container);

    // 保存实例引用用于销毁
    const instanceRef = this;

    // 创建一个包装组件来管理visible状态
    const WrapperComponent = Vue.extend({
      components: {
        ToastComponent,
      },
      data() {
        return {
          visible: false,
        };
      },
      render(h) {
        return h("ToastComponent", {
          props: {
            visible: this.visible,
            message: options.message,
            type: options.type || ToastType.INFO,
            duration: options.duration !== undefined ? options.duration : 3000,
            position: options.position || ToastPosition.CENTER,
            overlay: options.overlay || false,
            forbidClick: options.forbidClick || false,
            className: options.className || "",
          },
          on: {
            "update:visible": (val: boolean) => {
              this.visible = val;
            },
            close: () => {
              this.hide();
              if (options.onClose) {
                options.onClose();
              }
            },
            click: () => {
              if (options.onClick) {
                options.onClick();
              }
            },
            "after-leave": () => {
              instanceRef.destroy();
            },
          },
        });
      },
      methods: {
        show() {
          this.visible = true;
        },
        hide() {
          this.visible = false;
        },
      },
    });

    this.vm = new WrapperComponent();
    this.vm.$mount(this.container);
  }

  /**
   * 显示Toast
   */
  show(): void {
    if (this.vm) {
      (this.vm as any).show();
    }
  }

  /**
   * 隐藏Toast
   */
  hide(): void {
    if (this.vm) {
      (this.vm as any).hide();
    }
  }

  /**
   * 清除Toast
   */
  clear(): void {
    this.hide();
  }

  /**
   * 销毁Toast实例
   */
  private destroy(): void {
    if (this.vm) {
      this.vm.$destroy();
      this.vm = null;
    }
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
      this.container = null;
    }

    // 从管理器中移除当前实例
    toastManager.removeInstance(this);
  }
}

/**
 * Toast管理器类
 */
class ToastManagerImpl {
  private instances: ToastInstanceImpl[] = [];

  /**
   * 显示Toast
   */
  show(options: ToastOptions): ToastInstance {
    const instance = new ToastInstanceImpl(options);
    this.instances.push(instance);

    // 延迟显示，确保DOM已挂载
    this.$nextTick(() => {
      instance.show();
    });

    return instance;
  }

  /**
   * 显示成功提示
   */
  success(message: string, options: Partial<ToastOptions> = {}): ToastInstance {
    return this.show({
      message,
      type: ToastType.SUCCESS,
      ...options,
    });
  }

  /**
   * 显示错误提示
   */
  error(message: string, options: Partial<ToastOptions> = {}): ToastInstance {
    return this.show({
      message,
      type: ToastType.ERROR,
      ...options,
    });
  }

  /**
   * 显示警告提示
   */
  warning(message: string, options: Partial<ToastOptions> = {}): ToastInstance {
    return this.show({
      message,
      type: ToastType.WARNING,
      ...options,
    });
  }

  /**
   * 显示信息提示
   */
  info(message: string, options: Partial<ToastOptions> = {}): ToastInstance {
    return this.show({
      message,
      type: ToastType.INFO,
      ...options,
    });
  }

  /**
   * 显示加载提示
   */
  loading(
    message: string = "加载中...",
    options: Partial<ToastOptions> = {}
  ): ToastInstance {
    return this.show({
      message,
      type: ToastType.LOADING,
      duration: 0, // 加载提示默认不自动关闭
      ...options,
    });
  }

  /**
   * 清除所有Toast
   */
  clear(): void {
    this.instances.forEach((instance) => {
      instance.clear();
    });
    this.instances = [];
  }

  /**
   * 移除指定的Toast实例
   * @param instance 要移除的实例
   */
  removeInstance(instance: ToastInstanceImpl): void {
    const index = this.instances.indexOf(instance);
    if (index > -1) {
      this.instances.splice(index, 1);
    }
  }

  /**
   * 下一个tick执行
   */
  private $nextTick(callback: () => void): void {
    Vue.nextTick(callback);
  }
}

// 创建单例实例
const toastManager = new ToastManagerImpl();

export default toastManager;
