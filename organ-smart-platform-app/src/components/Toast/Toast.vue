<template>
  <transition name="toast-fade" @after-leave="handleAfterLeave">
    <div v-if="visible" class="toast-wrapper">
      <!-- 遮罩层 - 移到外层，确保覆盖整个视口 -->
      <div
        v-if="overlay"
        class="toast-overlay"
        :class="{ 'toast-overlay--forbid': forbidClick }"
        @click="handleOverlayClick"
      />

      <!-- Toast容器 -->
      <div
        class="toast-container"
        :class="[`toast-container--${position}`, className]"
        @click="handleClick"
      >
        <!-- Toast内容 -->
        <div
          class="toast-content"
          :class="[
            `toast-content--${type}`,
            { 'toast-content--with-overlay': overlay },
          ]"
        >
          <!-- 图标 -->
          <div v-if="isShowIcon" class="toast-icon">
            <i :class="iconClass" />
          </div>

          <!-- 加载动画 -->
          <div v-if="type === 'loading'" class="toast-loading">
            <div class="toast-loading-spinner" />
          </div>

          <!-- 消息内容 -->
          <div v-if="message" class="toast-message">
            {{ message }}
          </div>
        </div>
      </div>
    </div>
  </transition>
</template>

<script lang="ts">
import Vue from "vue";
import { ToastType, ToastPosition } from "./types";

export default Vue.extend({
  name: "Toast",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    message: {
      type: String,
      default: "",
    },
    type: {
      type: String,
      default: ToastType.INFO,
    },
    duration: {
      type: Number,
      default: 3000,
    },
    position: {
      type: String,
      default: ToastPosition.CENTER,
    },
    overlay: {
      type: Boolean,
      default: false,
    },
    forbidClick: {
      type: Boolean,
      default: false,
    },
    className: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      timer: null as number | null,
    };
  },
  computed: {
    /**
     * 是否显示图标
     */
    isShowIcon(): boolean {
      return (
        this.showIcon !== false &&
        this.type !== ToastType.LOADING &&
        this.type !== ToastType.INFO
      );
    },
    /**
     * 图标类名
     */
    iconClass(): string {
      const iconMap = {
        [ToastType.SUCCESS]: "el-icon-success",
        [ToastType.ERROR]: "el-icon-error",
        [ToastType.WARNING]: "el-icon-warning",
      };
      return iconMap[this.type as ToastType] || "";
    },
  },
  watch: {
    visible(newVal: boolean): void {
      if (newVal) {
        this.startTimer();
      } else {
        this.clearTimer();
      }
    },
  },
  methods: {
    /**
     * 启动定时器
     */
    startTimer(): void {
      if (this.duration > 0) {
        this.timer = window.setTimeout(() => {
          this.hide();
        }, this.duration);
      }
    },
    /**
     * 清除定时器
     */
    clearTimer(): void {
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
    },
    /**
     * 隐藏Toast
     */
    hide(): void {
      this.$emit("update:visible", false);
      this.$emit("close");
    },
    /**
     * 处理点击事件
     */
    handleClick(): void {
      if (!this.forbidClick) {
        this.$emit("click");
      }
    },
    /**
     * 处理遮罩层点击事件
     */
    handleOverlayClick(): void {
      // 如果禁止点击，则不处理点击事件
      if (this.forbidClick) {
        return;
      }
      // 否则隐藏Toast
      this.hide();
    },
    /**
     * 处理离开动画完成事件
     */
    handleAfterLeave(): void {
      this.$emit("after-leave");
    },
  },
  beforeDestroy(): void {
    this.clearTimer();
  },
});
</script>

<style lang="scss" scoped>
// Toast包装器 - 最外层容器
.toast-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  pointer-events: none;
}

// Toast容器 - 内容定位容器
.toast-container {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  pointer-events: none;
  z-index: 2; // 相对于遮罩层的层级

  &--top {
    top: 20%;
  }

  &--center {
    top: 50%;
    transform: translate(-50%, -50%);
  }

  &--bottom {
    bottom: 20%;
  }
}

// 遮罩层 - 覆盖整个视口
.toast-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  pointer-events: none;
  z-index: 1; // 在Toast内容之下

  &--forbid {
    pointer-events: auto;
    cursor: not-allowed;
  }
}

.toast-content {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  min-width: 96px;
  max-width: 300px;
  padding: 16px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  color: #fff;
  font-size: 14px;
  line-height: 1.4;
  text-align: center;
  word-wrap: break-word;
  pointer-events: auto;

  &--with-overlay {
    pointer-events: none;
  }

  &--success {
    .toast-icon {
      color: #67c23a;
    }
  }

  &--error {
    .toast-icon {
      color: #f56c6c;
    }
  }

  &--warning {
    .toast-icon {
      color: #e6a23c;
    }
  }

  &--info {
    // 信息类型的默认样式
    .toast-icon {
      color: #909399;
    }
  }

  &--loading {
    // 加载类型的样式
    .toast-loading {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.toast-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.toast-loading {
  margin-bottom: 8px;
}

.toast-loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #fff;
  border-radius: 50%;
  animation: toast-spin 1s linear infinite;
}

.toast-message {
  margin-top: 4px;
}

// 动画效果
.toast-fade-enter-active,
.toast-fade-leave-active {
  transition: opacity 0.3s ease;
}

.toast-fade-enter,
.toast-fade-leave-to {
  opacity: 0;
}

@keyframes toast-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
