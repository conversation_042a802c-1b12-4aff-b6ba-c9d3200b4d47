import toastManager from "./ToastManager";
import { ToastOptions, ToastInstance, ToastManager } from "./types";

/**
 * Toast Hook
 * 提供便捷的Toast使用方式
 */
export function useToast(): ToastManager {
  return {
    /**
     * 显示成功提示
     * @param message 提示信息
     * @param options 配置选项
     * @returns Toast实例
     */
    success: (
      message: string,
      options?: Partial<ToastOptions>
    ): ToastInstance => {
      return toastManager.success(message, options);
    },

    /**
     * 显示错误提示
     * @param message 提示信息
     * @param options 配置选项
     * @returns Toast实例
     */
    error: (
      message: string,
      options?: Partial<ToastOptions>
    ): ToastInstance => {
      return toastManager.error(message, options);
    },

    /**
     * 显示警告提示
     * @param message 提示信息
     * @param options 配置选项
     * @returns Toast实例
     */
    warning: (
      message: string,
      options?: Partial<ToastOptions>
    ): ToastInstance => {
      return toastManager.warning(message, options);
    },

    /**
     * 显示信息提示
     * @param message 提示信息
     * @param options 配置选项
     * @returns Toast实例
     */
    info: (message: string, options?: Partial<ToastOptions>): ToastInstance => {
      return toastManager.info(message, options);
    },

    /**
     * 显示加载提示
     * @param message 提示信息，默认为"加载中..."
     * @param options 配置选项
     * @returns Toast实例
     */
    loading: (
      message?: string,
      options?: Partial<ToastOptions>
    ): ToastInstance => {
      return toastManager.loading(message, options);
    },

    /**
     * 显示自定义Toast
     * @param options 配置选项
     * @returns Toast实例
     */
    show: (options: ToastOptions): ToastInstance => {
      return toastManager.show(options);
    },

    /**
     * 清除所有Toast
     */
    clear: (): void => {
      toastManager.clear();
    },
  };
}

/**
 * 全局Toast实例，可直接使用
 */
export const toast = toastManager;

// 导出类型
export * from "./types";
