<template>
  <div class="toast-test-page">
    <h1>Toast组件测试页面</h1>

    <div class="test-section">
      <h2>基础用法测试</h2>
      <div class="button-row">
        <el-button type="success" @click="testSuccess">成功提示</el-button>
        <el-button type="danger" @click="testError">错误提示</el-button>
        <el-button type="warning" @click="testWarning">警告提示</el-button>
        <el-button type="info" @click="testInfo">信息提示</el-button>
        <el-button type="primary" @click="testLoading">加载提示</el-button>
      </div>
    </div>

    <div class="test-section">
      <h2>位置测试</h2>
      <div class="button-row">
        <el-button @click="testTop">顶部</el-button>
        <el-button @click="testCenter">居中</el-button>
        <el-button @click="testBottom">底部</el-button>
      </div>
    </div>

    <div class="test-section">
      <h2>高级功能测试</h2>
      <div class="button-row">
        <el-button @click="testOverlay">带遮罩</el-button>
        <el-button @click="testLongDuration">长时间显示</el-button>
        <el-button @click="testCallback">带回调</el-button>
        <el-button @click="testClear">清除所有</el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import { useToast, ToastPosition } from "@/components/Toast";

export default Vue.extend({
  name: "ToastTest",
  data() {
    return {
      toast: useToast(),
    };
  },
  methods: {
    /**
     * 测试成功提示
     */
    testSuccess(): void {
      this.toast.success("操作成功！这是一个成功提示");
    },

    /**
     * 测试错误提示
     */
    testError(): void {
      this.toast.error("操作失败！请检查网络连接");
    },

    /**
     * 测试警告提示
     */
    testWarning(): void {
      this.toast.warning("请注意：数据即将过期");
    },

    /**
     * 测试信息提示
     */
    testInfo(): void {
      this.toast.info("这是一条普通的信息提示");
    },

    /**
     * 测试加载提示
     */
    testLoading(): void {
      const loadingToast = this.toast.loading("数据加载中，请稍候...");

      // 模拟异步操作
      setTimeout(() => {
        loadingToast.hide();
        this.toast.success("数据加载完成");
      }, 3000);
    },

    /**
     * 测试顶部显示
     */
    testTop(): void {
      this.toast.info("顶部显示的Toast", {
        position: ToastPosition.TOP,
      });
    },

    /**
     * 测试居中显示
     */
    testCenter(): void {
      this.toast.info("居中显示的Toast", {
        position: ToastPosition.CENTER,
      });
    },

    /**
     * 测试底部显示
     */
    testBottom(): void {
      this.toast.info("底部显示的Toast", {
        position: ToastPosition.BOTTOM,
      });
    },

    /**
     * 测试带遮罩层
     */
    testOverlay(): void {
      this.toast.info("带遮罩层的Toast，背景不可点击", {
        overlay: true,
        forbidClick: true,
        duration: 3000,
      });
    },

    /**
     * 测试长时间显示
     */
    testLongDuration(): void {
      this.toast.warning("这个Toast会显示10秒钟", {
        duration: 10000,
      });
    },

    /**
     * 测试带回调函数
     */
    testCallback(): void {
      this.toast.success("点击我或等待关闭", {
        duration: 5000,
        onClick: () => {
          console.log("Toast被点击了");
          this.toast.info("你点击了Toast！");
        },
        onClose: () => {
          console.log("Toast关闭了");
        },
      });
    },

    /**
     * 清除所有Toast
     */
    testClear(): void {
      this.toast.clear();
      this.toast.info("所有Toast已清除");
    },
  },
});
</script>

<style lang="scss" scoped>
.toast-test-page {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;

  h1 {
    text-align: center;
    color: #333;
    margin-bottom: 40px;
  }

  .test-section {
    margin-bottom: 40px;
    padding: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    background: #fafafa;

    h2 {
      margin-bottom: 20px;
      color: #606266;
      font-size: 18px;
    }

    .button-row {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;

      .el-button {
        margin: 0;
      }
    }
  }
}

@media (max-width: 768px) {
  .toast-test-page {
    padding: 10px;

    .test-section {
      padding: 15px;

      .button-row {
        flex-direction: column;

        .el-button {
          width: 100%;
        }
      }
    }
  }
}
</style>
