/**
 * Toast组件类型定义
 */

// Toast类型枚举
export enum ToastType {
  SUCCESS = "success",
  ERROR = "error",
  WARNING = "warning",
  INFO = "info",
  LOADING = "loading",
}

// Toast位置枚举
export enum ToastPosition {
  TOP = "top",
  CENTER = "center",
  BOTTOM = "bottom",
}

// Toast配置选项接口
export interface ToastOptions {
  /** 提示信息 */
  message: string;
  /** Toast类型 */
  type?: ToastType;
  /** 显示时长，单位毫秒，0表示不自动关闭 */
  duration?: number;
  /** 显示位置 */
  position?: ToastPosition;
  /** 是否显示遮罩层 */
  overlay?: boolean;
  /** 是否禁止背景点击 */
  forbidClick?: boolean;
  /** 自定义类名 */
  className?: string;
  /** 关闭回调函数 */
  onClose?: () => void;
  /** 点击回调函数 */
  onClick?: () => void;
  /** 是否显示图标 */
  showIcon?: boolean;
}

// Toast实例接口
export interface ToastInstance {
  /** 显示Toast */
  show: () => void;
  /** 隐藏Toast */
  hide: () => void;
  /** 清除Toast */
  clear: () => void;
}

// Toast组件Props接口
export interface ToastProps {
  /** 是否显示 */
  visible: boolean;
  /** 提示信息 */
  message: string;
  /** Toast类型 */
  type: ToastType;
  /** 显示时长 */
  duration: number;
  /** 显示位置 */
  position: ToastPosition;
  /** 是否显示遮罩层 */
  overlay: boolean;
  /** 是否禁止背景点击 */
  forbidClick: boolean;
  /** 自定义类名 */
  className: string;
}

// Toast管理器接口
export interface ToastManager {
  /** 显示成功提示 */
  success: (message: string, options?: Partial<ToastOptions>) => ToastInstance;
  /** 显示错误提示 */
  error: (message: string, options?: Partial<ToastOptions>) => ToastInstance;
  /** 显示警告提示 */
  warning: (message: string, options?: Partial<ToastOptions>) => ToastInstance;
  /** 显示信息提示 */
  info: (message: string, options?: Partial<ToastOptions>) => ToastInstance;
  /** 显示加载提示 */
  loading: (message?: string, options?: Partial<ToastOptions>) => ToastInstance;
  /** 显示自定义Toast */
  show: (options: ToastOptions) => ToastInstance;
  /** 清除所有Toast */
  clear: () => void;
}
