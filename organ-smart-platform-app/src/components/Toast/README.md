# Toast 组件

一个类似于 wot-ui 的 Toast 提示组件，支持多种类型的提示信息和灵活的配置选项。

## 特性

- 🎯 支持多种提示类型：成功、错误、警告、信息、加载
- 📍 支持多种显示位置：顶部、居中、底部
- ⏰ 支持自定义显示时长
- 🎭 支持遮罩层和禁止点击
- 🎨 支持自定义样式类名
- 🔄 支持加载动画
- 📱 响应式设计，适配移动端
- 🎣 提供 Hook 方式使用

## 基础用法

### 1. 使用 Hook 方式（推荐）

```typescript
import { useToast } from '@/hooks/useToast'

export default {
  data() {
    return {
      toast: useToast()
    }
  },
  methods: {
    showSuccess() {
      this.toast.success('操作成功！')
    },
    showError() {
      this.toast.error('操作失败')
    },
    showWarning() {
      this.toast.warning('请注意')
    },
    showInfo() {
      this.toast.info('提示信息')
    },
    showLoading() {
      const loadingToast = this.toast.loading('加载中...')
      // 手动关闭
      setTimeout(() => {
        loadingToast.hide()
      }, 3000)
    }
  }
}
```

### 2. 直接使用管理器

```typescript
import { toast } from '@/hooks/useToast'

// 在任何地方使用
toast.success('操作成功！')
toast.error('操作失败')
```

### 3. 组件方式使用

```vue
<template>
  <Toast
    :visible.sync="visible"
    :message="message"
    :type="type"
    @close="handleClose"
  />
</template>

<script>
import { Toast } from '@/components/Toast'

export default {
  components: {
    Toast
  },
  data() {
    return {
      visible: false,
      message: '',
      type: 'info'
    }
  }
}
</script>
```

## API

### ToastOptions

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| message | 提示信息 | string | - |
| type | 提示类型 | 'success' \| 'error' \| 'warning' \| 'info' \| 'loading' | 'info' |
| duration | 显示时长（毫秒），0 表示不自动关闭 | number | 3000 |
| position | 显示位置 | 'top' \| 'center' \| 'bottom' | 'center' |
| overlay | 是否显示遮罩层 | boolean | false |
| forbidClick | 是否禁止背景点击 | boolean | false |
| className | 自定义类名 | string | - |
| onClose | 关闭回调函数 | () => void | - |
| onClick | 点击回调函数 | () => void | - |

### Toast 组件 Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| visible | 是否显示 | boolean | false |
| message | 提示信息 | string | - |
| type | 提示类型 | string | 'info' |
| duration | 显示时长 | number | 3000 |
| position | 显示位置 | string | 'center' |
| overlay | 是否显示遮罩层 | boolean | false |
| forbidClick | 是否禁止背景点击 | boolean | false |
| className | 自定义类名 | string | - |

### Toast 组件 Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| close | 关闭时触发 | - |
| click | 点击时触发 | - |
| after-leave | 离开动画完成后触发 | - |

### useToast Hook 返回值

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| success | 显示成功提示 | (message: string, options?: Partial<ToastOptions>) | ToastInstance |
| error | 显示错误提示 | (message: string, options?: Partial<ToastOptions>) | ToastInstance |
| warning | 显示警告提示 | (message: string, options?: Partial<ToastOptions>) | ToastInstance |
| info | 显示信息提示 | (message: string, options?: Partial<ToastOptions>) | ToastInstance |
| loading | 显示加载提示 | (message?: string, options?: Partial<ToastOptions>) | ToastInstance |
| show | 显示自定义 Toast | (options: ToastOptions) | ToastInstance |
| clear | 清除所有 Toast | () => void | - |

### ToastInstance

| 方法 | 说明 |
|------|------|
| show | 显示 Toast |
| hide | 隐藏 Toast |
| clear | 清除 Toast |

## 高级用法

### 自定义样式

```typescript
this.toast.success('自定义样式', {
  className: 'custom-toast',
  duration: 5000
})
```

```scss
.custom-toast {
  .toast-content {
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    border-radius: 20px;
  }
}
```

### 带遮罩层的 Toast

```typescript
this.toast.loading('处理中...', {
  overlay: true,
  forbidClick: true
})
```

### 手动控制 Toast

```typescript
const loadingToast = this.toast.loading('上传中...', {
  duration: 0 // 不自动关闭
})

// 模拟上传过程
setTimeout(() => {
  loadingToast.hide()
  this.toast.success('上传成功')
}, 3000)
```

### 带回调的 Toast

```typescript
this.toast.info('点击我试试', {
  onClick: () => {
    console.log('Toast 被点击了')
  },
  onClose: () => {
    console.log('Toast 关闭了')
  }
})
```

## 注意事项

1. 加载类型的 Toast 默认不会自动关闭，需要手动调用 `hide()` 方法
2. 当设置 `overlay: true` 时，建议同时设置 `forbidClick: true` 以防止误操作
3. Toast 组件会自动管理 DOM 的创建和销毁，无需手动处理
4. 多个 Toast 可以同时显示，后显示的会覆盖在前面的上方

## 样式定制

组件使用 SCSS 编写样式，支持以下 CSS 变量定制：

```scss
:root {
  --toast-bg-color: rgba(0, 0, 0, 0.8);
  --toast-text-color: #fff;
  --toast-border-radius: 8px;
  --toast-padding: 16px;
  --toast-min-width: 96px;
  --toast-max-width: 300px;
  --toast-font-size: 14px;
  --toast-line-height: 1.4;
}
```
