# Toast 组件使用说明

## 快速开始

### 1. 基础使用（推荐）

在 Vue 组件中使用 useToast Hook：

```vue
<template>
  <div>
    <el-button @click="showSuccess">显示成功提示</el-button>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import { useToast } from "@/hooks/useToast";

export default Vue.extend({
  data() {
    return {
      toast: useToast(),
    };
  },
  methods: {
    showSuccess() {
      this.toast.success("操作成功！");
    },
  },
});
</script>
```

### 2. 全局使用

在任何地方直接使用：

```typescript
import { toast } from "@/hooks/useToast";

// 直接调用
toast.success("操作成功！");
toast.error("操作失败");
toast.warning("警告信息");
toast.info("提示信息");
```

### 3. 加载提示

```typescript
// 显示加载提示
const loadingToast = this.toast.loading("数据加载中...");

// 异步操作完成后关闭
setTimeout(() => {
  loadingToast.hide();
  this.toast.success("加载完成");
}, 3000);
```

## API 参考

### 基础方法

| 方法                          | 说明           | 参数                                                |
| ----------------------------- | -------------- | --------------------------------------------------- |
| `success(message, options?)`  | 成功提示       | message: 提示文本<br>options: 配置选项              |
| `error(message, options?)`    | 错误提示       | 同上                                                |
| `warning(message, options?)`  | 警告提示       | 同上                                                |
| `info(message, options?)`     | 信息提示       | 同上                                                |
| `loading(message?, options?)` | 加载提示       | message: 可选，默认"加载中..."<br>options: 配置选项 |
| `clear()`                     | 清除所有 Toast | 无                                                  |

### 配置选项 (ToastOptions)

| 属性          | 类型                          | 默认值   | 说明                             |
| ------------- | ----------------------------- | -------- | -------------------------------- |
| `duration`    | number                        | 3000     | 显示时长(毫秒)，0 表示不自动关闭 |
| `position`    | 'top' \| 'center' \| 'bottom' | 'center' | 显示位置                         |
| `overlay`     | boolean                       | false    | 是否显示遮罩层                   |
| `forbidClick` | boolean                       | false    | 是否禁止背景点击                 |
| `className`   | string                        | -        | 自定义 CSS 类名                  |
| `onClick`     | () => void                    | -        | 点击回调                         |
| `onClose`     | () => void                    | -        | 关闭回调                         |

## 使用示例

### 基础提示

```typescript
// 成功提示
this.toast.success("保存成功");

// 错误提示
this.toast.error("网络连接失败");

// 警告提示
this.toast.warning("数据即将过期");

// 信息提示
this.toast.info("这是一条提示信息");
```

### 自定义配置

```typescript
// 自定义显示时长
this.toast.success("操作成功", { duration: 5000 });

// 自定义位置
this.toast.info("顶部提示", { position: "top" });

// 带遮罩层
this.toast.loading("处理中...", {
  overlay: true,
  forbidClick: true,
});
```

### 带回调函数

```typescript
this.toast.success("点击我试试", {
  onClick: () => {
    console.log("Toast被点击了");
  },
  onClose: () => {
    console.log("Toast关闭了");
  },
});
```

### 手动控制

```typescript
// 显示不自动关闭的Toast
const toast = this.toast.loading("上传中...", { duration: 0 });

// 手动关闭
setTimeout(() => {
  toast.hide();
  this.toast.success("上传完成");
}, 3000);
```

## 注意事项

1. **加载提示**：loading 类型的 Toast 默认不会自动关闭，需要手动调用`hide()`方法
2. **遮罩层**：设置`overlay: true`时建议同时设置`forbidClick: true`
3. **多个 Toast**：可以同时显示多个 Toast，后显示的会覆盖在前面的上方
4. **内存管理**：Toast 组件会自动管理 DOM 的创建和销毁

## 测试页面

访问 `/test/toast` 路径可以查看完整的功能演示和测试。

## 样式定制

可以通过传入`className`属性来自定义样式：

```typescript
this.toast.success("自定义样式", {
  className: "my-custom-toast",
});
```

```scss
.my-custom-toast {
  .toast-content {
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    border-radius: 20px;
  }
}
```
