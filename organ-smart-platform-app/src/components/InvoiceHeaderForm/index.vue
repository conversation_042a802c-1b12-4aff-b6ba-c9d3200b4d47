<script setup lang="ts">
import type { InvoiceHeaderCreateReqDTO } from '@/api/invoice/header'
import { Button, Dialog, Form, FormItem, Input, Message, Option, Radio, RadioGroup, Select } from 'element-ui'
import { computed, nextTick, ref, watch, withDefaults } from 'vue'
import { createInvoiceHeader, HeaderType, InvoiceCategory, InvoiceType, Status, updateInvoiceHeader } from '@/api/invoice/header'
import { useResettableRef } from '@/hooks/useResettable'

interface Props {
  visible: boolean
  editData?: any // 编辑时传入的数据，为空表示新增
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void // 操作成功后触发
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  editData: undefined,
})
const emit = defineEmits<Emits>()

// 抬头类型映射
const headerTypeMap = {
  [HeaderType.Personal]: '个人',
  [HeaderType.Company]: '企业',
}

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: value => emit('update:visible', value),
})

const dialogTitle = computed(() => {
  return props.editData ? '编辑发票抬头' : '新增发票抬头'
})

const isEditing = computed(() => !!props.editData)

// 移动端适配通过CSS媒体查询处理

// 表单数据 - 使用useResettableRef方便重置
const [formData, resetFormData] = useResettableRef<InvoiceHeaderCreateReqDTO>({
  headerName: '',
  headerType: HeaderType.Personal,
  invoiceCategory: InvoiceCategory.Electronic,
  invoiceType: InvoiceType.Ordinary,
  taxNumber: '',
  isDefault: false,
  contactName: '',
  contactPhone: '',
  contactEmail: '',
  registeredAddress: '',
  registeredPhone: '',
  bankName: '',
  bankAccount: '',
  remark: '',
  status: Status.Active,
})

// 表单引用
const formRef = ref()

// 表单验证规则
const formRules = {
  headerName: [
    { required: true, message: '请输入抬头名称', trigger: 'blur' },
  ],
  headerType: [
    { required: true, message: '请选择抬头类型', trigger: 'change' },
  ],
  invoiceCategory: [
    { required: true, message: '请选择发票种类', trigger: 'change' },
  ],
  invoiceType: [
    { required: true, message: '请选择发票类型', trigger: 'change' },
  ],
  taxNumber: [
    {
      validator: (_rule: any, value: string, callback: (...args: any[]) => any) => {
        console.log('验证纳税人识别号，当前抬头类型：', formData.value.headerType)
        console.log('验证纳税人识别号，当前值：', value)

        // 只有企业抬头时才进行验证
        if (formData.value.headerType === HeaderType.Company) {
          // 企业抬头时必须填写纳税人识别号
          if (!value || value.trim() === '') {
            console.log('验证失败：企业抬头必须填写纳税人识别号')
            callback(new Error('企业抬头必须填写纳税人识别号'))
            return
          }

          // 验证格式
          const taxNumberRegex = /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/
          if (!taxNumberRegex.test(value)) {
            console.log('验证失败：纳税人识别号格式不正确')
            callback(new Error('纳税人识别号格式不正确'))
            return
          }
        }

        // 个人抬头或验证通过
        console.log('验证通过')
        callback()
      },
      trigger: ['blur', 'change'],
    },
  ],
  contactEmail: [
    {
      type: 'email',
      message: '请输入正确的邮箱地址',
      trigger: 'blur',
    },
  ],
}

// 监听编辑数据变化，填充表单
watch(() => props.editData, (newData) => {
  if (newData) {
    // 编辑时直接使用原始数据，不添加默认值，避免掩盖真实状态
    formData.value = { ...newData }
  }
  else {
    // 新增时重置表单
    resetForm()
  }
}, { immediate: true })

// 监听弹窗显示状态
watch(() => props.visible, (visible) => {
  if (visible && !props.editData) {
    // 新增时重置表单
    resetForm()
  }
})

// 监听抬头类型变化，重新验证纳税人识别号
watch(() => formData.value.headerType, () => {
  if (formRef.value) {
    // 延迟验证，确保DOM已更新
    nextTick(() => {
      formRef.value.validateField('taxNumber')
    })
  }
})

function resetForm() {
  // 使用useResettableRef提供的重置函数
  resetFormData()
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

function handleClose() {
  dialogVisible.value = false
  resetForm()
}

// 表单提交
async function handleSubmit() {
  try {
    console.log('提交表单，当前数据：', formData.value)
    console.log('抬头类型：', formData.value.headerType)
    console.log('纳税人识别号：', formData.value.taxNumber)

    // 验证逻辑已在表单验证规则中处理，无需重复验证

    // 表单验证
    console.log('formRef:', formRef.value)
    if (!formRef.value) {
      Message.error('表单引用未找到')
      return
    }

    await formRef.value.validate()

    if (isEditing.value && props.editData?.id) {
      // 编辑模式 - 直接传入包含 ID 的数据
      await updateInvoiceHeader({ id: props.editData.id, ...formData.value })
      Message.success('修改成功')
    }
    else {
      // 新增模式
      await createInvoiceHeader(formData.value)
      Message.success('创建成功')
    }

    // 触发成功事件
    emit('success')
    handleClose()
  }
  catch (error: any) {
    if (error.message) {
      Message.error(error.message)
    }
  }
}
</script>

<template>
  <Dialog
    :visible="dialogVisible"
    :title="dialogTitle"
    width="600px"
    :close-on-click-modal="false"
    custom-class="invoice-header-dialog"
    @close="handleClose"
  >
    <Form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      class="invoice-header-form"
    >
      <FormItem label="抬头名称" prop="headerName" required>
        <Input
          v-model="formData.headerName"
          placeholder="请输入抬头名称"
          maxlength="50"
        />
      </FormItem>

      <FormItem label="抬头类型" prop="headerType" required>
        <RadioGroup v-model="formData.headerType">
          <Radio :label="HeaderType.Personal">
            {{ headerTypeMap[HeaderType.Personal] }}
          </Radio>
          <Radio :label="HeaderType.Company">
            {{ headerTypeMap[HeaderType.Company] }}
          </Radio>
        </RadioGroup>
      </FormItem>

      <FormItem label="发票种类" prop="invoiceCategory" required>
        <Select v-model="formData.invoiceCategory" placeholder="请选择发票种类">
          <Option :value="InvoiceCategory.Electronic" label="电子发票" />
          <Option :value="InvoiceCategory.Paper" label="纸质发票" />
        </Select>
      </FormItem>

      <FormItem label="发票类型" prop="invoiceType" required>
        <Select v-model="formData.invoiceType" placeholder="请选择发票类型">
          <Option :value="InvoiceType.Ordinary" label="普通发票" />
          <Option :value="InvoiceType.Special" label="专用发票" />
        </Select>
      </FormItem>

      <FormItem
        v-show="formData.headerType === HeaderType.Company"
        label="纳税人识别号"
        prop="taxNumber"
        :required="formData.headerType === HeaderType.Company"
      >
        <Input
          v-model="formData.taxNumber"
          placeholder="请输入纳税人识别号"
          maxlength="30"
        />
      </FormItem>

      <FormItem label="联系人姓名">
        <Input
          v-model="formData.contactName"
          placeholder="请输入联系人姓名"
          maxlength="20"
        />
      </FormItem>

      <FormItem label="联系电话">
        <Input
          v-model="formData.contactPhone"
          placeholder="请输入联系电话"
          maxlength="20"
        />
      </FormItem>

      <FormItem label="联系邮箱" prop="contactEmail">
        <Input
          v-model="formData.contactEmail"
          placeholder="请输入联系邮箱"
          maxlength="50"
        />
      </FormItem>

      <FormItem
        v-if="formData.headerType === HeaderType.Company"
        label="注册地址"
      >
        <Input
          v-model="formData.registeredAddress"
          placeholder="请输入注册地址"
          maxlength="100"
        />
      </FormItem>

      <FormItem
        v-if="formData.headerType === HeaderType.Company"
        label="注册电话"
      >
        <Input
          v-model="formData.registeredPhone"
          placeholder="请输入注册电话"
          maxlength="20"
        />
      </FormItem>

      <FormItem
        v-if="formData.headerType === HeaderType.Company"
        label="开户银行"
      >
        <Input
          v-model="formData.bankName"
          placeholder="请输入开户银行"
          maxlength="50"
        />
      </FormItem>

      <FormItem
        v-if="formData.headerType === HeaderType.Company"
        label="银行账号"
      >
        <Input
          v-model="formData.bankAccount"
          placeholder="请输入银行账号"
          maxlength="30"
        />
      </FormItem>

      <FormItem label="备注">
        <Input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
          maxlength="200"
        />
      </FormItem>
    </Form>

    <template #footer>
      <div class="dialog-footer">
        <Button @click="handleClose">取消</Button>
        <Button type="primary" @click="handleSubmit">确定</Button>
      </div>
    </template>
  </Dialog>
</template>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;

  .el-button + .el-button {
    margin-left: 10px;
  }
}

// 表单基础样式在移动端媒体查询中定义

// 移动端按钮适配
@media (max-width: 768px) {
  .dialog-footer {
    text-align: center;

    .el-button {
      min-width: 80px;
      padding: 10px 20px;
      font-size: 14px;

      + .el-button {
        margin-left: 15px;
      }
    }
  }
}
</style>

<style lang="scss">
// 全局样式，用于Dialog的移动端适配
.invoice-header-dialog {
  border-radius: 8px;
  .el-dialog__header {
    .el-dialog__title {
      font-weight: 500;
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .invoice-header-dialog {
    width: 95% !important;
    margin: 5vh auto !important;
    max-height: 90vh !important;
    display: flex;
    flex-direction: column;

    .el-dialog__header {
      padding: 15px 20px 10px;
      flex-shrink: 0;

      .el-dialog__title {
        font-size: 18px;
        font-weight: 500;
      }
    }

    .el-dialog__body {
      padding: 10px 20px;
      flex: 1;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
    }

    .el-dialog__footer {
      padding: 10px 20px 20px;
      border-top: 1px solid #f0f0f0;
      margin-top: 10px;
      flex-shrink: 0;
    }
  }
}
</style>
