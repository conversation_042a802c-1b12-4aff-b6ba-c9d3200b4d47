<script setup lang="ts">
import type { ProductPackageVO } from '@/api/product'
import { Button, Checkbox, Dialog } from 'element-ui'
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
import { getVipProductList } from '@/api/product'
import PaymentDialog from '@/components/PaymentDialog/index.vue'
import { useRequest } from '@/hooks/useRequest'

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'paymentSuccess', orderInfo: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const dialogVisible = ref(false)
const loading = ref(false)

const { data: packageList } = useRequest(getVipProductList)

const selectedPackage = ref<ProductPackageVO | null>(null)
const agreedToTerms = ref(false)
const paymentDialogVisible = ref(false)

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
})

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 获取时长文本
function getDurationText(packageName: string): string {
  // 根据套餐名称解析时长，这里可以根据实际情况调整
  if (packageName.includes('天')) {
    return packageName
  }
  if (packageName.includes('月')) {
    return packageName
  }
  if (packageName.includes('年')) {
    return packageName
  }
  // 默认返回套餐名称
  return packageName
}

// 选择套餐
function selectPackage(pkg: ProductPackageVO) {
  selectedPackage.value = pkg
}

// 处理支付
function handlePayment() {
  if (selectedPackage.value && agreedToTerms.value) {
    paymentDialogVisible.value = true
  }
}

// 处理支付成功
function handlePaymentSuccess(orderInfo: any) {
  console.log('VipPackageDialog收到支付成功事件:', orderInfo)
  // 关闭支付弹窗
  paymentDialogVisible.value = false
  // 触发支付成功事件，通知父组件
  emit('paymentSuccess', orderInfo)
  // 关闭VIP选择弹窗
  handleClose()
}

// 关闭对话框
function handleClose() {
  dialogVisible.value = false
  selectedPackage.value = null
  agreedToTerms.value = false
}

// 移动端检测
const isMobile = ref(false)

// 检测屏幕尺寸
function checkScreenSize() {
  isMobile.value = window.innerWidth <= 768
}

// 计算弹窗宽度
const dialogWidth = computed(() => {
  return isMobile.value ? '95%' : '900px'
})

// 计算弹窗类名
const dialogClass = computed(() => {
  return isMobile.value ? 'vip-package-dialog vip-package-dialog-mobile' : 'vip-package-dialog'
})

onMounted(() => {
  checkScreenSize()
  window.addEventListener('resize', checkScreenSize)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize)
})
</script>

<template>
  <div>
    <Dialog
      :visible="dialogVisible"
      title="开通国聘 海南征信产品 · 解锁全部企业信用信息"
      :width="dialogWidth"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :custom-class="dialogClass"
      @close="handleClose"
    >
      <div class="dialog-content">
        <div class="subtitle">
          报告不限量，套餐更多企业信息，拒绝更新缺失
        </div>

        <div v-if="loading" v-loading="loading" class="loading-container">
          <div style="height: 200px;" />
        </div>

        <div v-else class="package-list">
          <div
            v-for="pkg in packageList"
            :key="pkg.id"
            class="package-item"
            :class="{
              selected: selectedPackage?.id === pkg.id,
            }"
            @click="selectPackage(pkg)"
          >
            <div class="package-header">
              <div class="duration">{{ getDurationText(pkg.packageName) }}</div>
              <div class="price">
                <span class="currency">¥</span>
                <span class="amount">{{ pkg.price.toFixed(2) }}</span>
              </div>
              <div v-if="pkg.originalPrice > pkg.price" class="original-price">
                ¥{{ pkg.originalPrice.toFixed(2) }}
              </div>
            </div>

            <!-- 选中状态显示在右下角 -->
            <img
              v-if="selectedPackage?.id === pkg.id" class="vip-active" src="@/assets/images/pc/vip-active.png"
              alt="选中"
            >

            <img class="vip-icon" src="@/assets/images/pc/vip-icon.png" alt="">
          </div>
        </div>

        <div class="terms">
          <p>高级内容：解决您实际业务中的各种疑难杂症，减少您实际业务中的各种疑难杂症，解决您实际业务中的各种疑难杂症，减少您实际业务中的各种疑难杂症，解决您实际业务中的各种疑难杂症，减少您实际业务中的各种疑难杂症。</p>
        </div>

        <div class="agreement">
          <Checkbox v-model="agreedToTerms" class="agreement-checkbox">
            我已阅读并同意
            <a href="#" class="agreement-link">《xxx协议》</a>
            <a href="#" class="agreement-link">《xxx协议》</a>
          </Checkbox>
        </div>

        <div class="payment-section">
          <Button
            type="primary"
            size="large"
            class="payment-btn"
            :disabled="!selectedPackage || !agreedToTerms"
            @click="handlePayment"
          >
            成为会员 支付 ¥{{ selectedPackage ? selectedPackage.price.toFixed(2) : '0.00' }}
          </Button>
          <div class="payment-note">
            注意事项（使用前请务必查看无法退款之类的文字）
          </div>
        </div>
      </div>
    </Dialog>

    <!-- 支付弹窗 -->
    <PaymentDialog
      :visible="paymentDialogVisible"
      :package-id="selectedPackage?.id?.toString() || ''"
      @update:visible="paymentDialogVisible = $event"
      @paymentSuccess="handlePaymentSuccess"
      @close="paymentDialogVisible = false"
    />
  </div>
</template>

<style>
.vip-package-dialog {
  background-image: url('@/assets/images/pc/vip-bg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

/* 移动端适配 */
.vip-package-dialog-mobile {
  .el-dialog {
    margin: 5vh auto !important;
    max-height: 90vh;
    overflow-y: auto;
  }

  .el-dialog__header {
    padding: 15px 20px 10px !important;

    .el-dialog__title {
      font-size: 16px !important;
      line-height: 1.4 !important;
    }
  }

  .el-dialog__body {
    padding: 10px 15px 20px !important;
  }
}
</style>

<style lang="scss" scoped>
.dialog-content {
  text-align: center;
  padding: 20px 0;

  .subtitle {
    font-size: 14px;
    color: #666;
    margin-bottom: 30px;
  }

  // 移动端适配
  @media (max-width: 768px) {
    padding: 15px 0;

    .subtitle {
      font-size: 13px;
      margin-bottom: 20px;
      line-height: 1.4;
    }
  }

  .loading-container {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .package-list {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 30px;
    flex-wrap: wrap;

    // 移动端适配
    @media (max-width: 768px) {
      gap: 10px;
      margin-bottom: 20px;
      padding: 0 10px;
    }
  }

  .package-item {
    position: relative;
    width: 120px;
    height: 160px;
    background: #FFF9E6;
    border-radius: 8px;
    border: 2px solid transparent;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 15px 10px 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    overflow: hidden;

    // 移动端适配
    @media (max-width: 768px) {
      width: 100px;
      height: 140px;
      padding: 12px 8px 8px;
    }

    @media (max-width: 480px) {
      width: 90px;
      height: 130px;
      padding: 10px 6px 6px;
    }

    &:hover {
      border-color: #FFD700;
      transform: translateY(-2px);
    }

    &.selected {
      border-color: #1677FF;
      box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3);
    }

    .vip-active {
      position: absolute;
      bottom: -2px;
      right: -2px;
      width: 40px;
      height: 40px;
    }

    .package-header {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .duration {
        font-size: 14px;
        color: #333;
        margin-bottom: 8px;
        font-weight: 500;

        @media (max-width: 768px) {
          font-size: 12px;
          margin-bottom: 6px;
        }

        @media (max-width: 480px) {
          font-size: 11px;
          margin-bottom: 4px;
        }
      }

      .price {
        display: flex;
        align-items: baseline;
        margin-bottom: 4px;

        .currency {
          font-size: 14px;
          color: #333;

          @media (max-width: 768px) {
            font-size: 12px;
          }

          @media (max-width: 480px) {
            font-size: 10px;
          }
        }

        .amount {
          font-size: 20px;
          font-weight: bold;
          color: #333;

          @media (max-width: 768px) {
            font-size: 18px;
          }

          @media (max-width: 480px) {
            font-size: 16px;
          }
        }
      }

      .original-price {
        font-size: 12px;
        color: #999;
        text-decoration: line-through;

        @media (max-width: 768px) {
          font-size: 10px;
        }

        @media (max-width: 480px) {
          font-size: 9px;
        }
      }
    }
    .vip-icon {
      position: absolute;
      width: 60px;
      height: 60px;
      right: -14px;
      bottom: -10px;

      @media (max-width: 768px) {
        width: 50px;
        height: 50px;
        right: -12px;
        bottom: -8px;
      }

      @media (max-width: 480px) {
        width: 40px;
        height: 40px;
        right: -10px;
        bottom: -6px;
      }
    }

    .package-footer {
      .select-btn {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        padding: 0;
        font-size: 16px;
        background: #FFD700;
        border-color: #FFD700;
        color: #333;

        &:hover {
          background: #FFC107;
          border-color: #FFC107;
        }
      }
    }
  }

  .terms {
    margin-bottom: 20px;
    padding: 0 40px;

    p {
      font-size: 12px;
      color: #666;
      line-height: 1.5;
      margin: 0;
    }

    @media (max-width: 768px) {
      margin-bottom: 15px;
      padding: 0 20px;

      p {
        font-size: 11px;
        line-height: 1.4;
      }
    }

    @media (max-width: 480px) {
      margin-bottom: 12px;
      padding: 0 15px;

      p {
        font-size: 10px;
        line-height: 1.3;
      }
    }
  }

  .agreement {
    margin-bottom: 30px;
    font-size: 14px;
    color: #666;
    display: flex;
    justify-content: center;

    @media (max-width: 768px) {
      margin-bottom: 20px;
      font-size: 13px;
      padding: 0 20px;
    }

    @media (max-width: 480px) {
      margin-bottom: 15px;
      font-size: 12px;
      padding: 0 15px;
    }

    .agreement-checkbox {
      display: flex;
      align-items: center;

      :deep(.el-checkbox__label) {
        font-size: 14px;
        color: #666;
      }
    }

    .agreement-link {
      color: #1677FF;
      text-decoration: none;
      margin-left: 5px;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .payment-section {
    .payment-btn {
      background: #8B4513;
      border-color: #8B4513;
      padding: 12px 40px;
      font-size: 16px;
      font-weight: 500;

      &:hover {
        background: #A0522D;
        border-color: #A0522D;
      }

      @media (max-width: 768px) {
        padding: 10px 30px;
        font-size: 15px;
        width: 200px;
      }

      @media (max-width: 480px) {
        padding: 8px 25px;
        font-size: 14px;
        width: 180px;
      }

      &:disabled {
        background: #ccc;
        border-color: #ccc;
      }
    }

    .payment-note {
      font-size: 12px;
      color: #999;
      margin-top: 10px;
    }
  }
}
</style>
