# CommonComparativeMetricsPanel 对比指标面板组件

用于展示多个地区的指标对比数据的表格组件。

## 基本用法

```vue
<script setup lang="ts">
import type { ComparativeMetricsData, RegionConfig } from '@/components/CommonComparativeMetricsPanel/type'
import CommonComparativeMetricsPanel from '@/components/CommonComparativeMetricsPanel'

// 地区配置
const regions: RegionConfig[] = [
  { key: 'national', label: '全国' },
  { key: 'fujian', label: '福建省' },
  { key: 'fuzhou', label: '福州市' },
]

// 指标数据
const metricsData: ComparativeMetricsData[] = [
  {
    name: '注册资本',
    values: {
      national: 100,
      fujian: 100,
      fuzhou: 100,
    },
  },
  {
    name: '实缴资本',
    values: {
      national: 97.09,
      fujian: 97.79,
      fuzhou: 96.75,
    },
  },
  {
    name: '所有者权益',
    values: {
      national: 99.99,
      fujian: 99.99,
      fuzhou: 99.98,
    },
  },
  {
    name: '总资产',
    values: {
      national: 99.97,
      fujian: 99.99,
      fuzhou: 99.97,
    },
  },
  {
    name: '营业收入',
    values: {
      national: 99.95,
      fujian: 99.96,
      fuzhou: 99.93,
    },
  },
  {
    name: '净利润',
    values: {
      national: 99.95,
      fujian: 99.95,
      fuzhou: 99.92,
    },
  },
]

// 数值格式化
function formatValue(value: number): string {
  return value.toFixed(2)
}
</script>

<template>
  <CommonComparativeMetricsPanel
    :data="metricsData"
    :regions="regions"
    :formatter="formatValue"
  />
</template>
```

## 自定义插槽

```vue
<template>
  <CommonComparativeMetricsPanel
    :data="metricsData"
    :regions="regions"
  >
    <!-- 自定义特定指标和地区的显示 -->
    <template #value-注册资本-national="{ value, formattedValue }">
      <span style="color: #67c23a; font-weight: bold;">
        {{ formattedValue }}
      </span>
    </template>
  </CommonComparativeMetricsPanel>
</template>
```

## Props

| 参数 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| data | 指标数据列表 | `ComparativeMetricsData[]` | — | — |
| regions | 地区配置列表 | `RegionConfig[]` | — | — |
| formatter | 数值格式化函数 | `(value: number, metric: ComparativeMetricsData, region: RegionConfig) => string` | — | `(value) => value.toString()` |
| bordered | 是否显示边框 | `boolean` | — | `true` |
| size | 表格大小 | `string` | `large` / `medium` / `small` / `mini` | `medium` |

## 插槽

| 插槽名 | 说明 | 参数 |
|--------|------|------|
| value-{指标名}-{地区key} | 自定义特定指标和地区的数值显示 | `{ value, metric, region, formattedValue }` |

## 类型定义

```typescript
interface ComparativeMetricsData {
  /** 指标名称 */
  name: string
  /** 各地区的数值 */
  values: Record<string, number>
}

interface RegionConfig {
  /** 地区标识 */
  key: string
  /** 地区显示名称 */
  label: string
}
```
