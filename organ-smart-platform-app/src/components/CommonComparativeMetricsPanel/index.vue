<script setup lang="ts">
import type { ComparativeMetricsData, RegionConfig } from './type'

/** CommonComparativeMetricsPanel组件Props */
interface CommonComparativeMetricsPanelProps {
  /** 指标数据列表 */
  data: ComparativeMetricsData[]
  /** 地区配置列表 */
  regions: RegionConfig[]
  /** 数值格式化函数 */
  formatter?: (value: number, metric: ComparativeMetricsData, region: RegionConfig) => string
  /** 是否显示边框 */
  bordered?: boolean
  /** 表格大小 */
  size?: 'large' | 'medium' | 'small' | 'mini'
}

const props = withDefaults(defineProps<CommonComparativeMetricsPanelProps>(), {
  bordered: true,
  size: 'medium',
  formatter: (value: number) => value.toString(),
})

/**
 * 格式化数值显示
 */
function formatValue(value: number, metric: ComparativeMetricsData, region: RegionConfig): string {
  if (props.formatter) {
    return props.formatter(value, metric, region)
  }
  return value.toString()
}

/**
 * 获取表格样式类名
 */
const tableClass = computed(() => {
  return [
    'comparative-metrics-panel',
    `comparative-metrics-panel--${props.size}`,
    {
      'comparative-metrics-panel--bordered': props.bordered,
    },
  ]
})
</script>

<template>
  <div :class="tableClass">
    <table class="metrics-table">
      <!-- 表头 -->
      <thead>
        <tr>
          <th class="header-cell metric-header">
            <div class="header-content">
              <span class="region-label">地区</span>
              <span class="metric-label">指标·(分)</span>
            </div>
          </th>
          <th
            v-for="region in regions"
            :key="region.key"
            class="header-cell region-header"
          >
            {{ region.label }}
          </th>
        </tr>
      </thead>
      <!-- 表体 -->
      <tbody>
        <tr
          v-for="(metric, index) in data"
          :key="metric.name"
          class="data-row"
          :class="{ 'row-even': index % 2 === 1 }"
        >
          <td class="metric-name-cell">
            {{ metric.name }}
          </td>
          <td
            v-for="region in regions"
            :key="`${metric.name}-${region.key}`"
            class="value-cell"
          >
            <slot
              :name="`value-${metric.name}-${region.key}`"
              :value="metric.values[region.key]"
              :metric="metric"
              :region="region"
              :formatted-value="formatValue(metric.values[region.key] || 0, metric, region)"
            >
              {{ formatValue(metric.values[region.key] || 0, metric, region) }}
            </slot>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<style scoped>
.comparative-metrics-panel {
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.metrics-table {
  width: 100%;
  border-collapse: collapse;
  background-color: #fff;
}

/* 边框样式 */
.comparative-metrics-panel--bordered .metrics-table {
  border: 1px solid #e4e7ed;
}

.comparative-metrics-panel--bordered .header-cell,
.comparative-metrics-panel--bordered .metric-name-cell,
.comparative-metrics-panel--bordered .value-cell {
  border: 1px solid #e4e7ed;
}

/* 表头样式 */
.header-cell {
  background-color: #f5f7fa;
  color: #909399;
  font-weight: 500;
  text-align: center;
  padding: 12px 8px;
  position: relative;
}

.metric-header {
  background-color: #f0f2f5;
  position: relative;
}

.header-content {
  position: relative;
}

.region-label {
  position: absolute;
  top: -8px;
  right: 8px;
  font-size: 12px;
  color: #666;
}

.metric-label {
  position: absolute;
  bottom: -8px;
  left: 8px;
  font-size: 12px;
  color: #666;
}

/* 对角线样式 */
.metric-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom right, transparent 49%, #e4e7ed 49%, #e4e7ed 51%, transparent 51%);
  pointer-events: none;
}

.region-header {
  min-width: 80px;
}

/* 表体样式 */
.data-row {
  transition: background-color 0.2s;
}

.data-row:hover {
  background-color: #f5f7fa;
}

.row-even {
  background-color: #fafafa;
}

.row-even:hover {
  background-color: #f0f2f5;
}

.metric-name-cell {
  background-color: #f9f9f9;
  color: #606266;
  font-weight: 500;
  text-align: center;
  padding: 12px 16px;
  white-space: nowrap;
}

.value-cell {
  color: #303133;
  text-align: center;
  padding: 12px 8px;
  font-family: 'Courier New', Consolas, monospace;
}

/* 尺寸变体 */
.comparative-metrics-panel--large .header-cell,
.comparative-metrics-panel--large .metric-name-cell,
.comparative-metrics-panel--large .value-cell {
  padding: 16px 12px;
  font-size: 16px;
}

.comparative-metrics-panel--medium .header-cell,
.comparative-metrics-panel--medium .metric-name-cell,
.comparative-metrics-panel--medium .value-cell {
  padding: 12px 8px;
  font-size: 14px;
}

.comparative-metrics-panel--small .header-cell,
.comparative-metrics-panel--small .metric-name-cell,
.comparative-metrics-panel--small .value-cell {
  padding: 8px 6px;
  font-size: 13px;
}

.comparative-metrics-panel--mini .header-cell,
.comparative-metrics-panel--mini .metric-name-cell,
.comparative-metrics-panel--mini .value-cell {
  padding: 6px 4px;
  font-size: 12px;
}
</style>
