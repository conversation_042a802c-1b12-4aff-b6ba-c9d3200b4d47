/**
 * 对比指标面板组件的数据类型
 */
export interface ComparativeMetricsData {
  /** 指标名称 */
  name: string
  /** 各地区的数值 */
  values: Record<string, number>
}

/**
 * 地区配置类型
 */
export interface RegionConfig {
  /** 地区标识 */
  key: string
  /** 地区显示名称 */
  label: string
}

/**
 * CommonComparativeMetricsPanel 组件的 Props 类型
 * 注意：Vue 2 项目中，组件的 props 类型只能在组件内部声明
 * 这里的类型定义仅供外部引用时的类型提示使用
 */
export interface CommonComparativeMetricsPanelProps {
  /** 指标数据列表 */
  data: ComparativeMetricsData[]
  /** 地区配置列表 */
  regions: RegionConfig[]
  /** 数值格式化函数 */
  formatter?: (value: number, metric: ComparativeMetricsData, region: RegionConfig) => string
  /** 是否显示边框 */
  bordered?: boolean
  /** 表格大小 */
  size?: 'large' | 'medium' | 'small' | 'mini'
}
