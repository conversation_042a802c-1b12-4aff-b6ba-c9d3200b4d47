<template>
  <div class="test-container">
    <h1>GroupCell Badge 功能测试</h1>

    <!-- 测试badge最大值功能 -->
    <h2>1. Badge最大值测试</h2>
    <GroupCell :cell-list="maxValueTestList" />

    <!-- 测试badge背景色功能 -->
    <h2>2. Badge背景色测试</h2>
    <GroupCell :cell-list="backgroundColorTestList" />

    <!-- 测试混合功能 -->
    <h2>3. 混合功能测试</h2>
    <GroupCell :cell-list="mixedTestList" />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import GroupCell from "./index.vue";
import { CellData } from "./types";

// 测试badge最大值功能
const maxValueTestList = ref<CellData[]>([
  {
    title: "数字50（默认最大值99）",
    value: "正常显示",
    badge: 50,
  },
  {
    title: "数字100（默认最大值99）",
    value: "显示99+",
    badge: 100,
  },
  {
    title: "数字200（自定义最大值150）",
    value: "显示150+",
    badge: 200,
    badgeMaxValue: 150,
  },
  {
    title: "文本Badge",
    value: "正常显示",
    badge: "New",
  },
  {
    title: "字符串数字（可转换）",
    value: "显示99+",
    badge: "120",
  },
]);

// 测试badge背景色功能
const backgroundColorTestList = ref<CellData[]>([
  {
    title: "默认红色",
    value: "默认背景色",
    badge: 5,
  },
  {
    title: "蓝色背景",
    value: "自定义蓝色",
    badge: 10,
    badgeBgColor: "#409eff",
  },
  {
    title: "绿色背景",
    value: "自定义绿色",
    badge: 15,
    badgeBgColor: "#67c23a",
  },
  {
    title: "橙色背景",
    value: "自定义橙色",
    badge: 20,
    badgeBgColor: "#e6a23c",
  },
]);

// 测试混合功能
const mixedTestList = ref<CellData[]>([
  {
    title: "混合功能1",
    value: "蓝色背景+最大值50",
    badge: 80,
    badgeMaxValue: 50,
    badgeBgColor: "#409eff",
  },
  {
    title: "混合功能2",
    value: "绿色背景+文本",
    badge: "Hot",
    badgeBgColor: "#67c23a",
  },
  {
    title: "混合功能3",
    value: "紫色背景+最大值10",
    badge: 25,
    badgeMaxValue: 10,
    badgeBgColor: "#9c27b0",
  },
]);
</script>

<style lang="scss" scoped>
.test-container {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;

  h1 {
    color: #333;
    text-align: center;
    margin-bottom: 30px;
  }

  h2 {
    color: #666;
    margin: 30px 0 15px 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
  }
}
</style>
