<template>
  <div class="fix-test-container">
    <h1>GroupCell & Cell 组件修复验证</h1>
    
    <!-- 垂直居中测试 -->
    <section>
      <h2>1. 垂直居中测试</h2>
      <p>测试右侧内容区域（badge、value、rightIcon）的垂直居中对齐</p>
      <GroupCell :cell-list="alignmentTestList" />
    </section>

    <!-- 样式定制测试 -->
    <section>
      <h2>2. 样式定制测试</h2>
      <p>测试左侧title和右侧value的样式定制功能</p>
      <GroupCell :cell-list="styledTestList" />
    </section>

    <!-- 混合功能测试 -->
    <section>
      <h2>3. 混合功能测试</h2>
      <p>测试样式定制与垂直居中的组合效果</p>
      <GroupCell :cell-list="mixedTestList" />
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import GroupCell from "./index.vue";
import { CellData } from "./types";

/**
 * 垂直居中测试数据
 * 测试多种右侧元素组合的垂直居中效果
 */
const alignmentTestList = ref<CellData[]>([
  {
    title: "Badge + Value + Icon",
    value: "长文本内容测试垂直居中效果",
    badge: 99,
    rightIcon: "el-icon-arrow-right",
  },
  {
    title: "Badge + Icon (无Value)",
    badge: "New",
    badgeBgColor: "#67c23a",
    rightIcon: "el-icon-arrow-right",
  },
  {
    title: "Value + Icon (无Badge)",
    value: "查看详情",
    rightIcon: "el-icon-arrow-right",
  },
  {
    title: "仅Badge",
    badge: 5,
  },
  {
    title: "仅Value",
    value: "简单文本",
  },
  {
    title: "仅Icon",
    rightIcon: "el-icon-arrow-right",
  },
]);

/**
 * 样式定制测试数据
 * 测试titleStyle和valueStyle的各种样式配置
 */
const styledTestList = ref<CellData[]>([
  {
    title: "大号蓝色标题",
    value: "大号红色值",
    titleStyle: {
      fontSize: "20px",
      color: "#409eff",
      fontWeight: "bold",
    },
    valueStyle: {
      fontSize: "18px",
      color: "#f56c6c",
      fontWeight: "600",
    },
  },
  {
    title: "小号灰色标题",
    value: "小号绿色值",
    titleStyle: {
      fontSize: "12px",
      color: "#909399",
      fontWeight: "300",
    },
    valueStyle: {
      fontSize: "10px",
      color: "#67c23a",
      fontStyle: "italic",
    },
  },
  {
    title: "自定义颜色组合",
    value: "紫色斜体文本",
    titleStyle: {
      color: "#e6a23c",
      fontWeight: "500",
      textDecoration: "underline",
    },
    valueStyle: {
      color: "#9c27b0",
      fontStyle: "italic",
      fontWeight: "bold",
    },
  },
]);

/**
 * 混合功能测试数据
 * 测试样式定制与垂直居中、badge等功能的组合效果
 */
const mixedTestList = ref<CellData[]>([
  {
    title: "样式化标题",
    value: "样式化值",
    badge: 88,
    rightIcon: "el-icon-arrow-right",
    titleStyle: {
      fontSize: "16px",
      color: "#409eff",
      fontWeight: "bold",
    },
    valueStyle: {
      fontSize: "14px",
      color: "#67c23a",
      fontWeight: "500",
    },
  },
  {
    title: "特殊样式",
    value: "特殊值",
    badge: "Hot",
    badgeBgColor: "#ff4757",
    rightIcon: "el-icon-star-on",
    titleStyle: {
      fontSize: "18px",
      color: "#e6a23c",
      fontWeight: "600",
      textShadow: "1px 1px 2px rgba(0,0,0,0.1)",
    },
    valueStyle: {
      fontSize: "16px",
      color: "#9c27b0",
      fontStyle: "italic",
      textDecoration: "underline",
    },
  },
]);
</script>

<style lang="scss" scoped>
.fix-test-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;

  h1 {
    text-align: center;
    color: #333;
    margin-bottom: 30px;
  }

  section {
    margin-bottom: 40px;

    h2 {
      color: #409eff;
      margin-bottom: 10px;
      font-size: 18px;
    }

    p {
      color: #666;
      margin-bottom: 15px;
      font-size: 14px;
      line-height: 1.5;
    }
  }
}
</style>
