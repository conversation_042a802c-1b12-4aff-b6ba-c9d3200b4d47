/**
 * Cell数据接口定义
 */
export interface CellData<T = any> {
  /** 左侧图标类名 */
  leftIcon?: string;
  /** 右侧图标类名 */
  rightIcon?: string;
  /** 标题文本 */
  title?: string;
  /** 值文本 */
  value?: string;
  /** 角标内容 */
  badge?: string | number | null;
  /** 角标最大值，超过时显示最大值+ */
  badgeMaxValue?: number;
  /** 角标背景色 */
  badgeBgColor?: string;
  /** 标题样式配置 */
  titleStyle?: {
    fontSize?: string;
    color?: string;
    fontWeight?: string | number;
    [key: string]: any;
  };
  /** 值样式配置 */
  valueStyle?: {
    fontSize?: string;
    color?: string;
    fontWeight?: string | number;
    [key: string]: any;
  };
  /** 自定义数据，用于点击事件回调 */
  data?: T;
}

/** 功能Cell点击事件数据 */
export interface CellClickPayload<T = any> {
  cell: CellData<T>;
  index: number;
}
