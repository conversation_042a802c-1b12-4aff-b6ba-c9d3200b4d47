/**
 * CommonScroll 组件相关类型定义
 */

/**
 * 滚动信息接口
 */
export interface ScrollInfo {
  /** 滚动距离顶部的距离 */
  scrollTop: number
  /** 滚动内容的总高度 */
  scrollHeight: number
  /** 可视区域高度 */
  clientHeight: number
  /** 是否在顶部 */
  isAtTop: boolean
  /** 是否在底部 */
  isAtBottom: boolean
}

/**
 * CommonScroll 组件实例方法
 */
export interface CommonScrollInstance {
  /** 滚动到顶部 */
  scrollToTop: (smooth?: boolean) => void
  /** 滚动到底部 */
  scrollToBottom: (smooth?: boolean) => void
  /** 滚动到指定位置 */
  scrollTo: (top: number, smooth?: boolean) => void
  /** 获取当前滚动信息 */
  getScrollInfo: () => ScrollInfo
  /** Scrollbar 组件引用 */
  scrollbarRef: any
}
