<script setup lang="ts">
import { Scrollbar } from 'element-ui'

/**
 * 组件属性
 */
interface Props {
  /**
   * 距离底部多少像素时触发加载更多
   * @default 50
   */
  threshold?: number

  /**
   * 是否禁用滑动到底部检测
   * @default false
   */
  disabled?: boolean
}

/**
 * 组件事件
 */
interface Emits {
  /**
   * 滑动到底部时触发
   */
  (e: 'reach-bottom'): void

  /**
   * 滚动事件
   * @param scrollTop 滚动距离顶部的距离
   * @param scrollHeight 滚动内容的总高度
   * @param clientHeight 可视区域高度
   */
  (e: 'scroll', scrollTop: number, scrollHeight: number, clientHeight: number): void
}

const props = withDefaults(defineProps<Props>(), {
  threshold: 50,
  disabled: false,
})

const emit = defineEmits<Emits>()

const vm = getCurrentInstance()
const scrollbarRef = ref()

// 节流控制，避免频繁触发事件
let isThrottled = false
const throttleDelay = 100

/**
 * 处理滚动事件
 */
function handleScroll(event: Event) {
  if (props.disabled || isThrottled)
    return

  const scrollElement = event.target as HTMLElement
  const { scrollTop, scrollHeight, clientHeight } = scrollElement

  // 发送滚动事件
  emit('scroll', scrollTop, scrollHeight, clientHeight)

  // 计算是否接近底部
  const distanceToBottom = scrollHeight - scrollTop - clientHeight

  // 如果距离底部小于阈值，触发到达底部事件
  if (distanceToBottom <= props.threshold) {
    emit('reach-bottom')

    // 节流控制，防止重复触发
    isThrottled = true
    setTimeout(() => {
      isThrottled = false
    }, throttleDelay)
  }
}

/**
 * 滚动到顶部
 */
function scrollToTop(smooth = true) {
  const scrollElement = getScrollElement()
  if (scrollElement) {
    scrollElement.scrollTo({
      top: 0,
      behavior: smooth ? 'smooth' : 'auto',
    })
  }
}

/**
 * 滚动到底部
 */
function scrollToBottom(smooth = true) {
  const scrollElement = getScrollElement()
  if (scrollElement) {
    scrollElement.scrollTo({
      top: scrollElement.scrollHeight,
      behavior: smooth ? 'smooth' : 'auto',
    })
  }
}

/**
 * 滚动到指定位置
 */
function scrollTo(top: number, smooth = true) {
  const scrollElement = getScrollElement()
  if (scrollElement) {
    scrollElement.scrollTo({
      top,
      behavior: smooth ? 'smooth' : 'auto',
    })
  }
}

/**
 * 获取滚动元素
 */
function getScrollElement(): HTMLElement | null {
  if (!vm?.proxy?.$el)
    return null
  return vm.proxy.$el.querySelector('.el-scrollbar__wrap')
}

/**
 * 获取当前滚动信息
 */
function getScrollInfo() {
  const scrollElement = getScrollElement()
  if (!scrollElement) {
    return {
      scrollTop: 0,
      scrollHeight: 0,
      clientHeight: 0,
      isAtTop: true,
      isAtBottom: false,
    }
  }

  const { scrollTop, scrollHeight, clientHeight } = scrollElement
  return {
    scrollTop,
    scrollHeight,
    clientHeight,
    isAtTop: scrollTop === 0,
    isAtBottom: scrollHeight - scrollTop - clientHeight <= props.threshold,
  }
}

// 组件挂载后绑定滚动事件
onMounted(() => {
  const scrollElement = getScrollElement()
  if (scrollElement) {
    scrollElement.addEventListener('scroll', handleScroll, { passive: true })
  }
})

// 组件卸载前清理事件监听
onUnmounted(() => {
  const scrollElement = getScrollElement()
  if (scrollElement) {
    scrollElement.removeEventListener('scroll', handleScroll)
  }
})

// 暴露方法给父组件
defineExpose({
  scrollToTop,
  scrollToBottom,
  scrollTo,
  getScrollInfo,
  scrollbarRef,
})
</script>

<template>
  <div class="common-scroll flex-col">
    <slot name="header" />
    <div class="flex-1 overflow-hidden">
      <Scrollbar ref="scrollbarRef" class="scrollbar">
        <slot />
      </Scrollbar>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.common-scroll {
  width: 100%;
  height: 100%;

  .scrollbar {
    width: 100%;
    height: 100%;
  }
}
</style>
