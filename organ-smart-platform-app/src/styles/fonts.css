/**
 * 自定义字体定义文件
 * 包含项目中使用的所有自定义字体
 */

/* 优设标题黑体 - 用于重要标题 */
@font-face {
  font-family: 'YouSheBiaoTiHei';
  src: url('@/assets/front/YouSheBiaoTiHei-2.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap; /* 优化字体加载性能 */
}

/* 自定义字体工具类 */
.font-youshe-title {
  font-family: 'YouSheBiaoTiHei', 'Microsoft YaHei', '微软雅黑', 'PingFang SC', 'Hiragino Sans GB', Arial, sans-serif !important;
}

/* 企业信用信息标题专用类 */
.enterprise-credit-title {
  font-family: 'YouSheBiaoTiHei', 'Microsoft YaHei', '微软雅黑', 'PingFang SC', 'Hiragino Sans GB', Arial, sans-serif !important;
  font-weight: normal;
  letter-spacing: 0.5px; /* 增加字间距，提升可读性 */
}

/* 响应式字体大小 */
@media (max-width: 768px) {
  .enterprise-credit-title {
    letter-spacing: 0.3px; /* 移动端减少字间距 */
  }
}

/* 字体预加载提示 */
.font-loading {
  font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  transition: font-family 0.3s ease;
}

.font-loaded .font-loading {
  font-family: 'YouSheBiaoTiHei', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}
