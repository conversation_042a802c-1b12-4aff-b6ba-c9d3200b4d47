<script setup lang="ts">
// import defaultLayout from "./components/default/index.vue";
import mobile from "./components/mobile/index.vue";
// import pc from "./components/pc/index.vue";
</script>

<template>
  <div class="layout">
    <mobile />
    <!-- <pc v-if="$route.path.startsWith('/pc')" />
    <mobile v-else-if="$route.path.startsWith('/mobile')" />
    <defaultLayout v-else /> -->
  </div>
</template>

<style lang="scss" scoped>
.layout {
  width: 100%;
  height: 100%;
}
</style>
