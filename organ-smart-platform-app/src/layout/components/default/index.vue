<script setup lang="ts">
import { Menu, MenuItem, Submenu } from 'element-ui'
import { cloneDeep } from 'lodash'
import { computed, ref } from 'vue'

const router = useRouter()
const route = useRoute()

/**
 * 递归过滤路由，移除 meta.menu 为 false 的路由
 * @param routes 路由数组
 * @returns 过滤后的路由数组
 */
function filterMenuRoutes(routes: any[]): any[] {
  return routes
    .filter(route => route.meta?.menu !== false)
    .map(route => ({
      ...route,
      children: route.children ? filterMenuRoutes(route.children) : undefined,
    }))
}

// 使用 cloneDeep 复制路由副本，避免修改原始路由
const routesCopy = cloneDeep(router.options.routes || [])

// 递归过滤掉 meta.menu 为 false 的路由
const menus = filterMenuRoutes(routesCopy)

// 当前激活的菜单项
const activeMenu = computed(() => route.path)

// 默认展开的子菜单
const defaultOpeneds = ref<string[]>([])

// 计算属性：有子菜单的路由
const menusWithChildren = computed(() =>
  menus.filter(route => route.children && route.children.length > 0),
)

// 计算属性：没有子菜单的路由
const menusWithoutChildren = computed(() =>
  menus.filter(route => !route.children || route.children.length === 0),
)

/**
 * 获取有子菜单的子路由
 * @param children 子路由数组
 */
function getChildrenWithSubmenus(children: any[]) {
  return children.filter(child => child.children && child.children.length > 0)
}

/**
 * 获取没有子菜单的子路由
 * @param children 子路由数组
 */
function getChildrenWithoutSubmenus(children: any[]) {
  return children.filter(child => !child.children || child.children.length === 0)
}

/**
 * 处理菜单点击事件
 * @param index 菜单索引（路由路径）
 */
function handleMenuSelect(index: string) {
  if (index !== route.path) {
    router.push(index)
  }
}

console.log('过滤后的菜单数据:', menus)
</script>

<template>
  <div class="layout-container">
    <!-- 侧边菜单 -->
    <div class="sidebar">
      <Menu
        :default-active="activeMenu"
        :default-openeds="defaultOpeneds"
        mode="vertical"
        background-color="#ffffff"
        text-color="#222222"
        active-text-color="#1658D9"
        @select="handleMenuSelect"
      >
        <!-- 有子菜单的情况 -->
        <Submenu
          v-for="menuRoute in menusWithChildren"
          :key="menuRoute.path"
          :index="menuRoute.path"
        >
          <template #title>
            <span>{{ menuRoute.meta?.title || menuRoute.name }}</span>
          </template>

          <!-- 二级菜单 -->
          <Submenu
            v-for="child in getChildrenWithSubmenus(menuRoute.children)"
            :key="child.path"
            :index="child.path"
          >
            <template #title>
              <span>{{ child.meta?.title || child.name }}</span>
            </template>
            <!-- 三级菜单 -->
            <MenuItem
              v-for="grandChild in child.children"
              :key="grandChild.path"
              :index="grandChild.path"
            >
              {{ grandChild.meta?.title || grandChild.name }}
            </MenuItem>
          </Submenu>

          <!-- 二级菜单项（没有子菜单） -->
          <MenuItem
            v-for="child in getChildrenWithoutSubmenus(menuRoute.children)"
            :key="child.path"
            :index="child.path"
          >
            {{ child.meta?.title || child.name }}
          </MenuItem>
        </Submenu>

        <!-- 没有子菜单的情况 -->
        <MenuItem
          v-for="menuRoute in menusWithoutChildren"
          :key="menuRoute.path"
          :index="menuRoute.path"
        >
          {{ menuRoute.meta?.title || menuRoute.name }}
        </MenuItem>
      </Menu>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <router-view />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.layout-container {
  display: flex;
  height: 100vh;

  .sidebar {
    width: 260px;
    background-color: #ffffff;
    overflow-y: auto;

    :deep(.el-menu) {
      border-right: none;

      .el-menu-item {
        height: 50px;
        line-height: 50px;
        padding-left: 20px !important;

        &:hover {
          background-color: #434a50;
        }

        &.is-active {
          background-color: #409eff;
          color: #fff;
        }
      }

      .el-submenu {
        .el-submenu__title {
          height: 50px;
          line-height: 50px;
          padding-left: 20px !important;

          &:hover {
            background-color: #434a50;
          }
        }

        .el-menu {
          background-color: #434a50;

          .el-menu-item {
            padding-left: 40px !important;
            background-color: #434a50;

            &:hover {
              background-color: #383f45;
            }

            &.is-active {
              background-color: #409eff;
            }
          }

          .el-submenu {
            .el-submenu__title {
              padding-left: 40px !important;
              background-color: #434a50;

              &:hover {
                background-color: #383f45;
              }
            }

            .el-menu-item {
              padding-left: 60px !important;
              background-color: #383f45;

              &:hover {
                background-color: #2d3339;
              }

              &.is-active {
                background-color: #409eff;
              }
            }
          }
        }
      }
    }
  }

  .main-content {
    flex: 1;
    padding: 20px;
    background-color: #f5f5f5;
    overflow-y: auto;
  }
}
</style>
