<script setup lang="ts"></script>

<template>
  <div class="mobile-layout flex-center">
    <div class="mobile-max-width">
      <router-view />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.mobile-layout {
  width: 100%;
  height: 100%;
  .mobile-max-width {
    width: 100%;
    max-width: 1024px;
    height: 100%;
    background-color: #f6f7fb;
    // background-image: url('@/assets/images/mobile/mobile-layout-bg.png');
    // background-position: top center;
    // background-repeat: no-repeat;
    // background-size: 100% auto;
  }
}
</style>
