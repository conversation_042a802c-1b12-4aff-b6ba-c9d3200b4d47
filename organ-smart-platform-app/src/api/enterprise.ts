import type { AnyObject } from '@/hooks/useReportDetail/type'
import service from '@/service'

/**
 * 获取企业基本信息详情
 */
export function getEnterpriseDetail(params: {
  key: string
  keyType: string
}) {
  return service.post<{}, EnterpriseDetailVO>('/enterpriseDetail/get', {
    ...params,
    version: 'A1',
  }).then(res => res?.result?.basic)
}

/**
 * 获取企业综合评价得分
 */
export function getBnScore(params: {
  key: string
  keyType: string
}) {
  return service.post<{}, BnScoreVO>('/bnScore/get', params)
}

/**
 * 获取企业招聘风险
 */
export function getBnScoreJobRisk(params: {
  key: string
  keyType: string
}) {
  return service.post<{}, JobRiskVO>('/bnScore/jobRisk', params)
}

/**
 * JobRiskVO
 */
export interface JobRiskVO {
  /**
   * 劳动争议案件数量
   */
  a0101?: number
  /**
   * 社保缴纳合规率得分
   */
  a0102?: number
  /**
   * 监管与法律诉讼事件数量
   */
  a0201?: number
  /**
   * 财务信用风险事件数量
   */
  a0301?: number
  /**
   * 股权出质数量
   */
  a0401?: number
  /**
   * 合同纠纷数量
   */
  a0402?: number
  /**
   * 负面舆情数量
   */
  a0501?: number
  /**
   * 资产负债率得分
   */
  b0101?: number
  /**
   * 净利润得分
   */
  b0102?: number
  /**
   * 营业总收入得分
   */
  b0103?: number
  /**
   * 福利覆盖岗位数量
   */
  b0201?: number
  /**
   * 福利关键词（未去重，词云展示用）
   */
  benefit?: string
  /**
   * 专利权人总量
   */
  c0101?: number
  /**
   * 专利授权数量
   */
  c0201?: number
  /**
   * 软件著作权数量
   */
  c0202?: number
  /**
   * 作品著作权数量
   */
  c0203?: number
  /**
   * 技术标准制定数量
   */
  c0204?: number
  /**
   * 施引专利数量
   */
  c0301?: number
  /**
   * 专利转让次数
   */
  c0302?: number
  /**
   * 专利质押次数
   */
  c0303?: number
  /**
   * 专利许可次数
   */
  c0304?: number
  /**
   * 市场占有率得分
   */
  d0101?: number
  /**
   * 企业扩张能力得分
   */
  d0102?: number
  /**
   * 权益乘数得分
   */
  d0201?: number
  /**
   * 负债总额增长率得分
   */
  d0202?: number
  /**
   * 资产周转率得分
   */
  d0203?: number
  /**
   * 行业利润增长率得分
   */
  e0101?: number
  /**
   * 行业营收增长率得分
   */
  e0102?: number
  /**
   * 政策支持数量
   */
  e0103?: number
  /**
   * 岗位招聘人数
   */
  e0201?: number
  /**
   * 平均净资产收益率得分
   */
  e0301?: number
  /**
   * 融资事件数量
   */
  e0302?: number
  /**
   * 一级指标-企业负面信息纬度得分
   */
  indexA?: number
  /**
   * 一级指标-企业负面信息纬度得分全国排名
   */
  indexARank?: number
  /**
   * 一级指标-企业负面信息纬度得分地区排名
   */
  indexARegionRank?: number
  /**
   * 一级指标-薪资吸引能力纬度得分
   */
  indexB?: number
  /**
   * 一级指标-薪资吸引能力纬度得分全国排名
   */
  indexBRank?: number
  /**
   * 一级指标-薪资吸引能力纬度得分地区排名
   */
  indexBRegionRank?: number
  /**
   * 一级指标-技术成长潜力维度得分
   */
  indexC?: number
  /**
   * 一级指标-技术成长潜力维度得分全国排名
   */
  indexCRank?: number
  /**
   * 一级指标-技术成长潜力维度得分地区排名
   */
  indexCRegionRank?: number
  /**
   * 一级指标-行业竞争能力纬度得分
   */
  indexD?: number
  /**
   * 一级指标-行业竞争能力纬度得分全国排名
   */
  indexDRank?: number
  /**
   * 一级指标-行业竞争能力纬度得分地区排名
   */
  indexDRegionRank?: number
  /**
   * 一级指标-职业发展前景纬度得分
   */
  indexE?: number
  /**
   * 一级指标-职业发展前景纬度得分全国排名
   */
  indexERank?: number
  /**
   * 一级指标-职业发展前景纬度得分地区排名
   */
  indexERegionRank?: number
  /**
   * 总分
   */
  indexTotal?: number
  /**
   * 总分全国排名
   */
  indexTotalRank?: number
  /**
   * 总分地区排名
   */
  indexTotalRegionRank?: number
  /**
   * 是否扣费(1:收费,0:不收费)
   */
  isVerify?: string
  /**
   * 查询关键字
   */
  key?: string
  /**
   * 查询类型
   */
  keyType?: string
  /**
   * 行业ID
   */
  nicId?: string
  /**
   * 订单编号
   */
  ordernum?: string
  /**
   * 地区ID
   */
  regionId?: string
}

/**
 * EnterpriseDetailVO
 */
export interface EnterpriseDetailVO {
  /**
   * 状态码
   */
  code?: string
  /**
   * 是否扣费
   */
  isVerify?: string
  /**
   * 信息
   */
  message?: string
  /**
   * 订单编号
   */
  ordernum?: string
  /**
   * 业务数据
   */
  result?: EnterpriseBasicInfo
}

/**
 * 业务数据
 *
 * EnterpriseBasicInfo
 */
export interface EnterpriseBasicInfo {
  /**
   * 节点相关数据
   */
  basic?: BasicData
  /**
   * 出参节点列表
   */
  versions?: string[]
}

/**
 * 节点相关数据
 *
 * BasicData
 */
export interface BasicData {
  /**
   * 许可经营项目（老字段不更新）
   */
  abuitem?: string
  /**
   * 最后年检日期
   */
  ancheyear?: string
  /**
   * 核准日期
   */
  apprdate?: string
  /**
   * 注销日期
   */
  candate?: string
  /**
   * 一般经营项目（老字段不更新）
   */
  cbuitem?: string
  /**
   * 市
   */
  city?: string
  /**
   * 常用地址
   */
  commonAddr?: string
  /**
   * 区
   */
  district?: string
  /**
   * 地址
   */
  dom?: string
  /**
   * 邮箱
   */
  email?: string
  /**
   * 员工人数
   */
  empnum?: number
  /**
   * 英文名
   */
  eName?: string
  /**
   * 企业唯一id
   */
  entid?: string
  /**
   * 企业logo
   */
  entLog?: string
  /**
   * 企业名称
   */
  entname?: string
  /**
   * 公司状态
   */
  entstatus?: string
  /**
   * 企业类型中文
   */
  enttype?: string
  /**
   * 企业类型中文代码
   */
  enttypeId?: string
  /**
   * 成立日期
   */
  esdate?: string
  /**
   * 法人名称
   */
  frname?: string
  /**
   * 法人类型
   */
  frtype?: string
  /**
   * 企业曾用名
   */
  historyname?: string
  /**
   * 是否上市
   */
  isListed?: number
  /**
   * 经度（高德）
   */
  lat?: number
  /**
   * 上市时间
   */
  listedDate?: string
  /**
   * 纬度（高德）
   */
  lng?: number
  /**
   * 销售收入
   */
  maibusinc?: number
  /**
   * 组织机构代码
   */
  nacaoid?: string
  /**
   * 行业门类
   */
  nic1Name?: string
  /**
   * 二级行业
   */
  nic2Name?: string
  /**
   * 三级行业
   */
  nic3Name?: string
  /**
   * 四级行业
   */
  nic4Name?: string
  /**
   * 行业码值
   */
  nicId?: string
  /**
   * 行业名称
   */
  nicName?: string
  /**
   * 营业期限起始
   */
  opfrom?: string
  /**
   * 营业范围
   */
  oploc?: string
  /**
   * 经营范围及方式（老字段不更新）
   */
  opscoandform?: string
  /**
   * 经营(业务)范围
   */
  opscope?: string
  /**
   * 营业期限终止
   */
  opto?: string
  /**
   * 组织机构批准单位
   */
  orgName?: string
  /**
   * 省份
   */
  province?: string
  /**
   * 纳税信用评级
   */
  rating?: string
  /**
   * A级纳税人年份
   */
  ratYear?: string
  /**
   * 实缴资本
   */
  reccap?: number
  /**
   * 注册资本
   */
  regcap?: number
  /**
   * 注册资本中文字段
   */
  regcapcn?: string
  /**
   * 注册资本币种
   */
  regcapcur?: string
  /**
   * 所在地区码值
   */
  regionId?: string
  /**
   * 所在地区名称
   */
  regionName?: string
  /**
   * 注册号
   */
  regno?: string
  /**
   * 注册机关
   */
  regorg?: string
  /**
   * 吊销日期
   */
  revdate?: string
  /**
   * 参保人数
   */
  socnum?: number
  /**
   * 纳税人识别号
   */
  taxid?: string
  /**
   * 税务机关
   */
  taxOrg?: string
  /**
   * 电话
   */
  tel?: string
  /**
   * 纳税人类型码值
   */
  tstype?: string
  /**
   * 纳税人类型名称
   */
  tstypeName?: string
  /**
   * 统一代码
   */
  uniscid?: string
  /**
   * 企业官网
   */
  website?: string
}

/**
 * BnScoreVO
 */
export interface BnScoreVO {
  /**
   * 创建时间
   */
  createTime?: string
  /**
   * 创建人
   */
  creator?: string
  /**
   * 统一社会信用代码
   */
  creditCode?: string
  /**
   * 详细得分A
   */
  detailsA?: MapObject
  /**
   * 详细得分B
   */
  detailsB?: MapObject
  /**
   * 详细得分C
   */
  detailsC?: MapObject
  /**
   * 详细得分D
   */
  detailsD?: MapObject
  /**
   * 详细得分E
   */
  detailsE?: MapObject
  /**
   * 企业名称
   */
  enterpriseName?: string
  /**
   * 主键ID
   */
  id?: number
  /**
   * 指标A得分
   */
  indexA?: number
  /**
   * 指标B得分
   */
  indexB?: number
  /**
   * 指标C得分
   */
  indexC?: number
  /**
   * 指标D得分
   */
  indexD?: number
  /**
   * 指数日期
   */
  indexDate?: string
  /**
   * 指标E得分
   */
  indexE?: number
  /**
   * 总分
   */
  indexTotal?: number
  /**
   * 是否验证
   */
  isVerify?: string
  /**
   * 查询关键字
   */
  key?: string
  /**
   * 查询类型
   */
  keyType?: string
  /**
   * 订单号
   */
  ordernum?: string
  /**
   * 租户ID
   */
  tenantId?: number
  /**
   * 更新时间
   */
  updateTime?: string
}

/**
 * 详细得分A
 *
 * MapObject
 *
 * 详细得分B
 *
 * 详细得分C
 *
 * 详细得分D
 *
 * 详细得分E
 */
export type MapObject = AnyObject
