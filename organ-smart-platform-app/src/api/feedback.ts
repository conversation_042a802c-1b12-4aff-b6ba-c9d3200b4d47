import service from '@/service'

/**
 * 创建意见反馈数据
 */
export function createFeedback(params: FeedbackCreateReqDTO) {
  return service.post<FeedbackCreateReqDTO>('/feedback/create', params)
}

/**
 * FeedbackCreateReqDTO
 */
export interface FeedbackCreateReqDTO {
  /**
   * 联系电话
   */
  contactPhone?: string
  /**
   * 反馈内容
   */
  content: string
  /**
   * 反馈类型(bug/suggestion/complaint/other)
   */
  feedbackType: string
  /**
   * 反馈标题
   */
  title: string
  /**
   * 用户ID
   */
  userId?: number
}
