import service from '@/service'

/**
 * UserMembershipVO
 */
export interface UserMembershipVO {
  /**
   * 是否自动续费
   */
  autoRenew: boolean
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 到期时间
   */
  expireTime: string
  /**
   * ID
   */
  id: number
  /**
   * 是否已过期
   */
  isExpired: boolean
  /**
   * 会员类型
   */
  membershipType: MembershipType
  /**
   * 会员类型描述
   */
  membershipTypeDesc: string
  /**
   * 剩余天数
   */
  remainingDays: number
  /**
   * 开始时间
   */
  startTime: string
  /**
   * 状态
   */
  status: Status
  /**
   * 状态描述
   */
  statusDesc: string
  /**
   * 租户ID
   */
  tenantId: number
  /**
   * 用户ID
   */
  userId: number
}

/**
 * 会员类型
 */
export enum MembershipType {
  Basic = 'BASIC',
  Premium = 'PREMIUM',
  Vip = 'VIP',
}

/**
 * 状态
 */
export enum Status {
  Active = 'ACTIVE',
  Cancelled = 'CANCELLED',
  Expired = 'EXPIRED',
}

/**
 * 获取用户信息
 * @param userId 用户id
 * @returns
 */
export function getUserInfo(userId: string) {
  return service.get<string, UserMembershipVO>(`/userMembership/info/${userId}`)
}
