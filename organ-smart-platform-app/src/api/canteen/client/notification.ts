import service from "@/service";
import { BIZ_MODULE_API_PREFIX } from "@/api/canteen/constant";
import { IUser } from "./types/user";

/** 模块API前缀 */
const MODULE_API_PREFIX = `${BIZ_MODULE_API_PREFIX}/notification`;

/** 是否模拟数据 */
const isMock = true;

/** 获取我的未读消息数 */
export function getUnreadNotificationCount() {
  if (isMock) {
    return new Promise<number>((resolve) => {
      resolve(100);
    });
  }
  return service.get<any, number>(`${MODULE_API_PREFIX}/unreadCount`);
}
