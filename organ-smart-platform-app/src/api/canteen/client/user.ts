import service from "@/service";
import { BIZ_MODULE_API_PREFIX } from "@/api/canteen/constant";
import { IUser } from "./types/user";

/** 模块API前缀 */
const MODULE_API_PREFIX = `${BIZ_MODULE_API_PREFIX}/user`;

/** 是否模拟数据 */
const isMock = true;

/**
 * 获取当前登录用户信息
 */
export function getCurrentUserInfo() {
  if (isMock) {
    return new Promise<IUser>((resolve) => {
      const user: IUser = {
        id: "1",
        username: "张三",
        mobile: "13800138000",
        dept: "数字中心",
        canteenId: "1",
        canteenCode: "Canteen1",
        canteenName: "学苑食堂",
        balance: 100,
        cardType: "一类",
        userType: "REGULAR",
        cardExpireTime: "2025-12-31 23:59:59",
        crossRegionFlag: true,
        diningRegion: "A区",
        status: "ACTIVE",
      };
      resolve(user);
    });
  }
  return service.get<any, IUser>(`${MODULE_API_PREFIX}/current`);
}
