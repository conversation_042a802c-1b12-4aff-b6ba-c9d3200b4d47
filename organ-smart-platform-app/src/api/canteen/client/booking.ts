import service from "@/service";
import { BIZ_MODULE_API_PREFIX } from "@/api/canteen/constant";
import {
  IBatchBookingRequest,
  IBatchBookingResult,
  IUserBooking,
} from "./types/booking";

/** 模块API前缀 */
const MODULE_API_PREFIX = `${BIZ_MODULE_API_PREFIX}/booking`;

/** 是否模拟数据 */
const isMock = true;

/** 分页获取用户预约记录 */
export function getUserBookingPage(params: PaginatedRequestParams) {
  if (isMock) {
    return new Promise<any>((resolve) => {
      resolve({
        total: 10,
        list: [
          {
            id: "1",
            canteenName: "学苑食堂",
            mealName: "午餐",
            bookingDate: "2024-08-28",
            bookingTime: "11:00-13:00",
            status: "CONFIRMED",
            createTime: "2024-08-27 10:30:00",
          },
          {
            id: "2",
            canteenName: "学苑食堂",
            mealName: "晚餐",
            bookingDate: "2024-08-28",
            bookingTime: "17:00-19:00",
            status: "PENDING",
            createTime: "2024-08-27 10:35:00",
          },
        ],
      });
    });
  }
  return service.get<typeof params, any>(
    `${MODULE_API_PREFIX}/userPages`,
    params
  );
}

/**
 * 批量预约
 */
export function batchBooking(params: IBatchBookingRequest) {
  if (isMock) {
    return new Promise<IBatchBookingResult>((resolve) => {
      resolve({
        success: true,
        message: "预约成功",
        successMeals: params.mealPeriods.map((item) => ({
          mealPeriodId: item.mealPeriodIds[0],
          mealName: "午餐",
          mealTime: "11:00-13:00",
          bookingDate: item.bookingDate,
        })),
        // 当天的第一个预约失败
        failedMeals: params.mealPeriods
          .filter((item, index) => index === 0)
          .map((item) => ({
            mealPeriodId: item.mealPeriodIds[0],
            mealTime: "11:00-13:00",
            mealName: "午餐",
            reason: "席位已满",
            bookingDate: item.bookingDate,
          })),
        totalCount: params.mealPeriods.length,
        successCount: params.mealPeriods.length,
        failedCount: 1,
      });
    });
  }
  return service.post<IBatchBookingRequest, IBatchBookingResult>(
    `${MODULE_API_PREFIX}/batch`,
    params
  );
}

/** 获取当前最近待使用的预约信息 */
export function getStartingSoonBooking() {
  if (isMock) {
    return new Promise<IUserBooking>((resolve) => {
      setTimeout(() => {
        resolve({
          id: "1",
          canteenName: "学苑食堂",
          mealName: "午餐",
          bookingDate: "2024-08-28",
          mealTime: "11:00-13:00",
          bookingCode: "66666",
          status: "CONFIRMED",
          statusLabel: "待使用",
        });
      }, 1000);
    });
  }
  return service.get<any, IUserBooking>(`${MODULE_API_PREFIX}/startingSoon`);
}

/** 获取的的预约数 */
export function getMyBookingCount() {
  if (isMock) {
    return new Promise<number>((resolve) => {
      resolve(10);
    });
  }
  return service.get<any, number>(`${MODULE_API_PREFIX}/myBookingCount`);
}
