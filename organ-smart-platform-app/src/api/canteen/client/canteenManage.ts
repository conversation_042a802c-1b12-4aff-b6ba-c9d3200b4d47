import service from "@/service";
import { ICanteenInfo } from "./types/canteenManage";
import { BIZ_MODULE_API_PREFIX } from "@/api/canteen/constant";

/** 模块API前缀 */
const MODULE_API_PREFIX = `${BIZ_MODULE_API_PREFIX}/canteenInfo`;

/** 是否模拟数据 */
const isMock = true;

/** 获取所有食堂列表 */
export function getCanteenList(params: Record<string, any> = {}) {
  if (isMock) {
    // 生成5个食堂的基本信息
    return new Promise<ICanteenInfo[]>((resolve) => {
      const list = Array.from({ length: 5 }, (_, index) => ({
        id: `${index + 1}`,
        canteenName: `食堂${index + 1}`,
        canteenAddress: `地址${index + 1}`,
      })) as ICanteenInfo[];
      resolve(list);
    });
  }
  return service.get<any, ICanteenInfo[]>(`${MODULE_API_PREFIX}/list`, params);
}

/** 根据ID获取食堂详细信息 */
export function getCanteenById(canteenId: string) {
  if (isMock) {
    return new Promise<ICanteenInfo>((resolve) => {
      const canteen: ICanteenInfo = {
        id: canteenId,
        canteenCode: `CODE_${canteenId}`,
        canteenName: `学苑食堂`,
        canteenAddress: `数字校区A区1楼`,
        canteenRegion: "A区",
        status: "ACTIVE",
        businessHours: "06:30-21:00",
        canteenManager: "张经理",
        canteenContact: "***********",
        canteenIntroduction:
          "学苑食堂是校园内最受欢迎的食堂之一，提供丰富多样的美食选择。",
      };
      resolve(canteen);
    });
  }
  return service.get<any, ICanteenInfo>(`${MODULE_API_PREFIX}/${canteenId}`);
}
