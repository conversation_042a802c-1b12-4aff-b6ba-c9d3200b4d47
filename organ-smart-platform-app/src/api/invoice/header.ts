import service from '@/service'
import { createListRequest } from '@/service/util'

/** 创建发票抬头 */
export function createInvoiceHeader(params: InvoiceHeaderCreateReqDTO) {
  return service.post('/invoiceHeader/create', params)
}

/** 查询发票抬头列表 */
export const getInvoiceHeaderPage = createListRequest<InvoiceHeaderPageReqDTO, InvoiceHeaderVO>('/invoiceHeader/page', 'post')

/** 修改发票抬头 */
export function updateInvoiceHeader(params: InvoiceHeaderUpdateReqDTO) {
  return service.post(`/invoiceHeader/update`, params)
}

/** 删除发票抬头 */
export function deleteInvoiceHeader(id: number) {
  return service.delete(`/invoiceHeader/delete/${id}`)
}

/**
 * 发票抬头视图对象
 *
 * InvoiceHeaderVO
 */
export interface InvoiceHeaderVO {
  /**
   * 银行账号
   */
  bankAccount?: string
  /**
   * 开户银行
   */
  bankName?: string
  /**
   * 联系人邮箱
   */
  contactEmail?: string
  /**
   * 联系人姓名
   */
  contactName?: string
  /**
   * 联系人电话
   */
  contactPhone?: string
  /**
   * 创建时间
   */
  createTime?: string
  /**
   * 创建人
   */
  creator?: string
  /**
   * 发票抬头名称
   */
  headerName?: string
  /**
   * 抬头类型(personal/company)
   */
  headerType?: HeaderType
  /**
   * 发票抬头ID
   */
  id?: number
  /**
   * 发票种类(electronic/paper)
   */
  invoiceCategory?: InvoiceCategory
  /**
   * 发票类型(ordinary/special)
   */
  invoiceType?: InvoiceType
  /**
   * 是否默认抬头
   */
  isDefault?: boolean
  /**
   * 注册地址
   */
  registeredAddress?: string
  /**
   * 注册电话
   */
  registeredPhone?: string
  /**
   * 备注信息
   */
  remark?: string
  /**
   * 状态(active/inactive)
   */
  status?: Status
  /**
   * 纳税人识别号
   */
  taxNumber?: string
  /**
   * 租户ID
   */
  tenantId?: number
  /**
   * 用户ID
   */
  userId?: number
}

/**
 * InvoiceHeaderPageReqDTO
 */
export interface InvoiceHeaderPageReqDTO {
  /**
   * 发票抬头名称
   */
  headerName?: string
  /**
   * 抬头类型(personal/company)
   */
  headerType?: string
  /**
   * 是否默认抬头
   */
  isDefault?: boolean
  /**
   * 页码，从 1 开始
   */
  pageNo: number
  /**
   * 每页条数，最大值为 100
   */
  pageSize: number
  /**
   * 用户ID
   */
  userId?: number
}

/**
 * InvoiceHeaderCreateReqDTO
 */
export interface InvoiceHeaderCreateReqDTO {
  /**
   * 银行账号
   */
  bankAccount?: string
  /**
   * 开户银行
   */
  bankName?: string
  /**
   * 联系人邮箱
   */
  contactEmail?: string
  /**
   * 联系人姓名
   */
  contactName?: string
  /**
   * 联系人电话
   */
  contactPhone?: string
  /**
   * 发票抬头名称
   */
  headerName: string
  /**
   * 抬头类型(personal/company)
   */
  headerType: HeaderType
  /**
   * 发票种类(electronic/paper)
   */
  invoiceCategory: InvoiceCategory
  /**
   * 发票类型(ordinary/special)
   */
  invoiceType: InvoiceType
  /**
   * 是否默认抬头
   */
  isDefault?: boolean
  /**
   * 注册地址
   */
  registeredAddress?: string
  /**
   * 注册电话
   */
  registeredPhone?: string
  /**
   * 备注信息
   */
  remark?: string
  /**
   * 状态(active/inactive)
   */
  status?: Status
  /**
   * 纳税人识别号
   */
  taxNumber?: string
  /**
   * 用户ID
   */
  userId?: number
}

/** 修改发票抬头入参 */
export interface InvoiceHeaderUpdateReqDTO extends Omit<InvoiceHeaderCreateReqDTO, 'userId'> {
  /** 发票id */
  id: number
}

/**
 * 抬头类型(personal/company)
 */
export enum HeaderType {
  Company = 'COMPANY',
  Personal = 'PERSONAL',
}

/**
 * 发票种类(electronic/paper)
 */
export enum InvoiceCategory {
  Electronic = 'ELECTRONIC',
  Paper = 'PAPER',
}

/**
 * 发票类型(ordinary/special)
 */
export enum InvoiceType {
  Ordinary = 'ORDINARY',
  Special = 'SPECIAL',
}

/**
 * 状态(active/inactive)
 */
export enum Status {
  Active = 'ACTIVE',
  Inactive = 'INACTIVE',
}
