import service from '@/service'

/** 创建发票 */
export function createInvoice(params: InvoiceCreateReqDTO) {
  return service.post<InvoiceCreateReqDTO, InvoiceVO>('/invoice/create', params)
}

/**
 * InvoiceCreateReqDTO
 */
export interface InvoiceCreateReqDTO {
  /**
   * 发票金额
   */
  invoiceAmount: number
  /**
   * 发票抬头ID
   */
  invoiceHeaderId: number
  /**
   * 订单ID
   */
  orderId: number
  /**
   * 备注
   */
  remark?: string
  /**
   * 用户ID
   */
  userId?: number
}

/**
 * InvoiceVO
 */
export interface InvoiceVO {
  /**
   * 申请时间
   */
  applyTime?: string
  /**
   * 创建时间
   */
  createTime?: string
  /**
   * 创建人
   */
  creator?: string
  /**
   * 关联查询字段
   * 发票抬头名称
   */
  headerName?: string
  /**
   * 抬头类型(personal/company)
   */
  headerType?: 'personal' | 'company'
  /**
   * 发票ID
   */
  id?: number
  /**
   * 发票金额
   */
  invoiceAmount?: number
  /**
   * 发票内容
   */
  invoiceContent?: string
  /**
   * 发票文件URL
   */
  invoiceFileUrl?: string
  /**
   * 发票抬头ID
   */
  invoiceHeaderId?: number
  /**
   * 发票申请编号
   */
  invoiceNo?: string
  /**
   * 发票状态(pending/processing/issued/rejected)
   */
  invoiceStatus?: 'pending' | 'processing' | 'issued' | 'rejected'
  /**
   * 发票状态描述
   */
  invoiceStatusDesc?: string
  /**
   * 开票时间
   */
  issueTime?: string
  /**
   * 订单ID
   */
  orderId?: number
  /**
   * 收件人地址
   */
  recipientAddress?: string
  /**
   * 收件人姓名
   */
  recipientName?: string
  /**
   * 收件人电话
   */
  recipientPhone?: string
  /**
   * 备注
   */
  remark?: string
  /**
   * 纳税人识别号
   */
  taxNumber?: string
  /**
   * 租户ID
   */
  tenantId?: number
  /**
   * 用户ID
   */
  userId?: number
}
