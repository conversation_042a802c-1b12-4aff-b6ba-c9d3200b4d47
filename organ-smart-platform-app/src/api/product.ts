import service from '@/service'

/**
 * 套餐类型(basic/premium/vip)
 */
export enum PackageType {
  Basic = 'BASIC',
  Premium = 'PREMIUM',
  Vip = 'VIP',
}

/**
 * 状态(active/inactive/discontinued)
 */
export enum Status {
  Active = 'ACTIVE',
  Disabled = 'DISABLED',
  Inactive = 'INACTIVE',
}

/**
 * 会员套餐视图对象
 *
 * ProductPackageVO
 */
export interface ProductPackageVO {
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 套餐描述
   */
  description: string
  /**
   * 失效时间
   */
  endTime: string
  /**
   * 套餐ID
   */
  id: number
  /**
   * 原价
   */
  originalPrice: number
  /**
   * 套餐编码
   */
  packageCode: string
  /**
   * 套餐名称
   */
  packageName: string
  /**
   * 套餐类型(basic/premium/vip)
   */
  packageType: PackageType
  /**
   * 现价
   */
  price: number
  /**
   * 排序顺序
   */
  sortOrder: number
  /**
   * 生效时间
   */
  startTime: string
  /**
   * 状态(active/inactive/discontinued)
   */
  status: Status
  /**
   * 租户ID
   */
  tenantId: number
  /**
   * 有效天数
   */
  validityDays: number
  /**
   * 有效期描述
   */
  validityDesc: string
  [property: string]: any
}

/**
 * 获取 VIP 套餐列表
 */
export function getVipProductList() {
  return service.get<{}, ProductPackageVO[]>('/productPackage/list')
}
