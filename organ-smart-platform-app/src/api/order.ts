import service from '@/service'

/**
 * PurchaseMembershipVO
 */
export interface PurchaseMembershipVO {
  /**
   * 实付金额
   */
  actualAmount?: number
  /**
   * 优惠金额
   */
  discountAmount?: number
  /**
   * 订单过期时间
   */
  expireTime?: string
  /**
   * 订单ID
   */
  orderId?: number
  /**
   * 订单号
   */
  orderNo?: string
  /**
   * 订单状态
   */
  orderStatus?: OrderStatus
  /**
   * 订单状态描述
   */
  orderStatusDesc?: string
  /**
   * 订单类型
   */
  orderType?: OrderType
  /**
   * 订单类型描述
   */
  orderTypeDesc?: string
  /**
   * 原价金额
   */
  originalAmount?: number
  /**
   * 套餐信息
   */
  packageInfo?: ProductPackageVO
  /**
   * 支付链接/二维码
   */
  paymentUrl?: string
  /**
   * 支付订单ID
   */
  payOrderId?: string
  /**
   * 会员有效期结束时间
   */
  validityEndTime?: string
  /**
   * 会员有效期开始时间
   */
  validityStartTime?: string
  /**
   * 支付方式代码
   */
  wayCode?: WayCode
  [property: string]: any
}

/**
 * 订单状态
 */
export enum OrderStatus {
  Cancelled = 'CANCELLED',
  Paid = 'PAID',
  Pending = 'PENDING',
  Refunded = 'REFUNDED',
}

/**
 * 订单类型
 */
export enum OrderType {
  Purchase = 'PURCHASE',
  Renew = 'RENEW',
  Upgrade = 'UPGRADE',
}

/**
 * 套餐信息
 *
 * ProductPackageVO
 */
export interface ProductPackageVO {
  /**
   * 创建时间
   */
  createTime?: string
  /**
   * 套餐描述
   */
  description?: string
  /**
   * 失效时间
   */
  endTime?: string
  /**
   * 套餐ID
   */
  id?: number
  /**
   * 原价
   */
  originalPrice?: number
  /**
   * 套餐编码
   */
  packageCode?: string
  /**
   * 套餐名称
   */
  packageName?: string
  /**
   * 套餐类型(basic/premium/vip)
   */
  packageType?: PackageType
  /**
   * 现价
   */
  price?: number
  /**
   * 排序顺序
   */
  sortOrder?: number
  /**
   * 生效时间
   */
  startTime?: string
  /**
   * 状态(active/inactive/discontinued)
   */
  status?: Status
  /**
   * 租户ID
   */
  tenantId?: number
  /**
   * 有效天数
   */
  validityDays?: number
  /**
   * 有效期描述
   */
  validityDesc?: string
  [property: string]: any
}

/**
 * 套餐类型(basic/premium/vip)
 */
export enum PackageType {
  Basic = 'BASIC',
  Premium = 'PREMIUM',
  Vip = 'VIP',
}

/**
 * 状态(active/inactive/discontinued)
 */
export enum Status {
  Active = 'ACTIVE',
  Disabled = 'DISABLED',
  Inactive = 'INACTIVE',
}

/**
 * 支付方式代码
 */
export enum WayCode {
  AliApp = 'ALI_APP',
  AliBar = 'ALI_BAR',
  AliJsapi = 'ALI_JSAPI',
  AliPC = 'ALI_PC',
  AliQr = 'ALI_QR',
  AliWAP = 'ALI_WAP',
  AutoBar = 'AUTO_BAR',
  QrCashier = 'QR_CASHIER',
  WxApp = 'WX_APP',
  WxBar = 'WX_BAR',
  WxH5 = 'WX_H5',
  WxJsapi = 'WX_JSAPI',
  WxLite = 'WX_LITE',
  WxNative = 'WX_NATIVE',
  YsfBar = 'YSF_BAR',
  YsfJsapi = 'YSF_JSAPI',
}
/**
 * 创建购买会员订单
 */
export function createPurchaseOrder(params: {
  packageId: string
  userId?: string
}) {
  return service.post<{}, PurchaseMembershipVO>('/membershipOrder/purchase', params)
}

/**
 * MembershipOrderVO
 */
export interface MembershipOrderVO {
  /**
   * 实付金额
   */
  actualAmount?: number
  /**
   * 创建时间
   */
  createTime?: string
  /**
   * 优惠金额
   */
  discountAmount?: number
  /**
   * 订单过期时间
   */
  expireTime?: string
  /**
   * 订单ID
   */
  id?: number
  /**
   * 会员套餐名称
   */
  membershipName?: string
  /**
   * 会员类型标准编码
   */
  membershipType?: MembershipType
  /**
   * 会员类型描述
   */
  membershipTypeDesc?: string
  /**
   * 订单号
   */
  orderNo?: string
  /**
   * 订单状态
   */
  orderStatus?: OrderStatus
  /**
   * 订单状态描述
   */
  orderStatusDesc?: string
  /**
   * 订单类型
   */
  orderType?: OrderType
  /**
   * 订单类型描述
   */
  orderTypeDesc?: string
  /**
   * 原价金额
   */
  originalAmount?: number
  /**
   * 支付时间
   */
  paymentTime?: string
  /**
   * 支付订单ID
   */
  payOrderId?: string
  /**
   * 产品套餐ID
   */
  productPackageId?: number
  /**
   * 备注
   */
  remark?: string
  /**
   * 租户ID
   */
  tenantId?: number
  /**
   * 用户ID
   */
  userId?: number
  /**
   * 用户姓名
   */
  userName?: string
  /**
   * 用户手机号
   */
  userPhone?: string
  /**
   * 会员有效期结束时间
   */
  validityEndTime?: string
  /**
   * 会员有效期开始时间
   */
  validityStartTime?: string
  /**
   * 支付方式代码
   */
  wayCode?: WayCode
  /**
   * 支付方式描述
   */
  wayCodeDesc?: string
}

/**
 * 会员类型标准编码
 */
export enum MembershipType {
  Basic = 'BASIC',
  Premium = 'PREMIUM',
  Vip = 'VIP',
}

/**
 * 查询订单支付状态
 * @param orderNo 订单号
 */
export function getOrderStatus(orderNo: string) {
  return service.get<{}, MembershipOrderVO>(`/membershipOrder/status/${orderNo}`)
}
