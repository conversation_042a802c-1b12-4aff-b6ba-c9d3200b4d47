import service from '@/service'
import { createListRequest } from '@/service/util'

/**
 * 查询支付统计
 */
export function getPaymentOrderStats() {
  return service.get<{}, {
    /**
     * 查询时间区间
     */
    timeRange: string
    /**
     * 支付总金额
     */
    totalAmount: number
    /**
     * 支付会员量
     */
    totalMembers: number
    /**
     * 支付订单量
     */
    totalOrders: number
  }>('/membershipOrder/paymentStats')
}

/**
 * 支付记录视图对象
 *
 * PaymentRecordVO
 */
export interface PaymentRecordVO {
  /**
   * 支付金额
   */
  actualAmount?: number
  /**
   * 会员类型
   */
  membershipType?: MembershipType
  /**
   * 订单编号
   */
  orderNo?: string
  /**
   * 支付日期
   */
  paymentTime?: string
  /**
   * 用户姓名
   */
  userName?: string
  /**
   * 用户手机号
   */
  userPhone?: string
  /**
   * 会员有效期
   */
  validityEndTime?: string
}

/**
 * 会员类型
 */
export enum MembershipType {
  Basic = 'BASIC',
  Premium = 'PREMIUM',
  Vip = 'VIP',
}

export interface QueryOrderParameters {
  /**
   * 会员类型
   */
  membershipType?: MembershipType
  /**
   * 订单编号
   */
  orderNo?: string
  /**
   * 支付结束时间
   */
  paymentEndTime?: string
  /**
   * 支付开始时间
   */
  paymentStartTime?: string
  /**
   * 用户姓名
   */
  userName?: string
  /**
   * 用户手机号
   */
  userPhone?: string
}

/**
 * 查询国聘支付记录
 */
export const getPaymentRecordsPage = createListRequest<QueryOrderParameters, PaymentRecordVO>('/membershipOrder/paymentRecords', 'post')

/**
 * 导出国聘支付记录Excel
 */
export function exportOrderExcel(params: QueryOrderParameters) {
  return service.post<QueryOrderParameters, {}>('/membershipOrder/paymentRecords/export', params)
}
