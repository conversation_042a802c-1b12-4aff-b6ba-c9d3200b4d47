import service from '@/service'
import { createListRequest } from '@/service/util'

/**
 * 分页查询报告查看记录
 */
export const getReportViewRecordPage = createListRequest<{
  /**
   * 企业名称
   */
  enterpriseName?: string
}, {
  id: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 企业名称
   */
  enterpriseName: string
  /**
   * 查看结果
   */
  viewResult: 'success' | 'failed'
}>('/reportViewRecord/page', 'post')

/**
 * 分页查询意见反馈
 */
export const getFeedbackPage = createListRequest<{

}, {
  /**
   * 反馈类型描述
   */
  feedbackTypeDesc: string
  /**
   * 反馈时间
   */
  createTime: string
  /**
   * 联系电话
   */
  contactPhone: string
}>('/feedback/page', 'post')

/**
 * 分页查询发票
 */
export const getInvoicePage = createListRequest<{

}, {
  /**
   * 发票申请编号
   */
  invoiceNo: string
  /**
   * 发票金额
   */
  invoiceAmount: string
  /**
   * 申请时间
   */
  createTime: string
  /**
   * 开票状态
   */
  invoiceStatus: 'pending' | 'processing' | 'issued' | 'rejected'
}>('/invoice/page', 'post')

/**
 * 分页查询会员订单
 */
export const getMembershipOrderPage = createListRequest<{

}, {
  /**
   * 订单编号
   */
  orderNo: string
  /**
   * 订单名称
   */
  membershipName: string
  /**
   * 实际付款金额
   */
  actualAmount: string
  /**
   * 支付时间
   */
  paymentTime: string
  /**
   * 开票相关字段是否可以开票
   */
  canInvoice: boolean
}>('/membershipOrder/page', 'post')

/**
 * 申请发票接口参数
 */
export interface InvoiceApplicationParams {
  /**
   * 订单编号
   */
  orderNo: string
  /**
   * 总金额
   */
  totalAmount: number
  /**
   * 接收方式 - 邮箱
   */
  receiveMethod: 'email'
  /**
   * 邮箱地址
   */
  email: string
}

/**
 * 申请发票
 */
export function applyInvoice(params: InvoiceApplicationParams) {
  return service.post<InvoiceApplicationParams, {
    /**
     * 申请结果
     */
    success: boolean
    /**
     * 发票申请编号
     */
    invoiceNo?: string
    /**
     * 消息
     */
    message?: string
  }>('/invoice/apply', params)
}
