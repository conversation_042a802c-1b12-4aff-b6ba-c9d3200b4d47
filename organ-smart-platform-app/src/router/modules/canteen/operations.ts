import { createRouterFlatModule, createRouterModule } from "@/router/util";

/** 运营端路由配置 */
const moduleName = "operations";

export default createRouterModule({
  name: moduleName,
  options: {
    meta: {
      menu: false,
    },
  },
  children: [
    {
      path: "home",
      meta: {
        title: "就餐码使用",
      },
      component: () => import("@/views/canteen/operations/home/<USER>"),
    },
    {
      path: "scanQrCode",
      meta: {
        title: "就餐码",
      },
      component: () =>
        import("@/views/canteen/operations/scanQrCode/index.vue"),
    },
  ],
});
