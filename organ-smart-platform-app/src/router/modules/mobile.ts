import { createRouterFlatModule, createRouterModule } from '../util'

const moduleName = 'mobile'

export default createRouterModule({
  name: moduleName,
  options: {
    meta: {
      menu: false,
    },
  },
  children: [
    {
      path: 'reports',
      meta: {
        title: '海易信企业征信报告',
      },
      component: () => import('@/mobile/reports/index.vue'),
    },
    ...createRouterFlatModule({
      name: 'control',
      children: [
        {
          path: 'index',
          component: () => import('@/mobile/control/index.vue'),
          meta: {
            title: '海易信企业征信报告',
          },
        },
        {
          path: 'review',
          component: () => import('@/mobile/control/review.vue'),
          meta: {
            title: '报告下载管理',
          },
        },
        {
          path: 'orders',
          component: () => import('@/mobile/control/orders.vue'),
          meta: {
            title: '我的订单',
          },
        },
        {
          path: 'invoices',
          component: () => import('@/mobile/control/invoices.vue'),
          meta: {
            title: '我的发票',
          },
        },
        {
          path: 'invoicesHeader',
          component: () => import('@/mobile/control/invoicesHeader.vue'),
          meta: {
            title: '发票抬头管理',
          },
        },
        {
          path: 'feedback',
          component: () => import('@/mobile/control/feedback.vue'),
          meta: {
            title: '意见反馈',
          },
        },
        {
          path: 'account',
          component: () => import('@/mobile/control/account.vue'),
          meta: {
            title: '账号管理',
          },
        },
      ],
    }),
  ],
})
