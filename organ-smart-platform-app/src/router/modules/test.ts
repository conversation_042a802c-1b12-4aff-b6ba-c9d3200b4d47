/**
 * 测试路由模块
 */

import { createRouterModule } from "../util";

const moduleName = "test";

export default createRouterModule({
  name: moduleName,
  options: {
    meta: {
      menu: false,
    },
  },
  children: [
    {
      path: "demo",
      meta: {
        title: "组件演示",
      },
      component: () => import("@/components/demo.vue"),
    },
    {
      path: "simple",
      meta: {
        title: "简单测试",
      },
      component: () => import("@/components/test-simple.vue"),
    },
    {
      path: "components",
      meta: {
        title: "完整测试",
      },
      component: () => import("@/views/test-components.vue"),
    },
  ],
});
