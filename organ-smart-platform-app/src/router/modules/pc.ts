import { createRouterFlatModule, createRouterModule } from '../util'

const moduleName = 'pc'

export default createRouterModule({
  name: moduleName,
  options: {
    meta: {
      menu: false,
    },
  },
  children: [
    {
      path: 'reports',
      component: () => import('@/pc/reports/index.vue'),
    },
    {
      path: 'control',
      component: () => import('@/pc/control/index.vue'),
      meta: {
        title: '个人中心',
      },
      children: createRouterFlatModule({
        name: 'pc/control',
        children: [
          {
            path: 'review',
            component: () => import('@/pc/control/review.vue'),
            meta: {
              title: '报告订单查看',
            },
          },
          {
            path: 'orders',
            component: () => import('@/pc/control/orders.vue'),
            meta: {
              title: '我的订单',
            },
          },
          {
            path: 'invoices',
            component: () => import('@/pc/control/invoices.vue'),
            meta: {
              title: '我的发票',
            },
          },
          {
            path: 'invoicesHeader',
            component: () => import('@/pc/control/invoicesHeader/index.vue'),
            meta: {
              title: '发票抬头',
            },
          },
          {
            path: 'feedback',
            component: () => import('@/pc/control/feedback.vue'),
            meta: {
              title: '意见反馈',
            },
          },
          {
            path: 'account',
            component: () => import('@/pc/control/account.vue'),
            meta: {
              title: '账号管理',
            },
          },
        ],
      }),
    },

  ],
})
