import type { RouteConfig } from "vue-router";
import Vue from "vue";
import VueRouter from "vue-router";
import { getAppName } from "@/utils/env";

Vue.use(VueRouter);

/**
 * 为路由添加前缀
 * @param route 路由配置
 * @param prefix 路径前缀
 * @returns 处理后的路由配置
 */
function addRoutePrefix(route: RouteConfig, prefix: string): RouteConfig {
  const newRoute = { ...route };

  // 为主路由路径添加前缀（仅当路径以/开头且不包含任何前缀时）
  if (newRoute.path && newRoute.path.startsWith("/")) {
    // 检查路径是否只是以/开头的简单路径（不包含子路径）
    const pathSegments = newRoute.path.split("/").filter(Boolean);
    if (pathSegments.length === 0 || pathSegments[0] !== prefix) {
      newRoute.path = `/${prefix}${newRoute.path}`;
    }
  }

  // 递归处理子路由
  if (newRoute.children && Array.isArray(newRoute.children)) {
    newRoute.children = newRoute.children.map((child) => {
      const childCopy = { ...child };
      if (childCopy.path && childCopy.path.startsWith("/")) {
        const childPathSegments = childCopy.path.split("/").filter(Boolean);
        if (childPathSegments.length === 0 || childPathSegments[0] !== prefix) {
          childCopy.path = `/${prefix}${childCopy.path}`;
        }
      }
      return childCopy;
    });
  }

  return newRoute;
}

/**
 * 递归导入所有路由模块（包括子文件夹）
 * @returns 路由配置数组
 */
function importAllRoutes(): RouteConfig[] {
  const routes: RouteConfig[] = [];
  const processedFiles = new Set<string>(); // 避免重复处理文件

  // 使用递归的 require.context 获取所有文件
  const allRouteFiles = require.context("./modules", true, /\.ts|\.js$/);

  allRouteFiles.keys().forEach((fileName) => {
    // 避免重复处理
    if (processedFiles.has(fileName)) return;
    processedFiles.add(fileName);

    const module: { default: RouteConfig } = allRouteFiles(fileName);
    let route = { ...module.default };

    // 解析文件路径，确定是否在子文件夹中
    const pathParts = fileName.replace(/^\.\//, "").split("/");

    if (pathParts.length > 1) {
      // 子文件夹中的文件
      const folderName = pathParts[0]; // 获取第一级子文件夹名称

      // 为子文件夹中的路由添加前缀（仅当路由路径不是以该文件夹名开头时）
      if (route.path && route.path.startsWith("/")) {
        const routeSegments = route.path.split("/").filter(Boolean);
        if (routeSegments.length === 0 || routeSegments[0] !== folderName) {
          route = addRoutePrefix(route, folderName);
        }
      }
    }

    routes.push(route);
  });

  return routes;
}

// 批量导入./modules 目录下的所有 路由模块
// const modules = require.context("./modules", false, /\.ts$/);
/**
 * 路由注册表，考虑到
 * createRouterModule 返回对象
 * createRouterFlatModule 返回数组
 * 所有需要使用 flat 进行扁平化
 */
// const routes = modules
//   .keys()
//   .map<RouteConfig[]>((path) => {
//     return modules(path).default;
//   })
//   .flat(1);

const router = new VueRouter({
  mode: "history",
  routes: importAllRoutes(),
});

// 路由守卫 - 设置页面标题
router.beforeEach((to, _from, next) => {
  document.title = to.meta?.title || getAppName();
  next();
});

export default router;
