import type { RouteConfig } from 'vue-router'
import layout from '@/layout/index.vue'
import { defaultMeta } from './config'

/** 创建模块路由，返回的将是路由对象 */
export function createRouterModule(config: {
  name: string
  children: RouteConfig['children']
  options?: Omit<RouteConfig, 'path' | 'children'>
}): RouteConfig {
  return {
    ...config.options,
    path: `/${config.name}`,
    meta: Object.assign({ ...defaultMeta }, config?.options?.meta),
    component: layout,
    children: createRouterFlatModule(config),
  }
}

/** 创建扁平的模块路由，返回的将是路由数组 */
export function createRouterFlatModule(config: {
  name: string
  children: RouteConfig['children']
}): RouteConfig[] {
  return config.children?.map(child => ({
    ...child,
    path: `/${config.name}/${child.path}`.replace(/\/+/g, '/'),
    props: route => ({
      ...route.params,
      ...route.query,
    }),
    meta: Object.assign({ ...defaultMeta }, child.meta),
  }))
}
