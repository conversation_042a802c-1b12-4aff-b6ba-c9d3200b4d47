import dayjs from 'dayjs'

/**
 * 格式化时间到秒
 * @param dateTime 时间值，支持字符串、Date对象、null、undefined
 * @returns 格式化后的时间字符串 YYYY-MM-DD HH:mm:ss，无效值返回 '-'
 */
export function formatDateTime(dateTime: string | Date | number | null | undefined): string {
  return dateTime ? dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss') : '-'
}

/**
 * 格式化日期
 * @param date 日期值
 * @returns 格式化后的日期字符串 YYYY-MM-DD，无效值返回 '-'
 */
export function formatDate(date: string | Date | number | null | undefined): string {
  return date ? dayjs(date).format('YYYY-MM-DD') : '-'
}

/**
 * 格式化时间
 * @param time 时间值
 * @returns 格式化后的时间字符串 HH:mm:ss，无效值返回 '-'
 */
export function formatTime(time: string | Date | number | null | undefined): string {
  return time ? dayjs(time).format('HH:mm:ss') : '-'
}

/**
 * 格式化相对时间
 * @param dateTime 时间值
 * @returns 相对时间描述，如 "2小时前"、"3天前"
 */
export function formatRelativeTime(dateTime: string | Date | number | null | undefined): string {
  if (!dateTime)
    return '-'

  const now = dayjs()
  const target = dayjs(dateTime)
  const diffInMinutes = now.diff(target, 'minute')
  const diffInHours = now.diff(target, 'hour')
  const diffInDays = now.diff(target, 'day')

  if (diffInMinutes < 1) {
    return '刚刚'
  }
  else if (diffInMinutes < 60) {
    return `${diffInMinutes}分钟前`
  }
  else if (diffInHours < 24) {
    return `${diffInHours}小时前`
  }
  else if (diffInDays < 30) {
    return `${diffInDays}天前`
  }
  else {
    return formatDate(dateTime)
  }
}

/**
 * 自定义格式化时间
 * @param dateTime 时间值
 * @param format 格式字符串，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的时间字符串
 */
export function formatCustomDateTime(
  dateTime: string | Date | number | null | undefined,
  format: string = 'YYYY-MM-DD HH:mm:ss',
): string {
  return dateTime ? dayjs(dateTime).format(format) : '-'
}
