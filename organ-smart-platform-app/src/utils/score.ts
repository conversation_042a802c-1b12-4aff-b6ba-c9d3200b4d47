/**
 * 分数相关工具函数
 */

/**
 * 计算分数节点
 * @param maxScore 最高分
 * @param nodeCount 节点数量
 * @returns 分数节点数组
 */
export function calculateScoreNodes(maxScore: number, nodeCount: number = 3): number[] {
  if (nodeCount < 2) {
    throw new Error('节点数量至少为2')
  }

  const nodes: number[] = []
  const step = maxScore / (nodeCount - 1)

  for (let i = 0; i < nodeCount; i++) {
    nodes.push(Math.round(i * step))
  }

  return nodes
}

/**
 * 计算分数百分比位置
 * @param score 当前分数
 * @param maxScore 最高分
 * @returns 百分比位置 (0-100)
 */
export function calculateScorePercentage(score: number, maxScore: number): number {
  if (maxScore <= 0)
    return 0
  return Math.min(Math.max((score / maxScore) * 100, 0), 100)
}

/**
 * 计算进度条遮罩宽度（剩余部分）
 * @param score 当前分数
 * @param maxScore 最高分
 * @returns 遮罩宽度百分比字符串
 */
export function calculateMaskWidth(score: number, maxScore: number): string {
  const progressPercentage = calculateScorePercentage(score, maxScore)
  return `${100 - progressPercentage}%`
}

/**
 * 根据分数获取等级
 * @param score 当前分数
 * @param maxScore 最高分
 * @returns 等级信息
 */
export function getScoreLevel(score: number, maxScore: number) {
  const percentage = calculateScorePercentage(score, maxScore)

  if (percentage >= 80) {
    return { level: 'excellent', label: '优秀', color: '#52c41a' }
  }
  else if (percentage >= 60) {
    return { level: 'good', label: '良好', color: '#1890ff' }
  }
  else if (percentage >= 40) {
    return { level: 'fair', label: '一般', color: '#faad14' }
  }
  else {
    return { level: 'poor', label: '较差', color: '#f5222d' }
  }
}

/**
 * 分数配置类型
 */
export interface ScoreConfig {
  /** 最高分 */
  maxScore: number
  /** 节点数量 */
  nodeCount: number
  /** 是否显示小数 */
  showDecimal: boolean
}

/**
 * 默认分数配置
 */
export const DEFAULT_SCORE_CONFIG: ScoreConfig = {
  maxScore: 100,
  nodeCount: 3,
  showDecimal: false,
}

/**
 * 创建分数配置
 * @param config 自定义配置
 * @returns 完整的分数配置
 */
export function createScoreConfig(config: Partial<ScoreConfig> = {}): ScoreConfig {
  return {
    ...DEFAULT_SCORE_CONFIG,
    ...config,
  }
}
