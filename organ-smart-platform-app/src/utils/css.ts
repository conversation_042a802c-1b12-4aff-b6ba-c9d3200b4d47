/**
 * 获取CSS单位值
 * @param value 数值或字符串
 * @param unit 单位，默认为'px'
 * @returns 带单位的CSS值
 * @example
 * getCssUnit(100) // '100px'
 * getCssUnit('100') // '100px'
 * getCssUnit(100, 'rem') // '100rem'
 * getCssUnit('auto') // 'auto'
 */
export function getCssUnit(value?: string | number, unit = 'px') {
  const num = Number(value)
  if (Number.isNaN(num)) {
    return value
  }
  else {
    return `${num}${unit}`
  }
}
