/**
 * 对数组中指定键的数值进行求和，或使用自定义reduce函数进行求和
 * @template T - 数组元素类型，当key为字符串时，T必须包含类型为number的键K
 * @template K - 键的类型，必须是字符串
 * @param {T[]} arr - 要进行求和的数组
 * @param {K | ((prev: number, curr: T) => number)} key - 要求和的键名，或自定义的reduce函数
 * @returns {number} 求和结果
 * @example
 * // 使用键名求和
 * const items = [{value: 1}, {value: 2}, {value: 3}];
 * ArraySum(items, 'value'); // 返回 6
 *
 * // 使用自定义函数求和
 * const numbers = [{num: 1}, {num: 2}, {num: 3}];
 * ArraySum(numbers, (prev, curr) => prev + curr.num); // 返回 6
 */
export function ArraySum<T extends Record<string, any>, K extends string>(arr: T[], key: K | ((prev: number, curr: T) => number)): number {
  if (typeof key === 'string') {
    return arr.reduce((pre, cur) => pre + cur[key], 0)
  }
  else {
    return arr.reduce(key, 0)
  }
}
