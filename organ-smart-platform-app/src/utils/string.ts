import { getStaticUrl } from './env'

/** 首字母大写 */
export function upperFirst(str: string) {
  return str.charAt(0).toUpperCase() + str.slice(1)
}

/**
 * 判断是否是外部链接或base64
 *
 * @param {string} path
 * @return {boolean}
 */
export function isExternal(path: string) {
  const isExternal = /^(?:https?:|http?:|mailto:|tel:|data:)/.test(path)
  return isExternal
}

/**
 * 拼接静态资源完整URL
 * @param path 资源路径
 * @returns 完整的静态资源URL
 */
export function getFullStaticUrl(path: string): string {
  const baseUrl = getStaticUrl()

  // 如果已经是完整URL，直接返回
  if (isExternal(path)) {
    return path
  }

  // 确保路径以 / 开头
  const normalizedPath = path.startsWith('/') ? path : `/${path}`

  // 确保baseUrl不以 / 结尾，避免双斜杠
  const normalizedBaseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl

  return `${normalizedBaseUrl}${normalizedPath}`
}
