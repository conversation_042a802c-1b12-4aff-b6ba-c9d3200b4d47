/**
 * 复制文本到剪贴板
 * 适配：Web端（现代/旧浏览器）、H5端（iOS WKWebView）、钉钉内嵌页面
 * @param text 要复制的文本
 * @returns Promise<boolean> 成功或失败
 */
export const copyText = async (text: string): Promise<boolean> => {
  return new Promise<boolean>((resolve) => {
    // 辅助函数：用于创建临时 textarea 并执行 document.execCommand
    const execCopyFallback = () => {
      const textarea = document.createElement("textarea");
      textarea.value = text;
      textarea.style.position = "fixed";
      textarea.style.opacity = "0";
      textarea.style.left = "-9999px";
      document.body.appendChild(textarea);
      textarea.focus();
      textarea.select();

      try {
        const successful = document.execCommand("copy");
        console.log("document.execCommand 复制结果:", successful);
        document.body.removeChild(textarea);
        resolve(successful);
      } catch (err) {
        console.error("降级复制失败:", err);
        document.body.removeChild(textarea);
        resolve(false);
      }
    };

    try {
      // 1. 优先：现代 Web 端 Clipboard API
      if (window.navigator.clipboard && window.navigator.clipboard.writeText) {
        window.navigator.clipboard
          .writeText(text)
          .then(() => {
            console.log("✅ 复制成功（Clipboard API）");
            resolve(true);
          })
          .catch((err) => {
            console.warn("📋 Clipboard API 失败，尝试降级:", err);
            execCopyFallback();
          });
        return;
      }

      // 2. iOS H5 环境（如企业微信、钉钉、自定义 WebView）
      if (window.webkit?.messageHandlers?.copyText) {
        try {
          window.webkit.messageHandlers.copyText.postMessage(text);
          console.log("✅ H5 复制请求已发送");
          resolve(true);
        } catch (err) {
          console.error("❌ H5 复制失败:", err);
          resolve(false);
        }
        return;
      }

      // 3. 钉钉内嵌环境
      if (typeof dd?.biz?.util?.copyText === "function") {
        dd.biz.util.copyText({
          text,
          onSuccess: () => {
            console.log("✅ 钉钉复制成功");
            resolve(true);
          },
          onFail: (err: any) => {
            console.error("❌ 钉钉复制失败:", err);
            resolve(false);
          },
        });
        return;
      }

      // 4. 降级：使用 document.execCommand（兼容老浏览器）
      if (document.queryCommandSupported?.("copy")) {
        console.warn("⚠️ 使用 document.execCommand 降级方案");
        execCopyFallback();
        return;
      }

      // 5. 完全不支持
      console.error("❌ 当前环境不支持任何复制功能");
      resolve(false);
    } catch (error) {
      console.error("❌ 复制过程中发生异常:", error);
      resolve(false);
    }
  });
};
