/**
 * 判断传进来的值是不是一个数字
 * @param o any
 */
export function isNumber(o: any) {
  return typeof Number(o) === 'number' && !Number.isNaN(Number(Number))
}

/**
 * 为数字添加千分号分隔符
 * @param num 数字或数字字符串
 * @param decimalPlaces 保留小数位数，默认为原始小数位数
 * @returns 格式化后的字符串
 */
export function formatNumberWithCommas(num: number | string, decimalPlaces?: number): string {
  if (num === null || num === undefined || num === '') {
    return ''
  }

  const numStr = String(num)
  const numValue = Number(numStr)

  // 如果不是有效数字，返回原始值
  if (Number.isNaN(numValue)) {
    return numStr
  }

  // 如果指定了小数位数，先格式化小数位
  const formattedNum = decimalPlaces !== undefined
    ? numValue.toFixed(decimalPlaces)
    : numStr

  // 分离整数和小数部分
  const parts = String(formattedNum).split('.')
  const integerPart = parts[0]
  const decimalPart = parts[1]

  // 为整数部分添加千分号
  const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',')

  // 组合整数和小数部分
  return decimalPart ? `${formattedInteger}.${decimalPart}` : formattedInteger
}
