/**
 * 判断一个变量是否是空
 * @param val 任意变量
 * @returns boolean
 */
export function isEmpty(val: any): boolean {
  if (typeof val === "boolean") {
    return false;
  }
  if (typeof val === "number") {
    return false;
  }
  if (val instanceof Array) {
    return val.length === 0;
  } else if (val instanceof Object) {
    return Object.keys(val).length === 0;
  } else {
    return (
      val === "null" ||
      val === null ||
      val === "undefined" ||
      val === undefined ||
      val === ""
    );
  }
}
