/** 依次检查传入的参数，返回第一个既不是 undefined 也不是 null 的参数 */
export function firstDefined<T>(...args: T[]): T | undefined {
  for (const arg of args) {
    if (arg !== undefined && arg !== null) {
      return arg
    }
  }
  return undefined
}

/** 判断传入的对象，value值是否都为 假值 */
export function isAllFalsy(obj: Record<string, any>): boolean {
  if (!obj || typeof obj !== 'object') {
    return true
  }
  return Object.values(obj).every((value) => {
    if (value === null || value === undefined) {
      return true
    }
    if (typeof value === 'string') {
      return value.trim() === ''
    }
    if (Array.isArray(value)) {
      return value.length === 0
    }
    if (typeof value === 'object') {
      return isAllFalsy(value)
    }
    if (value === 0) {
      return false
    }
    if (value === false) {
      return false
    }
    return !value
  })
}

/**
 * 过滤对象中值为 undefined、null、空字符串的属性
 * @param obj 要过滤的对象
 * @param options 过滤选项
 * @returns 过滤后的新对象
 */
export function filterEmptyValues<T extends Record<string, any>>(
  obj: T,
  options: {
    /** 是否过滤 undefined 值，默认 true */
    filterUndefined?: boolean
    /** 是否过滤 null 值，默认 true */
    filterNull?: boolean
    /** 是否过滤空字符串，默认 true */
    filterEmptyString?: boolean
    /** 是否过滤空白字符串（只包含空格、制表符等），默认 false */
    filterWhitespace?: boolean
    /** 是否过滤空数组，默认 false */
    filterEmptyArray?: boolean
    /** 是否过滤空对象，默认 false */
    filterEmptyObject?: boolean
  } = {},
): Partial<T> {
  const {
    filterUndefined = true,
    filterNull = true,
    filterEmptyString = true,
    filterWhitespace = false,
    filterEmptyArray = false,
    filterEmptyObject = false,
  } = options

  const result: Partial<T> = {}

  for (const [key, value] of Object.entries(obj)) {
    let shouldFilter = false

    // 检查 undefined
    if (filterUndefined && value === undefined) {
      shouldFilter = true
    }
    // 检查 null
    else if (filterNull && value === null) {
      shouldFilter = true
    }
    // 检查空字符串
    else if (filterEmptyString && value === '') {
      shouldFilter = true
    }
    // 检查空白字符串
    else if (filterWhitespace && typeof value === 'string' && value.trim() === '') {
      shouldFilter = true
    }
    // 检查空数组
    else if (filterEmptyArray && Array.isArray(value) && value.length === 0) {
      shouldFilter = true
    }
    // 检查空对象
    else if (filterEmptyObject && typeof value === 'object' && value !== null && !Array.isArray(value) && Object.keys(value).length === 0) {
      shouldFilter = true
    }

    if (!shouldFilter) {
      result[key as keyof T] = value
    }
  }

  return result
}

/**
 * 过滤对象中值为 undefined、null、空字符串的属性（简化版本）
 * @param obj 要过滤的对象
 * @returns 过滤后的新对象
 */
export function removeEmptyValues<T extends Record<string, any>>(obj: T): Partial<T> {
  return filterEmptyValues(obj, {
    filterUndefined: true,
    filterNull: true,
    filterEmptyString: true,
  })
}
