/**
 * 设备检测工具函数
 * 提供移动端、PC端、平板等设备类型检测功能
 */

/**
 * 检测是否为移动设备
 * @returns {boolean} 是否为移动设备
 */
export function isMobile(): boolean {
  const userAgent = navigator.userAgent;
  const mobileAgents = [
    'Android',
    'iPhone',
    'SymbianOS', 
    'Windows Phone',
    'iPod'
  ];
  
  return mobileAgents.some(agent => userAgent.indexOf(agent) > -1);
}

/**
 * 检测是否为平板设备
 * @returns {boolean} 是否为平板设备
 */
export function isTablet(): boolean {
  const userAgent = navigator.userAgent;
  return userAgent.indexOf('iPad') > -1;
}

/**
 * 检测是否为PC设备
 * @returns {boolean} 是否为PC设备
 */
export function isPC(): boolean {
  return !isMobile() && !isTablet();
}

/**
 * 检测是否为iOS设备
 * @returns {boolean} 是否为iOS设备
 */
export function isIOS(): boolean {
  const userAgent = navigator.userAgent;
  return /iPad|iPhone|iPod/.test(userAgent);
}

/**
 * 检测是否为Android设备
 * @returns {boolean} 是否为Android设备
 */
export function isAndroid(): boolean {
  const userAgent = navigator.userAgent;
  return userAgent.indexOf('Android') > -1;
}

/**
 * 检测是否为微信浏览器
 * @returns {boolean} 是否为微信浏览器
 */
export function isWeChat(): boolean {
  const userAgent = navigator.userAgent;
  return /MicroMessenger/i.test(userAgent);
}

/**
 * 检测是否为钉钉浏览器
 * @returns {boolean} 是否为钉钉浏览器
 */
export function isDingTalk(): boolean {
  const userAgent = navigator.userAgent;
  return /DingTalk/i.test(userAgent);
}

/**
 * 获取设备类型
 * @returns {'mobile' | 'tablet' | 'pc'} 设备类型
 */
export function getDeviceType(): 'mobile' | 'tablet' | 'pc' {
  if (isMobile()) return 'mobile';
  if (isTablet()) return 'tablet';
  return 'pc';
}

/**
 * 检测是否支持触摸
 * @returns {boolean} 是否支持触摸
 */
export function isTouchDevice(): boolean {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
}

/**
 * 获取屏幕尺寸信息
 * @returns {object} 屏幕尺寸信息
 */
export function getScreenInfo() {
  return {
    width: window.screen.width,
    height: window.screen.height,
    availWidth: window.screen.availWidth,
    availHeight: window.screen.availHeight,
    devicePixelRatio: window.devicePixelRatio || 1,
  };
}

/**
 * 获取视口尺寸信息
 * @returns {object} 视口尺寸信息
 */
export function getViewportInfo() {
  return {
    width: window.innerWidth,
    height: window.innerHeight,
    documentWidth: document.documentElement.clientWidth,
    documentHeight: document.documentElement.clientHeight,
  };
}

/**
 * 设备检测工具对象
 * 统一导出所有设备检测相关的工具函数
 */
export const deviceUtils = {
  isMobile,
  isTablet,
  isPC,
  isIOS,
  isAndroid,
  isWeChat,
  isDingTalk,
  getDeviceType,
  isTouchDevice,
  getScreenInfo,
  getViewportInfo,
} as const;

export default deviceUtils;
