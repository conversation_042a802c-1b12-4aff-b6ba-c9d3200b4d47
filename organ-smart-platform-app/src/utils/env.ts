/**
 * 环境变量工具函数
 * 提供便捷的环境变量访问和判断方法
 */

import type { EnvUtils } from '@/types/env'

/**
 * 判断是否为开发环境
 */
export const isDev = process.env.NODE_ENV === 'development'

/**
 * 判断是否为生产环境
 */
export const isProd = process.env.NODE_ENV === 'production'

/**
 * 获取应用名称
 * @returns 应用名称
 */
export function getAppName(): string {
  return process.env.VUE_APP_NAME || '应用'
}

/**
 * 获取 API 基础路径
 * @returns API 基础路径
 */
export function getApiBaseUrl(): string {
  return process.env.VUE_APP_BASE_API || '/api'
}

/**
 * 获取静态资源地址
 * @returns 静态资源 CDN 地址
 */
export function getStaticUrl(): string {
  return process.env.VUE_APP_STATIC_IMG_URL || ''
}

/**
 * 获取当前应用环境
 * @returns 应用环境标识
 */
export function getAppEnv() {
  return process.env.VUE_APP_ENV
}

/**
 * 环境变量工具对象
 * 统一导出所有环境相关的工具函数
 */
export const envUtils: EnvUtils = {
  isDev,
  isProd,
  getAppName,
  getApiBaseUrl,
  getStaticUrl,
}

/**
 * 环境变量常量
 * 提供常用环境变量的直接访问
 */
export const ENV = {
  /** Node.js 环境 */
  NODE_ENV: process.env.NODE_ENV,
  /** 应用名称 */
  APP_NAME: process.env.VUE_APP_NAME,
  /** 应用环境 */
  APP_ENV: process.env.VUE_APP_ENV,
  /** API 基础路径 */
  BASE_API: process.env.VUE_APP_BASE_API,
  /** 静态资源地址 */
  STATIC_URL: process.env.VUE_APP_STATIC_IMG_URL,
} as const

export default envUtils
