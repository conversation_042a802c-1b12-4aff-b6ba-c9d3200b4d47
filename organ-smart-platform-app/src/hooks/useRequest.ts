import { nextTick, onUnmounted, ref } from 'vue'

/**
 * useRequest 配置选项
 */
export interface UseRequestOptions<TData = any, TParams extends any[] = any[]> {
  /**
   * 是否手动触发请求
   * @default false
   */
  manual?: boolean

  /**
   * 默认参数
   */
  defaultParams?: TParams

  /**
   * 初始数据
   */
  initialData?: TData

  /**
   * 格式化请求结果
   */
  formatResult?: (response: TData) => any

  /**
   * 请求前回调
   */
  onBefore?: (params: TParams) => void

  /**
   * 请求成功回调
   */
  onSuccess?: (data: TData, params: TParams) => void

  /**
   * 请求失败回调
   */
  onError?: (error: Error, params: TParams) => void

  /**
   * 请求完成回调（无论成功失败）
   */
  onFinally?: (params: TParams, data?: TData, error?: Error) => void

  /**
   * 防抖延迟（毫秒）
   */
  debounceWait?: number

  /**
   * 节流延迟（毫秒）
   */
  throttleWait?: number

  /**
   * 轮询间隔（毫秒）
   */
  pollingInterval?: number

  /**
   * 错误重试次数
   */
  retryCount?: number

  /**
   * 重试间隔（毫秒）
   */
  retryInterval?: number

  /**
   * 是否在错误时回滚乐观更新
   */
  rollbackOnError?: boolean

  /**
   * loading 延迟显示时间（毫秒）
   */
  loadingDelay?: number
}

/**
 * useRequest 返回值
 */
export interface UseRequestResult<TData = any, TParams extends any[] = any[]> {
  /**
   * 响应数据
   */
  data: Readonly<Ref<TData | undefined>>

  /**
   * 错误信息
   */
  error: Readonly<Ref<Error | undefined>>

  /**
   * 加载状态
   */
  loading: Readonly<Ref<boolean>>

  /**
   * 请求参数
   */
  params: Readonly<Ref<TParams | []>>

  /**
   * 手动触发请求（同步）
   */
  run: (...params: TParams) => void

  /**
   * 手动触发请求（异步）
   */
  runAsync: (...params: TParams) => Promise<TData>

  /**
   * 使用上次参数重新请求（同步）
   */
  refresh: () => void

  /**
   * 使用上次参数重新请求（异步）
   */
  refreshAsync: () => Promise<TData>

  /**
   * 直接修改数据
   */
  mutate: (data?: TData | ((oldData?: TData) => TData | undefined)) => void

  /**
   * 取消当前请求
   */
  cancel: () => void
}

/**
 * 服务函数类型
 */
export type Service<TData, TParams extends any[]> = (...args: TParams) => Promise<TData>

/**
 * 强大的请求 Hook，支持防抖、节流、轮询、重试等功能
 *
 * @param service 请求服务函数
 * @param options 配置选项
 * @returns 请求状态和控制方法
 *
 * @example
 * ```ts
 * // 基础用法
 * const { data, loading, error } = useRequest(getUserInfo)
 *
 * // 手动触发
 * const { run, loading } = useRequest(updateUser, { manual: true })
 *
 * // 带参数
 * const { data, run } = useRequest(getUser, {
 *   defaultParams: ['123'],
 *   onSuccess: (data) => console.log('成功:', data)
 * })
 * ```
 */
export function useRequest<TData = any, TParams extends any[] = any[]>(
  service: Service<TData, TParams>,
  options: UseRequestOptions<TData, TParams> = {},
): UseRequestResult<TData, TParams> {
  const {
    manual = false,
    defaultParams,
    initialData,
    formatResult,
    onBefore,
    onSuccess,
    onError,
    onFinally,
    debounceWait,
    throttleWait,
    pollingInterval,
    retryCount = 0,
    retryInterval = 1000,
    rollbackOnError = false,
    loadingDelay = 0,
  } = options

  // 响应式状态
  const data = ref<TData | undefined>(initialData)
  const error = ref<Error | undefined>()
  const loading = ref(false)
  const params = ref<TParams | []>([])

  // 内部状态
  let currentPromise: Promise<TData> | null = null
  let retryTimer: NodeJS.Timeout | null = null
  let pollingTimer: NodeJS.Timeout | null = null
  let loadingDelayTimer: NodeJS.Timeout | null = null
  let debounceTimer: NodeJS.Timeout | null = null
  let throttleTimer: NodeJS.Timeout | null = null
  let lastCallTime = 0
  let isUnmounted = false

  // 清理定时器
  const clearTimers = () => {
    if (retryTimer) {
      clearTimeout(retryTimer)
      retryTimer = null
    }
    if (pollingTimer) {
      clearTimeout(pollingTimer)
      pollingTimer = null
    }
    if (loadingDelayTimer) {
      clearTimeout(loadingDelayTimer)
      loadingDelayTimer = null
    }
    if (debounceTimer) {
      clearTimeout(debounceTimer)
      debounceTimer = null
    }
    if (throttleTimer) {
      clearTimeout(throttleTimer)
      throttleTimer = null
    }
  }

  // 设置 loading 状态
  const setLoading = (value: boolean) => {
    if (loadingDelay && value) {
      loadingDelayTimer = setTimeout(() => {
        if (!isUnmounted) {
          loading.value = true
        }
      }, loadingDelay)
    }
    else {
      if (loadingDelayTimer) {
        clearTimeout(loadingDelayTimer)
        loadingDelayTimer = null
      }
      loading.value = value
    }
  }

  // 核心请求执行函数
  const executeRequest = async (requestParams: TParams, retryTimes = 0): Promise<TData> => {
    // 保存当前参数
    (params as any).value = requestParams

    // 执行前回调
    onBefore?.(requestParams)

    // 设置加载状态
    setLoading(true)
    error.value = undefined

    // 创建请求 Promise
    const promise = service(...requestParams)
    currentPromise = promise

    try {
      // 等待请求完成
      let result = await promise

      // 检查是否已被取消或组件已卸载
      if (currentPromise !== promise || isUnmounted) {
        return result
      }

      // 格式化结果
      if (formatResult) {
        result = formatResult(result)
      }

      // 更新数据
      (data as any).value = result

      // 成功回调
      onSuccess?.(result, requestParams)

      // 设置轮询
      if (pollingInterval && pollingInterval > 0) {
        pollingTimer = setTimeout(() => {
          if (!isUnmounted) {
            executeRequest(requestParams).catch(() => {
              // 轮询中的错误静默处理
            })
          }
        }, pollingInterval)
      }

      return result
    }
    catch (err) {
      const requestError = err instanceof Error ? err : new Error(String(err))

      // 检查是否已被取消或组件已卸载
      if (currentPromise !== promise || isUnmounted) {
        throw requestError
      }

      // 重试逻辑
      if (retryTimes < retryCount) {
        return new Promise((resolve, reject) => {
          retryTimer = setTimeout(() => {
            if (!isUnmounted) {
              executeRequest(requestParams, retryTimes + 1)
                .then(resolve)
                .catch(reject)
            }
            else {
              reject(requestError)
            }
          }, retryInterval)
        })
      }

      // 设置错误状态
      error.value = requestError

      // 错误回调
      onError?.(requestError, requestParams)

      throw requestError
    }
    finally {
      // 完成回调
      onFinally?.(requestParams, (data as any).value, error.value)

      // 清除加载状态
      setLoading(false)
    }
  }

  // 防抖包装函数
  const debounceExecute = (requestParams: TParams): Promise<TData> => {
    return new Promise((resolve, reject) => {
      if (debounceTimer) {
        clearTimeout(debounceTimer)
      }

      debounceTimer = setTimeout(() => {
        executeRequest(requestParams)
          .then(resolve)
          .catch(reject)
      }, debounceWait)
    })
  }

  // 节流包装函数
  const throttleExecute = (requestParams: TParams): Promise<TData> => {
    const now = Date.now()

    if (now - lastCallTime >= (throttleWait || 0)) {
      lastCallTime = now
      return executeRequest(requestParams)
    }

    return new Promise((resolve, reject) => {
      if (throttleTimer) {
        clearTimeout(throttleTimer)
      }

      const remaining = (throttleWait || 0) - (now - lastCallTime)
      throttleTimer = setTimeout(() => {
        lastCallTime = Date.now()
        executeRequest(requestParams)
          .then(resolve)
          .catch(reject)
      }, remaining)
    })
  }

  // 选择执行策略
  const getExecuteFunction = () => {
    if (debounceWait && debounceWait > 0) {
      return debounceExecute
    }
    if (throttleWait && throttleWait > 0) {
      return throttleExecute
    }
    return executeRequest
  }

  // 异步执行函数
  const runAsync = (...requestParams: TParams): Promise<TData> => {
    const executeFunc = getExecuteFunction()
    return executeFunc(requestParams)
  }

  // 同步执行函数
  const run = (...requestParams: TParams): void => {
    runAsync(...requestParams).catch((err) => {
      // run 方法自动处理错误，不需要外部 catch
      console.error('useRequest run error:', err)
    })
  }

  // 刷新函数（异步）
  const refreshAsync = (): Promise<TData> => {
    const lastParams = params.value as TParams
    return runAsync(...lastParams)
  }

  // 刷新函数（同步）
  const refresh = (): void => {
    refreshAsync().catch((err) => {
      console.error('useRequest refresh error:', err)
    })
  }

  // 直接修改数据
  const mutate = (newData?: TData | ((oldData?: TData) => TData | undefined)): void => {
    const previousData = (data as any).value

    if (typeof newData === 'function') {
      const mutateFunction = newData as (oldData?: TData) => TData | undefined
      const result = mutateFunction((data as any).value)
      if (result !== undefined) {
        (data as any).value = result
      }
    }
    else if (newData !== undefined) {
      (data as any).value = newData
    }

    // 如果启用了错误回滚，保存之前的数据以便回滚
    if (rollbackOnError && error.value) {
      (data as any).value = previousData
    }
  }

  // 取消当前请求
  const cancel = (): void => {
    currentPromise = null
    clearTimers()
    setLoading(false)
  }

  // 组件卸载时清理
  onUnmounted(() => {
    isUnmounted = true
    cancel()
  })

  // 自动执行初始请求
  if (!manual) {
    nextTick(() => {
      if (!isUnmounted) {
        const initialParams = (defaultParams || []) as TParams
        run(...initialParams)
      }
    })
  }

  // 返回结果
  return {
    data: data as Readonly<Ref<TData | undefined>>,
    error: error as Readonly<Ref<Error | undefined>>,
    loading: loading as Readonly<Ref<boolean>>,
    params: params as Readonly<Ref<TParams | []>>,
    run,
    runAsync,
    refresh,
    refreshAsync,
    mutate,
    cancel,
  }
}
