import { cloneDeep } from 'lodash'
import { ref } from 'vue'
/**
 * 创建一个可重置的值
 * @param value 初始值
 * @param clone 克隆函数
 * @returns 一个数组，包含 state 和 reset 方法
 * @example
 * // 基本使用
 * const [state, reset] = useResettableRef({ count: 0 })
 * state.value.count++ // 1
 * reset() // 重置为初始值 { count: 0 }
 */
export function useResettableRef<T>(value: T, clone = cloneDeep) {
  const initialValue = clone(value)
  const state = ref(value)
  const reset = () => {
    // @ts-expect-error - 这里需要忽略类型检查，因为 clone 函数的返回类型可能不完全匹配
    state.value = clone(initialValue)
  }
  return <const>[state, reset]
}
