import type { TabItem } from './type'
import { createFormChildrenItem, createTabItem } from './util'

/** 工商信息 */
const basic = createTabItem({
  label: '基本信息',
  children: [
    createFormChildrenItem({
      label: '工商信息',
      columns: [
        {
          label: '企业名称',
          prop: 'entname',
          span: 24,
        },
        {
          label: '法人姓名',
          prop: 'frname',
          span: 12,
        },
        {
          label: '组织机构代码',
          prop: 'nacaoid',
          span: 12,
        },
        {
          label: '统一社会信用代码',
          prop: 'uniscid',
          span: 12,
        },
        {
          label: '注册资本',
          prop: 'regcap',
          span: 12,
        },
        {
          label: '成立日期',
          prop: 'esdate',
          span: 12,
        },
        {
          label: '核准日期',
          prop: 'apprdate',
          span: 12,
        },
        {
          label: '经营范围',
          prop: 'opscope',
          span: 24,
        },
      ],
    }),
  ],
})

export const publicTemplate: TabItem[] = [basic]

export const template: TabItem[] = []
