import type { CommonDescriptionsColumns } from '@/components/CommonDescriptions/type'

/** 定义通用类型 */
export type AnyObject = Record<string, any>

export interface TabItem {
  /** 名称 */
  label: string
  /** 是否初始化 */
  isInit: boolean
  /** 父级的请求函数 */
  fetch?: () => void
  /** 子项 */
  children: TabChildren[]
  /** 本身初始化回调 初始化时触发或由外部主动触发 */
  initCallback?: (data?: AnyObject) => void

}

interface TabChildrenBase {
  /** 名称 */
  label: string
  /** 请求的参数 */
  params: AnyObject
  /** 子级的请求函数 */
  fetch?: () => Promise<any>
  // /** 全局初始化回调 由外部js触发 */
  /** 本身初始化回调 初始化时触发或由外部主动触发 */
  initCallback?: (data?: AnyObject) => void
}

export interface TabChildrenForm extends TabChildrenBase {
  /** 显示类型 */
  type: 'form'
  /** 数据 */
  data: AnyObject
  columns: CommonDescriptionsColumns
}

export type TabChildren = TabChildrenForm
