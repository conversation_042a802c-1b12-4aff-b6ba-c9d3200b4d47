import { cloneDeep } from 'lodash'
import { getEnterpriseDetail } from '@/api/enterprise'
import { publicTemplate } from '@/hooks/useReportDetail/template'

/**
 * 返回企业的报告信息
 * @param params 企业信息
 * @param isBrief 是否是简版报告
 */

export function useReportDetail(params: Parameters<typeof getEnterpriseDetail>[0], _isBrief = true) {
  /**
   * 企业的基本信息
   */
  const detail = ref<Awaited<ReturnType<typeof getEnterpriseDetail>>>({})

  /** 公共的tab 数据，不管是否vip都可查看 */
  const publicTab = ref(cloneDeep(publicTemplate))

  getEnterpriseDetail(params).then((res) => {
    detail.value = res
    publicTab.value.forEach(item => item.initCallback?.(res))
  })

  // initCallback

  // const tabs = ref(cloneDeep(template))

  return [detail, publicTab] as const
}

/**
 * 获取 非Vip时返回企业报告最基本的信息
 */
// export function useNotVipReportDetail(params: Parameters<typeof getEnterpriseDetail>[0]){
//   const
// }
