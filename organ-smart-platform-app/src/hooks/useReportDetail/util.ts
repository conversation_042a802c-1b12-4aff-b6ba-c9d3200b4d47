import type { TabChildrenForm, TabItem } from './type'
import { get, isEmpty } from 'lodash'
import { isAllFalsy } from '@/utils/object'

type TabItemOption = Partial<Omit<TabItem, 'isInit'>>

/**
 * 创建tab项
 */
export function createTabItem(options: RequiredSomeFields<TabItemOption, 'children'>): TabItem {
  return {
    label: options.label,
    isInit: false,
    initCallback(this: TabItemOption, data?: Record<string, any>) {
      if (data) {
        this.children?.forEach(item => item.initCallback(data))
      }
    },
    ...options,
  }
}

type TabItemChildrenOptions = Partial<Omit<TabChildrenForm, 'data' | 'params'>>
/**
 * 创建表单子项
 * @param options 配置项
 * @returns 表单子项
 */
export function createFormChildrenItem(
  options: RequiredSomeFields<TabItemChildrenOptions, 'label' | 'columns'>,
): TabChildrenForm {
  return {
    label: options.label,
    type: 'form',
    data: {},
    params: {},
    initCallback(data) {
      if (isEmpty(data) && this.fetch === undefined)
        return
      Promise.resolve(data || this.fetch?.()).then((res) => {
        const result = Object.fromEntries(
          this.columns.map(item => [item.prop, get(res, item.prop)]),
        )
        if (!isAllFalsy(result)) {
          this.data = result
        }
      })
    },
    ...options,
  }
}
