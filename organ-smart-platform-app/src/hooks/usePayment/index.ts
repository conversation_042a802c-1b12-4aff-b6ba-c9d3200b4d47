import type { PurchaseMembershipVO } from '@/api/order'
import { Message } from 'element-ui'
import { ref } from 'vue'

/**
 * 支付相关的 hook
 */
export function usePayment() {
  // 支付弹窗显示状态
  const paymentDialogVisible = ref(false)

  // 当前选中的套餐ID
  const selectedPackageId = ref('')

  // 用户ID（可选）
  const userId = ref<string>()

  /**
   * 开始支付流程
   * @param packageId 套餐ID
   * @param userIdParam 用户ID（可选）
   */
  function startPayment(packageId: string, userIdParam?: string) {
    if (!packageId) {
      Message.error('请选择要购买的套餐')
      return
    }

    selectedPackageId.value = packageId
    userId.value = userIdParam
    paymentDialogVisible.value = true
  }

  /**
   * 处理支付成功
   * @param orderInfo 订单信息
   */
  function handlePaymentSuccess(orderInfo: PurchaseMembershipVO) {
    Message.success('支付成功！')

    // 这里可以添加支付成功后的逻辑
    // 比如刷新用户会员状态、跳转到成功页面等
    console.log('支付成功，订单信息：', orderInfo)

    // 关闭支付弹窗
    paymentDialogVisible.value = false

    // 可以触发自定义事件
    // emit('payment-success', orderInfo)
  }

  /**
   * 关闭支付弹窗
   */
  function closePaymentDialog() {
    paymentDialogVisible.value = false
    selectedPackageId.value = ''
    userId.value = undefined
  }

  return {
    // 状态
    paymentDialogVisible,
    selectedPackageId,
    userId,

    // 方法
    startPayment,
    handlePaymentSuccess,
    closePaymentDialog,
  }
}
