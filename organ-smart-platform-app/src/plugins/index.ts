import type Vue from 'vue'
import type { Priority } from './config'

interface Modeles {
  index: `${Priority}`
  name: string
  install: (app: typeof Vue) => void
}

// 批量导入./modules 目录下的所有 路由模块
const modules = require.context('./modules', false, /\.ts$/)

const plugins = modules.keys().map<Modeles>((path) => {
  return modules(path).default
})

export default function (app: typeof Vue) {
  plugins.sort((a, b) => Number(a.index) - Number(b.index))

  plugins.forEach((plugin) => {
    plugin.install(app)
  })
}
