import ElementUI from 'element-ui'
import Vue from 'vue'
// import plugins from '@/plugins'
import App from './App.vue'
import router from './router'
import store from './store'
import 'element-ui/lib/theme-chalk/index.css'

import '@/styles/index.css'

Vue.config.productionTip = false
// Vue.use(plugins)
Vue.use(ElementUI, {
  size: 'default',
})

new Vue({
  router,
  store,
  render: h => h(App),
}).$mount('#app')
