<script setup lang="ts">
import { But<PERSON>, Link, Tag } from 'element-ui'
import { getInvoicePage } from '@/api/control'
import CommonMobileTable from '@/components/CommonMobileTable/index.vue'
import { formatDateTime } from '@/utils/date'
import { formatNumberWithCommas } from '@/utils/number'
import User from './comp/User.vue'

const router = useRouter()

// 发票状态映射
const statusMap = {
  pending: { text: '待处理', type: 'warning' },
  PENDING: { text: '待处理', type: 'warning' },
  processing: { text: '处理中', type: 'primary' },
  issued: { text: '已开票', type: 'success' },
  rejected: { text: '已拒绝', type: 'danger' },
}

function getStatusInfo(status: string) {
  return statusMap[status as keyof typeof statusMap] || { text: status, type: 'info' }
}

// 处理函数
function handleDownload(row: any) {
  // 这里可以触发发票下载
}

// 跳转到抬头管理
function handleHeaderManagement() {
  router.push('/mobile/control/invoicesHeader')
}

// 表格列配置
const tableColumns = [
  { label: '发票编号', prop: 'invoiceNo' },
  {
    label: '发票金额',
    prop: 'invoiceAmount',
    width: 120,
    formatter(_row: any, _column: any, cellValue: any) {
      return cellValue ? `¥${formatNumberWithCommas(cellValue, 2)}` : '-'
    },
  },
  {
    label: '申请时间',
    prop: 'createTime',
    width: 200,
    formatter(_row: any, _column: any, cellValue: any) {
      // 使用 dayjs 格式化时间，精确到秒
      return formatDateTime(cellValue)
    },
  },
  {
    label: '状态',
    prop: 'invoiceStatus',
    width: 100,
    formatter(_row: any, _column: any, cellValue: any) {
      return getStatusInfo(cellValue).text
    },
  },
]
</script>

<template>
  <CommonMobileTable :fetch="getInvoicePage" :columns="tableColumns">
    <template #header>
      <User />
      <div class="header-section">
        <div class="title">
          我的发票
        </div>
        <div class="header-actions">
          <Button type="primary" size="small" @click="handleHeaderManagement">
            抬头管理
          </Button>
        </div>
      </div>
    </template>

    <template #invoiceStatus="{ row }">
      <Tag
        :type="getStatusInfo(row.invoiceStatus).type"
        size="small"
      >
        {{ getStatusInfo(row.invoiceStatus).text }}
      </Tag>
    </template>

    <template #footer="{ row }">
      <Link
        v-if="row.invoiceStatus === 'issued'"
        type="success"
        icon="el-icon-download"
        @click="handleDownload(row)"
      >
        下载
      </Link>
    </template>
  </CommonMobileTable>
</template>

<style lang="scss" scoped>
.header-section {
  padding: 0px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;

  .title {
    /** 文本1 */
    font-size: 20px;
    font-weight: 500;
    letter-spacing: 0px;
    line-height: 1;
    color: rgba(56, 56, 56, 1);
  }

  .header-actions {
    display: flex;
    align-items: center;
  }
}
</style>
