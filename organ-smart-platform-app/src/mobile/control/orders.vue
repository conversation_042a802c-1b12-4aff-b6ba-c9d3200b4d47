<script setup lang="ts">
import { Link } from 'element-ui'
import { ref } from 'vue'
import { getMembershipOrderPage } from '@/api/control'
import CommonMobileTable from '@/components/CommonMobileTable/index.vue'
import { formatDateTime } from '@/utils/date'
import { formatNumberWithCommas } from '@/utils/number'
import User from './comp/User.vue'

// 申请发票弹窗状态
const invoiceDialogVisible = ref(false)
const selectedOrder = ref<any>(null)

// 处理申请发票
function handleApplyInvoice(row: any) {
  // selectedOrder.value = row
  // invoiceDialogVisible.value = true
}

// 表格列配置
const tableColumns = [
  { label: '订单编号', prop: 'orderNo' },
  { label: '会员套餐', prop: 'membershipName' },
  {
    label: '实付金额',
    prop: 'actualAmount',
    width: 120,
    formatter(_row: any, _column: any, cellValue: any) {
      return cellValue ? `¥${formatNumberWithCommas(cellValue, 2)}` : '-'
    },
  },
  {
    label: '支付时间',
    prop: 'paymentTime',
    width: 200,
    formatter(_row: any, _column: any, cellValue: any) {
      // 使用 dayjs 格式化时间，精确到秒
      return formatDateTime(cellValue)
    },
  },
]
</script>

<template>
  <div>
    <CommonMobileTable :fetch="getMembershipOrderPage" :columns="tableColumns">
      <template #header>
        <User />
        <div class="title">
          我的订单
        </div>
      </template>

      <!-- 操作按钮插槽 -->
      <template #footer="{ row }">
        <Link
          type="primary"
          @click="handleApplyInvoice(row)"
        >
          申请发票
        </Link>
      </template>
    </CommonMobileTable>
  </div>
</template>

<style lang="scss" scoped>
.title {
  padding: 0px 20px;
  /** 文本1 */
  font-size: 20px;
  font-weight: 500;
  letter-spacing: 0px;
  line-height: 1;
  color: rgba(56, 56, 56, 1);
}
</style>
