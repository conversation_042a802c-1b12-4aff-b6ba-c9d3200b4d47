<script setup lang="ts">
import { getFeedbackPage } from '@/api/control'
import CommonMobileTable from '@/components/CommonMobileTable/index.vue'
import { formatDateTime } from '@/utils/date'
import User from './comp/User.vue'

// 表格列配置
const tableColumns = [
  { label: '反馈类型', prop: 'feedbackTypeDesc' },
  {
    label: '反馈时间',
    prop: 'createTime',
    width: 200,
    formatter(_row: any, _column: any, cellValue: any) {
      // 使用 dayjs 格式化时间，精确到秒
      return formatDateTime(cellValue)
    },
  },
  {
    label: '联系电话',
    prop: 'contactPhone',
    width: 120,
    formatter(_row: any, _column: any, cellValue: any) {
      // 对联系电话中间四位数进行掩码处理
      if (!cellValue)
        return ''
      const phone = String(cellValue)
      if (phone.length === 11) {
        // 手机号格式：前3位 + **** + 后4位
        return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
      }
      else if (phone.length >= 7) {
        // 其他电话号码：保留前3位和后4位，中间用*号替代
        const start = phone.substring(0, 3)
        const end = phone.substring(phone.length - 4)
        const middle = '*'.repeat(phone.length - 7)
        return start + middle + end
      }
      return phone
    },
  },
]
</script>

<template>
  <CommonMobileTable :fetch="getFeedbackPage" :columns="tableColumns" :footer="false">
    <template #header>
      <User />
      <div class="title">
        意见反馈管理
      </div>
    </template>
  </CommonMobileTable>
</template>

<style lang="scss" scoped>
.title {
  padding: 0px 20px;
  /** 文本1 */
  font-size: 20px;
  font-weight: 500;
  letter-spacing: 0px;
  line-height: 1;
  color: rgba(56, 56, 56, 1);
}
</style>
