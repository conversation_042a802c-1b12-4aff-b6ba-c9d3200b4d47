<script setup lang="ts">
import type { InvoiceHeaderUpdateReqDTO } from '@/api/invoice/header'
import { Button, Link, Message, MessageBox, Tag } from 'element-ui'
import { ref } from 'vue'
import { deleteInvoiceHeader, getInvoiceHeaderPage, HeaderType, updateInvoiceHeader } from '@/api/invoice/header'
import CommonMobileTable from '@/components/CommonMobileTable/index.vue'
import InvoiceHeaderForm from '@/components/InvoiceHeaderForm/index.vue'
import { formatDateTime } from '@/utils/date'
import User from './comp/User.vue'

// 抬头类型映射
const headerTypeMap = {
  [HeaderType.Personal]: '个人',
  [HeaderType.Company]: '企业',
}

// 表格列配置
const tableColumns = [
  {
    label: '抬头名称',
    prop: 'headerName',
  },
  {
    label: '抬头类型',
    prop: 'headerType',
    formatter(_row: any, _column: any, cellValue: HeaderType) {
      return headerTypeMap[cellValue] || cellValue
    },
  },
  {
    label: '纳税人识别号',
    prop: 'taxNumber',
  },
  {
    label: '是否默认',
    prop: 'isDefault',
  },
  {
    label: '创建时间',
    prop: 'createTime',
    formatter(_row: any, _column: any, cellValue: any) {
      return formatDateTime(cellValue)
    },
  },
  {
    label: '创建人',
    prop: 'creator',
  },
]

// 表格引用
const tableRef = ref()

// 弹窗状态
const dialogVisible = ref(false)
const editData = ref<any>(null)

// 操作处理函数
function handleCreate() {
  editData.value = null // 新增时清空编辑数据
  dialogVisible.value = true
}

function handleEdit(row: any) {
  editData.value = row // 编辑时传入行数据
  dialogVisible.value = true
}

// 表单操作成功回调
function handleFormSuccess() {
  // 刷新表格数据
  if (tableRef.value) {
    tableRef.value.initFetchData()
  }
}

async function handleDelete(row: any) {
  try {
    await MessageBox.confirm(
      `确定要删除发票抬头"${row.headerName}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    // 调用删除API
    await deleteInvoiceHeader(row.id)
    Message.success('删除成功')

    // 刷新表格数据
    if (tableRef.value) {
      tableRef.value.initFetchData()
    }
  }
  catch (error: any) {
    if (error !== 'cancel') {
      Message.error(error.message || '删除失败')
    }
  }
}

async function handleSetDefault(row: any) {
  try {
    await MessageBox.confirm(
      `确定要将"${row.headerName}"设为默认发票抬头吗？`,
      '设为默认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info',
      },
    )

    // 使用对象展开语法，只覆盖需要修改的字段
    const updateData: InvoiceHeaderUpdateReqDTO = {
      ...row, // 展开所有原有字段
      isDefault: true, // 只修改这个字段
    }

    // 调用更新API设为默认
    await updateInvoiceHeader(updateData)
    Message.success('设置成功')

    // 刷新表格数据
    if (tableRef.value) {
      tableRef.value.initFetchData()
    }
  }
  catch (error: any) {
    // 用户取消操作或设置失败
    if (error !== 'cancel') {
      Message.error(error.message || '设置失败')
    }
  }
}
</script>

<template>
  <div class="invoice-header-management">
    <CommonMobileTable
      ref="tableRef"
      :fetch="getInvoiceHeaderPage"
      :columns="tableColumns"
    >
      <template #header>
        <User />
        <div class="header-section">
          <div class="title">
            发票抬头管理
          </div>
          <div class="header-actions">
            <Button type="primary" size="small" @click="handleCreate">
              新增抬头
            </Button>
          </div>
        </div>
      </template>

      <!-- 是否默认列插槽 -->
      <template #isDefault="{ row }">
        <Tag
          v-if="row.isDefault"
          type="success"
          size="small"
        >
          默认
        </Tag>
        <span v-else class="text-muted">-</span>
      </template>

      <!-- 操作列插槽 -->
      <template #footer="{ row }">
        <div class="action-buttons">
          <Link
            type="primary"
            @click="handleEdit(row)"
          >
            编辑
          </Link>
          <Link
            v-if="!row.isDefault"
            type="success"
            @click="handleSetDefault(row)"
          >
            设为默认
          </Link>
          <Link
            style="color: #f56c6c;"
            @click="handleDelete(row)"
          >
            删除
          </Link>
        </div>
      </template>
    </CommonMobileTable>

    <!-- 新增/编辑发票抬头表单 -->
    <InvoiceHeaderForm
      :visible="dialogVisible"
      :edit-data="editData"
      @update:visible="dialogVisible = $event"
      @success="handleFormSuccess"
    />
  </div>
</template>

<style lang="scss" scoped>
.invoice-header-management {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header-section {
  padding: 0px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;

  .title {
    /** 文本1 */
    font-size: 20px;
    font-weight: 500;
    letter-spacing: 0px;
    line-height: 1;
    color: rgba(56, 56, 56, 1);
  }

  .header-actions {
    display: flex;
    align-items: center;
  }
}

.action-buttons {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.text-muted {
  color: #999;
  font-size: 12px;
}
</style>
