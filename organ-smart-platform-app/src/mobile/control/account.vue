<script setup lang="ts">
import { Button } from 'element-ui'
import CommonScroll from '@/components/CommonScroll/index.vue'
import User from './comp/User.vue'

// 账号管理菜单项
const accountMenuItems = [
  {
    title: '个人信息',
    icon: '👤',
    desc: '查看和编辑个人基本信息',
    action: 'profile',
  },
  {
    title: '密码修改',
    icon: '🔒',
    desc: '修改登录密码',
    action: 'password',
  },
  {
    title: '绑定手机',
    icon: '📱',
    desc: '绑定或更换手机号码',
    action: 'phone',
  },
  {
    title: '实名认证',
    icon: '📄',
    desc: '完成实名认证，提升账号安全',
    action: 'verify',
  },
  {
    title: '账号注销',
    icon: '⚠️',
    desc: '永久注销账号（不可恢复）',
    action: 'delete',
  },
]

function handleMenuClick(action: string) {
  console.log('点击菜单:', action)
  // 这里可以根据不同的action跳转到对应的页面或打开弹窗
  switch (action) {
    case 'profile':
      // 跳转到个人信息页面
      break
    case 'password':
      // 打开密码修改弹窗
      break
    case 'phone':
      // 打开手机绑定弹窗
      break
    case 'verify':
      // 跳转到实名认证页面
      break
    case 'delete':
      // 打开账号注销确认弹窗
      break
  }
}
</script>

<template>
  <CommonScroll
    @reach-bottom="() => console.log('滑动到底部了')"
    @scroll="(scrollTop, scrollHeight, clientHeight) => console.log('滚动信息:', { scrollTop, scrollHeight, clientHeight })"
  >
    <template #header>
      <User />
      <div class="title">
        账号管理
      </div>
    </template>

    <div class="account-content">
      <div class="menu-list">
        <div
          v-for="(item, index) in accountMenuItems"
          :key="index"
          class="menu-item"
          @click="handleMenuClick(item.action)"
        >
          <div class="menu-icon">{{ item.icon }}</div>
          <div class="menu-info">
            <div class="menu-title">{{ item.title }}</div>
            <div class="menu-desc">{{ item.desc }}</div>
          </div>
          <div class="menu-arrow">›</div>
        </div>
      </div>

      <div class="logout-section">
        <Button type="danger" size="large" class="logout-btn">
          退出登录
        </Button>
      </div>
    </div>
  </CommonScroll>
</template>

<style lang="scss" scoped>
.title {
  padding: 0px 20px;
  /** 文本1 */
  font-size: 20px;
  font-weight: 500;
  letter-spacing: 0px;
  line-height: 1;
  color: rgba(56, 56, 56, 1);
}

.account-content {
  padding: 20px;
}

.menu-list {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  .menu-item {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #f5f5f5;
    cursor: pointer;
    transition: all 0.2s ease;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background: #f8f9fa;
    }

    &:active {
      background: #e9ecef;
    }

    .menu-icon {
      font-size: 24px;
      margin-right: 16px;
      width: 32px;
      text-align: center;
    }

    .menu-info {
      flex: 1;

      .menu-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin-bottom: 4px;
      }

      .menu-desc {
        font-size: 13px;
        color: #666;
        line-height: 1.2;
      }
    }

    .menu-arrow {
      font-size: 18px;
      color: #ccc;
      font-weight: 300;
    }
  }
}

.logout-section {
  margin-top: 30px;
  text-align: center;

  .logout-btn {
    width: 100%;
    height: 48px;
    font-size: 16px;
    font-weight: 500;
    border-radius: 12px;
  }
}
</style>
