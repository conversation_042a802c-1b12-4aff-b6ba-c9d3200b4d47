<script setup lang="ts">
import { Input, Link } from 'element-ui'
import { throttle } from 'lodash'
import { getReportViewRecordPage } from '@/api/control'
import CommonMobileTable from '@/components/CommonMobileTable/index.vue'
import { formatDateTime } from '@/utils/date'
import User from './comp/User.vue'

const search = ref('')

const handleInput = throttle((fetch: (params: Record<string, any>) => any) => {
  const params = {
    enterpriseName: search.value,
  }
  fetch(params)
}, 300)

// 处理函数
function handleView(row: any) {
  // 这里可以跳转到详情页面或打开弹窗
}

function handleDelete(row: any) {
  // 这里可以打开删除确认弹窗
}

// 表格列配置
const tableColumns = [
  { label: '企业名称', prop: 'enterpriseName' },
  {
    label: '查看时间',
    prop: 'createTime',
    width: 200,
    formatter(_row: any, _column: any, cellValue: any) {
      // 使用 dayjs 格式化时间，精确到秒
      return formatDateTime(cellValue)
    },
  },
  { label: '查看结果', prop: 'viewResult', width: 120 },
]
</script>

<template>
  <CommonMobileTable :fetch="getReportViewRecordPage" :columns="tableColumns">
    <template #header="{ initFetchData }">
      <User />
      <div class="title">
        报告查看管理
      </div>
      <div class="search">
        <Input
          v-model="search" prefix-icon="el-icon-search" placeholder="搜索企业名称"
          clearable
          @input="handleInput(initFetchData)"
        />
      </div>
    </template>
    <template #footer="{ row }">
      <Link type="primary" @click="handleView(row)">查看</Link>
      <Link type="danger" @click="handleDelete(row)">删除</Link>
    </template>
  </CommonMobileTable>
</template>

<style lang="scss" scoped>
.title {
  padding: 0px 20px;
  /** 文本1 */
  font-size: 20px;
  font-weight: 500;
  letter-spacing: 0px;
  line-height: 1;
  color: rgba(56, 56, 56, 1);
}
.search {
  padding: 0px 20px;
  margin-top: 10px;
}
.el-link {
  +.el-link {
    margin-left: 10px;
  }
}
</style>
