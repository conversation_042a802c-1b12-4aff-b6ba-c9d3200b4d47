<script setup lang="ts">
import { Avatar } from 'element-ui'
import { getUserInfo } from '@/api/user'
import { useRequest } from '@/hooks/useRequest'
import { formatDateTime } from '@/utils/date'
import { getFullStaticUrl } from '@/utils/string'

const props = defineProps<{
  // userId: string
}>()

const router = useRouter()

/** logo */
const hyxLogo = getFullStaticUrl('crc/login-logo.png')

const { data: userInfo } = useRequest(getUserInfo, {
  defaultParams: ['1'],
})

// 菜单配置 - 根据UI图精确还原
const menuItems = [
  {
    title: '报告查看管理',
    icon: 'el-icon-tickets',
    path: '/mobile/control/review',
  },
  {
    title: '我的订单',
    icon: 'el-icon-document',
    path: '/mobile/control/orders',
  },
  {
    title: '我的发票',
    icon: 'el-icon-takeaway-box',
    path: '/mobile/control/invoices',
  },
  {
    title: '意见反馈',
    icon: 'el-icon-chat-line-round',
    path: '/mobile/control/feedback',
  },
  {
    title: '账号管理',
    icon: 'el-icon-user',
    path: '/mobile/control/account',
  },
]

// 处理菜单点击事件
function handleMenuClick(path: string) {
  router.push(path)
}
</script>

<template>
  <div class="size-full flex-col">
    <div class="header flex flex-align-center">
      <img class="logo" :src="hyxLogo" alt="logo">
      <div class="text-content flex-1 flex-col">
        <div class="title enterprise-credit-title">
          查看企业信用信息
        </div>
        <div class="sub-title">甄选优质企业岗位·抢占优质岗位先机</div>
      </div>
    </div>

    <div class="content">
      <div class="user flex">
        <Avatar
          class="user-avatar" :size="48"
          src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"
        />
        <div class="user-info flex-1 flex-col">
          <div class="user-name">{{ userInfo?.membershipTypeDesc }}</div>
          <div class="user-detail flex flex-align-center">
            <div v-if="userInfo?.isExpired">会员已经过期</div>
            <div v-else>会员有效期至: {{ formatDateTime(userInfo?.expireTime) }}</div>
            <div class="updata-level flex-center">
              会员升级
            </div>
          </div>
        </div>
      </div>
      <div class="list">
        <div
          v-for="item in menuItems" :key="item.path" class="list-item flex flex-align-center"
          @click="handleMenuClick(item.path)"
        >
          <div class="icon flex-center">
            <i :class="[item.icon]" />
          </div>
          {{ item.title }}
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.header {
  width: 100%;
  height: 80px;
  padding: 0 40px;
  .logo {
    height: 30px;
    object-fit: cover;
  }
  .text-content {
    margin-left: 10px;
    .title {
      font-size: 18px;
      color: #1658D9;

    }
    .sub-title {
      color: #383838;
      font-size: 12px;
    }
  }

}
.content {
  margin: 0 12px;
  padding: 16px;
  border-radius: 20px;
  background: linear-gradient(180deg, #FFFFFF 0%, #FFFFFF 100%);
  .user {
    .user-info {
      justify-content: space-between;
      margin-left: 10px;
      .user-name {
        color: #333333;
      }
      .user-detail {
        font-size: 12px;
        color: #808080;
        .updata-level {
          margin-left: 10px;
          padding: 4px 6px;
          color: #ffffff;
          border-radius: 16px;
          line-height: 1;
          background: linear-gradient(137.02deg, #f03a31 0%, #f09e9e 100%);
        }
      }
    }
  }
  .list {
    margin-top: 20px;
    .list-item {
      padding: 12px;
      border-radius: 10px;
      background: linear-gradient(180deg, #E2EDFF 0%, #fbfbfc 100%);
      border: 2px solid #FFFFFF;
      color: #383838;
      +.list-item {
        margin-top: 13px;
      }
      .icon {
        width: 24px;
        height: 24px;
        border-radius: 20%;
        margin-right: 10px;
        color: #1658D9;
        background-color: #ffffff;
      }
    }
  }
}
</style>
