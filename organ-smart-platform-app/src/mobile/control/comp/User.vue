<script setup lang="ts">
import { getUserInfo } from '@/api/user'
import { useRequest } from '@/hooks/useRequest'
import { formatDateTime } from '@/utils/date'

// TODO 测试阶段，用户id写死，后续不需要
const { data: userInfo } = useRequest(getUserInfo, {
  defaultParams: ['1'],
})
</script>

<template>
  <div class="mobile-control-user">
    <div class="user-content flex">
      <div class="flex flex-align-center">
        {{ userInfo?.membershipTypeDesc }}
        <div v-if="userInfo?.isExpired" class="date">会员已经过期</div>
        <div v-else class="date">会员有效期至: {{ formatDateTime(userInfo?.expireTime) }}</div>
      </div>
      <div class="updata-level flex-center">
        会员升级
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.mobile-control-user {
  width: 100%;
  padding: 20px 20px;
  .user-content {
    padding: 10px;
    background-color: #FDF2F2;
    justify-content: space-between;
    .date {
      font-size: 15px;
      color: #808080;
      margin-left: 10px;
    }
    .updata-level {
      font-size: 12px;
      margin-left: 10px;
      padding: 4px 6px;
      color: #ffffff;
      border-radius: 16px;
      line-height: 1;
      background: linear-gradient(137.02deg, #f03a31 0%, #f09e9e 100%);
    }
  }
}
</style>
