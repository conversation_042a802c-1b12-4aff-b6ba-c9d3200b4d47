<script setup lang="ts">
import type { BasicData, JobRiskVO } from '@/api/enterprise'
import * as echarts from 'echarts'
import { cloneDeep } from 'lodash'
import { computed, onMounted, ref, watch } from 'vue'
import CommonDescriptions from '@/components/CommonDescriptions/index.vue'
import { publicTemplate } from '@/hooks/useReportDetail/template'
import { formatNumberWithCommas } from '@/utils/number'
import Collapse from './Collapse.vue'

const props = defineProps<{
  detail?: BasicData
  jobRisk?: JobRiskVO
  isExpired?: boolean
}>()

const publicTab = cloneDeep(publicTemplate)

/**
 * 移动端优化的template - 将form类型的columns调整为24
 * 在移动端，表单字段需要占满整行以获得更好的显示效果
 */
const template = computed(() => {
  return publicTab.map(tab => ({
    ...tab,
    children: tab.children?.map((child) => {
      const processedChild = { ...child }

      // 处理columns配置
      if (child.type === 'form') {
        // form类型：将所有column的span设置为24（占满整行）
        if (child.columns) {
          processedChild.columns = child.columns.map(column => ({
            ...column,
            span: 24, // 移动端表单字段占满整行
          }))
        }
      }
      else {
        // 非form类型：保持原有配置不变
        processedChild.columns = child.columns
      }

      return processedChild
    }),
  }))
})

// 雷达图 DOM 引用
const radarChart = ref<HTMLDivElement>()

// 评分数据
const scoreData = computed(() => [
  { name: '企业负面信息', value: props.jobRisk?.indexA || 0 },
  { name: '薪资吸引能力', value: props.jobRisk?.indexB || 0 },
  { name: '技术成长潜力', value: props.jobRisk?.indexC || 0 },
  { name: '行业竞争能力', value: props.jobRisk?.indexD || 0 },
  { name: '职业发展前景', value: props.jobRisk?.indexE || 0 },
])

// 雷达图数据
const radarData = computed(() => [
  { name: '企业负面信息', value: props.jobRisk?.indexA || 0, max: 100 },
  { name: '薪资吸引能力', value: props.jobRisk?.indexB || 0, max: 100 },
  { name: '技术成长潜力', value: props.jobRisk?.indexC || 0, max: 100 },
  { name: '行业竞争能力', value: props.jobRisk?.indexD || 0, max: 100 },
  { name: '职业发展前景', value: props.jobRisk?.indexE || 0, max: 100 },
])

let chartInstance: echarts.ECharts | null = null

// 渲染雷达图
function renderRadarChart() {
  if (!radarChart.value)
    return

  if (!chartInstance) {
    chartInstance = echarts.init(radarChart.value)
  }

  const option = {
    radar: {
      indicator: radarData.value.map(item => ({
        name: item.name,
        max: item.max,
      })),
      center: ['50%', '50%'],
      radius: '60%',
      axisName: {
        color: '#999',
        fontSize: 12,
      },
      splitLine: {
        lineStyle: {
          color: '#e6e6e6',
        },
      },
      axisLine: {
        lineStyle: {
          color: '#e6e6e6',
        },
      },
      splitArea: {
        show: false,
      },
    },
    series: [{
      type: 'radar',
      data: [{
        value: radarData.value.map(item => item.value),
        areaStyle: {
          color: 'rgba(54, 144, 255, 0.2)',
        },
        lineStyle: {
          color: '#3690ff',
        },
        itemStyle: {
          color: '#3690ff',
        },
      }],
    }],
  }

  chartInstance.setOption(option)

  // 响应式处理
  window.addEventListener('resize', () => {
    chartInstance?.resize()
  })
}

// 监听数据变化
watch(() => radarData.value, () => {
  renderRadarChart()
}, { deep: true })

onMounted(() => {
  renderRadarChart()
})
</script>

<template>
  <Collapse title="01 雇主基本情况">
    <div v-for="tabs in template" :key="tabs.label">
      <div v-for="tab in tabs.children" :key="tab.label">
        <CommonDescriptions :data="detail" :columns="tab.columns" label-width="140">
          <template #prop-regcap="{ value }">
            {{ formatNumberWithCommas(value) }}万人民币
          </template>
        </CommonDescriptions>
      </div>
    </div>

    <div class="main-title">
      雇主价值整体评估总分
    </div>
    <div class="sub-title">
      雇主价值总分主要反应企业在其 <span style="color: #FF8D1A;">福利信息、负面信息、雇主发展力</span>等五大核心维度的综合实力
    </div>
    <div class="main-title">
      评估结论
    </div>
    <div class="sub-title">
      中科极限元(杭州)智能科技股份有限公司雇主价值评分为<span style="color: #FF8D1A;">51.7</span>分，属于<span
        style="color: #FF8D1A;"
      >稳健价值型企业</span>。
    </div>

    <div class="valuation-step1">
      <!-- 雷达图 -->
      <div class="radar-chart-container">
        <div ref="radarChart" class="radar-chart" />
      </div>

      <!-- 说明文字 -->
      <div class="description">
        分值说明：雇主价值评分，分值在[0, 100]区间，分值越高，则代表招聘场景下企业的综合实力越强。
      </div>

      <!-- 各项评分条形图 -->
      <div class="score-bars">
        <!-- 总分显示 -->
        <div class="total-score">
          总分: {{ props.jobRisk?.indexTotal || 0 }}
        </div>
        <div v-for="item in scoreData" :key="item.name" class="score-item">
          <div class="score-item-header">
            <span class="score-name">{{ item.name }}</span>
            <span class="score-number">{{ item.value }}</span>
          </div>
          <div class="score-bar">
            <div class="score-bar-bg">
              <div class="score-bar-fill" :style="{ width: `${(item.value / 100) * 100}%` }" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </Collapse>
</template>

<style lang="scss" scoped>
.main-title {
  color: #383838;
  line-height: 1em;
  font-size: 20px;
  margin-top: 30px;
  font-weight: 500;
}

.sub-title {
  color: #383838;
  margin-top: 20px;
}

// 雇主价值评分组件样式
.valuation-step1 {
  border-radius: 8px;
  padding: 20px;
  margin-top: 16px;
  background-color: #F7FAFF;

  .radar-chart-container {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;

    .radar-chart {
      width: 280px;
      height: 280px;
    }
  }

  .total-score {
    background-color: #E7EFFF;
    padding: 8px 10px;
    margin-bottom: 10px;
    font-size: 18px;
    font-weight: bold;
    color: #3690ff;
  }

  .score-bars {
    background-color: #ffffff;
    margin-bottom: 16px;
    padding-bottom: 10px;
    margin-top: 16px;

    .score-item {
      padding: 0 10px;
      margin-bottom: 16px;

      .score-item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .score-name {
          font-size: 14px;
          color: #333;
        }

        .score-number {
          font-size: 14px;
          color: #666;
        }
      }

      .score-bar {
        .score-bar-bg {
          width: 100%;
          height: 8px;
          background: #f0f0f0;
          border-radius: 4px;
          overflow: hidden;

          .score-bar-fill {
            height: 100%;
            background: linear-gradient(90deg, #3690ff 0%, #36a2ff 100%);
            border-radius: 4px;
            transition: width 0.3s ease;
          }
        }
      }
    }
  }

  .description {
    font-size: 12px;
    color:#999;
  }
}
</style>
