<script setup lang="ts">
import type { BasicData, JobRiskVO } from '@/api/enterprise'
import Step1 from './Step1.vue'
import Step2 from './Step2.vue'
import Step3 from './Step3.vue'
import Step4 from './Step4.vue'

const props = defineProps<{
  detail?: BasicData
  isExpired?: boolean
  jobRisk?: JobRiskVO
}>()
</script>

<template>
  <div class="valuation">
    <Step1 :detail="detail" :job-risk="jobRisk" />
    <Step2 :is-expired="isExpired" :detail="detail" :job-risk="jobRisk" />
    <template v-if="isExpired">
      <Step3 :detail="detail" :job-risk="jobRisk" />
      <Step4 :detail="detail" :job-risk="jobRisk" />
    </template>
  </div>
</template>

<style lang="scss" scoped>

</style>
