<script setup lang="ts">
import * as echarts from 'echarts'
import { onMounted, ref, watch } from 'vue'
import Collapse from './Collapse.vue'

export interface PublicTemplateProps {
  /**
   * 步骤标题
   */
  setpTitle?: string
  /**
   * 标题
   */
  mainTitle?: string
  /**
   * 副标题，会被v-html渲染
   */
  subTitle?: string

  /** 评分描述 */
  ratingDescription?: string
  /**
   * 圆形图的介绍
   */
  ringDescribe?: string

  ringProgressOptions?: FixedLengthTuple<{
    percentage: number
    color: string
    fillColor: string
    bgColor: string
    text: string
  }, 2>
}

const props = defineProps<PublicTemplateProps>()

const RegionChartRef = ref<FixedLengthTuple<HTMLDivElement, 2>>()
const chartInstances = ref<echarts.ECharts[]>([])

// 渲染图表的函数
function renderCharts() {
  if (!RegionChartRef.value || !props.ringProgressOptions)
    return

  RegionChartRef.value.forEach((item, index) => {
    if (!chartInstances.value[index]) {
      chartInstances.value[index] = echarts.init(item)
    }

    const config = props.ringProgressOptions[index]
    if (config) {
      chartInstances.value[index].setOption(createRingProgressOption(config.percentage, config.fillColor, config.color))
    }
  })
}

onMounted(() => {
  renderCharts()
})

// 监听props.ringProgressOptions的变化
watch(() => props.ringProgressOptions, () => {
  renderCharts()
}, { deep: true })

// 创建圆形进度条的配置
function createRingProgressOption(percentage: number, color: string, bgColor: string) {
  return {
    series: [
      {
        type: 'gauge',
        startAngle: 90, // 从顶部开始（12点方向）
        endAngle: 450, // 逆时针一圈（90 + 360 = 450）
        clockwise: false, // 逆时针方向
        pointer: {
          show: false, // 隐藏指针
        },
        progress: {
          show: true,
          overlap: false,
          roundCap: true, // 圆角端点
          clip: false,
          itemStyle: {
            borderWidth: 0,
            color,
          },
        },
        axisLine: {
          lineStyle: {
            width: 8, // 移动端适当减小线宽
            color: [[1, bgColor]], // 背景色
          },
        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
        data: [
          {
            value: percentage,
            name: '',
            title: {
              show: false,
            },
            detail: {
              valueAnimation: true,
              fontSize: 16, // 移动端适当减小字体
              fontWeight: 'bold',
              color: '#333',
              formatter: '{value}%',
              offsetCenter: [0, 0],
            },
          },
        ],
        radius: '80%',
        center: ['50%', '50%'],
      },
    ],
  }
}
</script>

<template>
  <div v-if="setpTitle">
    <Collapse :title="setpTitle">
      <slot name="header" />

      <div v-if="mainTitle" class="main-title">
        {{ mainTitle }}
      </div>

      <div v-if="subTitle" class="sub-title" v-html="subTitle" />

      <div v-if="ringProgressOptions?.length" class="setp-content">
        <!-- 左侧内容区域 -->
        <div class="setp-content-left">
          <div class="content-slot">
            <slot />
          </div>
          <div v-if="ratingDescription" class="rating-description">
            {{ ratingDescription }}
          </div>
        </div>

        <!-- 右侧圆形图区域 -->
        <div class="setp-content-right">
          <div v-if="ringDescribe" class="ring-describe">
            {{ ringDescribe }}
          </div>

          <div class="echarts-container">
            <div
              v-for="(item, index) in ringProgressOptions"
              :key="index"
              class="echarts-ring"
              :style="{ backgroundColor: item.bgColor }"
            >
              <div ref="RegionChartRef" class="chart-container" />
              <div class="echarts-ring-text">{{ item.text }}</div>
            </div>
          </div>
        </div>
      </div>
    </Collapse>
  </div>

  <!-- 没有setpTitle时的内容显示 -->
  <div v-else class="template-content">
    <slot name="header" />

    <div v-if="mainTitle" class="main-title">
      {{ mainTitle }}
    </div>

    <div v-if="subTitle" class="sub-title" v-html="subTitle" />

    <div v-if="ringProgressOptions?.length" class="setp-content">
      <!-- 左侧内容区域 -->
      <div class="setp-content-left">
        <div class="content-slot">
          <slot />
        </div>
        <div v-if="ratingDescription" class="rating-description">
          {{ ratingDescription }}
        </div>
      </div>

      <!-- 右侧圆形图区域 -->
      <div class="setp-content-right">
        <div v-if="ringDescribe" class="ring-describe">
          {{ ringDescribe }}
        </div>

        <div class="echarts-container">
          <div
            v-for="(item, index) in ringProgressOptions"
            :key="index"
            class="echarts-ring"
            :style="{ backgroundColor: item.bgColor }"
          >
            <div ref="RegionChartRef" class="chart-container" />
            <div class="echarts-ring-text">{{ item.text }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.template-content {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
}

.main-title {
  color: #383838;
  line-height: 1.2;
  font-size: 18px;
  margin-top: 20px;
  font-weight: 500;
}

.sub-title {
  color: #383838;
  line-height: 1.4;
  margin-top: 16px;
  font-size: 14px;
}

.setp-content {
  margin-top: 20px;
  padding: 16px;
  background-color: #F6F9FF;
  border-radius: 8px;

  .setp-content-left {
    margin-bottom: 20px;

    .content-slot {
      margin-bottom: 16px;
      height: 180px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .rating-description {
      color: #837F81;
      font-size: 14px;
      line-height: 1.5;
    }
  }

  .setp-content-right {
    background-color: #ffffff;
    border-radius: 8px;
    padding: 16px;

    .ring-describe {
      font-size: 16px;
      line-height: 1.6;
      margin-bottom: 20px;
      text-align: center;
      color: #333;
    }

    .echarts-container {
      display: flex;
      flex-direction: row;
      gap: 16px;
      justify-content: space-around;

      .echarts-ring {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px;
        border-radius: 8px;
        flex: 1;
        max-width: 180px;

        .chart-container {
          width: 120px;
          height: 120px;
        }

        .echarts-ring-text {
          font-size: 12px;
          color: #666;
          font-weight: 400;
          margin-top: 12px;
          text-align: center;
          line-height: 1.4;
        }
      }
    }
  }

  // 在小屏幕设备上使用垂直布局
  @media (max-width: 360px) {
    .echarts-container {
      flex-direction: column;

      .echarts-ring {
        max-width: none;
        width: 100%;
      }
    }
  }
}
</style>
