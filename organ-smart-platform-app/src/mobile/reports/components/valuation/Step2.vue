<script setup lang="ts">
import type { PublicTemplateProps } from './PublicTemplate.vue'
import type { BasicData, JobRiskVO } from '@/api/enterprise'
import { computed } from 'vue'
import VueWordCloud from 'vue-wordcloud'
import PublicTemplate from './PublicTemplate.vue'

const props = defineProps<{
  detail?: BasicData
  jobRisk?: JobRiskVO
  isExpired?: boolean
}>()

interface BenefitWordData {
  name: string
  value: number
}

const benefitsData = computed<BenefitWordData[]>(() => {
  if (props.jobRisk?.benefit && props.jobRisk.benefit.length > 0) {
    return props.jobRisk.benefit.split(/[、,，]\s*/).map(item => ({
      name: item,
      value: 40,
    }))
  }
  else {
    return []
  }
})

// 定义颜色数组
const wordColors = ['#1677FF', '#722ED1', '#13C2C2', '#8C8C8C', '#FF6B35', '#52C41A']

const publicTemplateProps = computed<PublicTemplateProps>(() => {
  const indexB = props.jobRisk?.indexB || 0
  const indexBRank = (props.jobRisk?.indexBRank || 0) * 100
  const indexBRegionRank = (props.jobRisk?.indexBRegionRank || 0) * 100

  return {
    setpTitle: '02 雇主福利信息',
    mainTitle: '薪资吸引力',
    subTitle: '薪资吸引力包含<span style="color: #FF8D1A;">4</span>项指标，重点关注 <span style="color: #FF8D1A;">岗位薪资，公司收入、福利</span>等',
    ratingDescription: '分值说明:薪资吸引能力总分，分值在[0,·100]区间，分值越高，则代表薪资吸引能力越强',
    ringDescribe: `${props.detail?.entname}薪资吸引力能力得分为：${indexB}分，其薪酬增长机制的合理性与现状况相相对较好`,
    ringProgressOptions: [{
      percentage: Number(indexBRank.toFixed(2)),
      fillColor: '#B37FEB',
      color: '#E8D5FF',
      bgColor: '#F5F0FF',
      text: `在全国同行业招聘企业中排名前${indexBRank.toFixed(2)}%`,
    }, {
      percentage: Number(indexBRegionRank.toFixed(2)),
      fillColor: '#36CFC9',
      color: '#B3F5F2',
      bgColor: '#E6FFFB',
      text: `在${props.detail?.province}同行业招聘企业中排名前${indexBRegionRank.toFixed(2)}%`,
    }],
  }
})
</script>

<template>
  <PublicTemplate v-bind="publicTemplateProps">
    <template #header>
      <div class="benefits-section">
        <div class="main-title">
          关键福利展示
        </div>
        <div class="benefits-cloud">
          <VueWordCloud
            v-if="benefitsData.length"
            :data="benefitsData"
            :color="wordColors"
            :show-tooltip="false"
          />
          <el-empty v-else description="暂无数据" image-size="140" />
        </div>
      </div>
    </template>

    <div class="charts-section">
      <div class="clover flex-center">
        <div class="clover-description flex-1 flex-col">
          <div class="flex-1 flex-center" />
          <div class="flex-1 flex-center">
            <div>
              营业总收入
              <div class="gray">（{{ props.jobRisk?.b0103 }}）</div>
            </div>
          </div>
        </div>
        <div class="clover-box">
          <div class="clover-item clover-item-1 flex-center">
            吸引 <br> 能力
          </div>
          <div class="clover-item clover-item-2 flex-center">01</div>
          <div class="clover-item clover-item-3 flex-center">02</div>
          <div class="clover-item clover-item-4 flex-center">03</div>
        </div>
        <div class="clover-description flex-1 flex-col">
          <div class="flex-1 flex-center">
            <div>
              净利润
              <div class="gray">（{{ props.jobRisk?.b0102 }}）</div>
            </div>
          </div>
          <div class="flex-1 flex-center">
            <div>
              福利覆盖
              <div class="gray">（{{ props.jobRisk?.b0201 }}）</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PublicTemplate>
</template>

<style lang="scss" scoped>
.benefits-section {
  margin-bottom: 20px;

  .main-title {
    color: #383838;
    line-height: 1.2;
    font-size: 18px;
    margin-top: 20px;
    font-weight: 500;
  }

  .benefits-cloud {
    position: relative;
    width: 100%;
    height: 200px;
    background: #ffffff;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    margin-top: 16px;
    align-items: center;
    justify-content: center;
  }
}

.charts-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;

  .clover {
    display: flex;
    align-items: center;
    width: 100%;
    height: 200px; // 给父容器设置明确的高度

    .clover-description {
      height: 100%;
    }
      .clover-box {
        width: 170px;
        height: 170px;
        display: flex;
        flex-wrap: wrap;
        .clover-item {
          width: 80px;
          height: 80px;
          color: #ffffff;
          font-size: 18px;
          background-color: #86A8EB;
          &:nth-child(odd) {
            margin-right: 10px;
          }

          &:nth-child(n+3) {
            margin-top: 10px;
          }
        }
        .clover-item-1 {
          border-top-left-radius: 50%;
          border-top-right-radius: 50%;
          border-bottom-left-radius: 50%;
          background-color: #46D1FF;
        }

        .clover-item-2 {
          border-top-left-radius: 50%;
          border-top-right-radius: 50%;
          border-bottom-right-radius: 50%;
        }

        .clover-item-3 {
          border-top-left-radius: 50%;
          border-bottom-left-radius: 50%;
          border-bottom-right-radius: 50%;
        }

        .clover-item-4 {
          border-top-right-radius: 50%;
          border-bottom-left-radius: 50%;
          border-bottom-right-radius: 50%;
        }
      }
    }

  .clover-bottom-description {
    margin-top: 16px;
    font-size: 12px;
    color: #666;
    text-align: center;
    line-height: 1.4;
  }
}
</style>
