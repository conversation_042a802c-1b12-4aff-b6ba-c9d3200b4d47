<script setup lang="ts">
import type { PublicTemplateProps } from './PublicTemplate.vue'
import type { BasicData, JobRiskVO } from '@/api/enterprise'
import { computed } from 'vue'
import PublicTemplate from './PublicTemplate.vue'

const props = defineProps<{
  detail?: BasicData
  jobRisk?: JobRiskVO
}>()

const publicTemplateProps = computed<PublicTemplateProps>(() => {
  const indexA = props.jobRisk?.indexA || 0
  const indexARank = (props.jobRisk?.indexARank || 0) * 100
  const indexARegionRank = (props.jobRisk?.indexARegionRank || 0) * 100

  return {
    setpTitle: '03 雇主负面信息',
    mainTitle: '雇主负面信息',
    subTitle: '雇主负面信息包含7项指标：重点关注<span style="color: #FF8D1A;">劳动争议、负面舆情、社保缴纳</span>等信息',
    ratingDescription: '分值说明:企业负面信息总分，分值在[0，·100]区间，分值越高，则代表企业的负面信息越少',
    ringDescribe: `${props.detail?.entname}企业负面信息维度得分为 ${indexA} 分，其负面事件或不良记录相对较${indexA > 50 ? '少' : '多'}`,
    ringProgressOptions: [{
      percentage: Number(indexARank.toFixed(2)),
      fillColor: '#B37FEB',
      color: '#E8D5FF',
      bgColor: '#F5F0FF',
      text: `在全国同行业招聘企业中排名前${indexARank.toFixed(2)}%`,
    }, {
      percentage: Number(indexARegionRank.toFixed(2)),
      fillColor: '#36CFC9',
      color: '#B3F5F2',
      bgColor: '#E6FFFB',
      text: `在${props.detail?.province}同行业招聘企业中排名前${indexARegionRank.toFixed(2)}%`,
    }],
  }
})
</script>

<template>
  <PublicTemplate v-bind="publicTemplateProps">
    <!-- 六边形图表区域 - 移动端适配 -->
    <div class="hexagon-image-container">
      <div class="image-text image-text-1">
        负面舆情
        <div class="gray">（{{ props.jobRisk?.a0501 }}）</div>
      </div>
      <div class="image-text image-text-2">
        劳动争议案件
        <div class="gray">（{{ props.jobRisk?.a0101 }}）</div>
      </div>
      <div class="image-text image-text-3">
        社保缴纳合规率
        <div class="gray">（{{ props.jobRisk?.a0102 }}）</div>
      </div>
      <img src="@/assets/images/pc/negative-information.png" alt="负面信息六边形图表">
    </div>
  </PublicTemplate>
</template>

<style lang="scss" scoped>
.hexagon-image-container {
  margin-bottom: 16px;
  position: relative;
  display: flex;
  justify-content: center;

  img {
    display: block;
    width: 100%;
    max-width: 300px;
    height: auto;
  }

  .image-text {
    position: absolute;
    font-size: 12px;
    color: #333;
    text-align: center;
    line-height: 1.2;
    font-weight: 500;
  }

  .image-text-1 {
    top: 15%;
    left: 57%;
    transform: translateX(-50%);
  }

  .image-text-2 {
    top: 60%;
    left: 38%;
    transform: translateX(-50%);
  }

  .image-text-3 {
    top: 60%;
    right: 15%;
    transform: translateX(50%);
  }

  .gray {
    color: #A6A6A6;
  }
}
</style>
