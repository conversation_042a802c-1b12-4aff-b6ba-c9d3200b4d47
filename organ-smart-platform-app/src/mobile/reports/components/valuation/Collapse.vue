<script setup lang="ts">
defineProps<{
  title: string
}>()

const isExpand = ref(!false)

function handleChangeExpand() {
  isExpand.value = !isExpand.value
}
</script>

<template>
  <div class="collapse">
    <div class="collapse-title" @click="handleChangeExpand">
      <div class="title">
        {{ title }}
      </div>
      <div class="icon">
        {{ isExpand ? '收起' : '展开' }}
        <i v-if="isExpand" class="el-icon-arrow-up" />
        <i v-else class="el-icon-arrow-right" />
      </div>
    </div>
    <div v-show="isExpand" class="collapse-slot">
      <slot />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.collapse {
  background-color: #ffffff;
  .collapse-title {
    padding: 10px 20px;
    color: #1677FF;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .title {
      font-weight: 500;
      font-size: 17px;
    }
    .icon {
      font-size: 15px;
    }
  }
  .collapse-slot {
    padding: 10px 20px 30px;
  }
}
</style>
