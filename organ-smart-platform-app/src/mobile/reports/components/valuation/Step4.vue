<script setup lang="ts">
import type { PublicTemplateProps } from './PublicTemplate.vue'
import type { BasicData, JobRiskVO } from '@/api/enterprise'
import * as echarts from 'echarts'
import { computed, onMounted, ref, watch } from 'vue'
import Collapse from './Collapse.vue'

const props = defineProps<{
  detail?: BasicData
  jobRisk?: JobRiskVO
}>()

const publicTemplateProps = computed<PublicTemplateProps>(() => {
  const indexC = props.jobRisk?.indexC || 0
  const indexCRank = (props.jobRisk?.indexCRank || 0) * 100
  const indexCRegionRank = (props.jobRisk?.indexCRegionRank || 0) * 100

  return {
    setpTitle: '04 雇主发展力',
    mainTitle: '技术成长潜力',
    subTitle: '技术成长潜力包含9项指标，重点关注<span style="color: #FF8D1A;">专利权利人、专利授权、软件著作权</span>',
    ratingDescription: '分值说明:技术成长潜力总分，分值在[0,·100]区间，分值越高，则代表技术成长潜力越大',
    ringDescribe: `${props.detail?.entname}技术成长潜力维度得分为 ${indexC} 分，其员工技术技能提升和职业发展的机会相对较${indexC > 50 ? '好' : '差'}。`,
    ringProgressOptions: [{
      percentage: Number(indexCRank.toFixed(2)),
      fillColor: '#B37FEB',
      color: '#E8D5FF',
      bgColor: '#F5F0FF',
      text: `在全国同行业招聘企业中排名前${indexCRank.toFixed(2)}%`,
    }, {
      percentage: Number(indexCRegionRank.toFixed(2)),
      fillColor: '#36CFC9',
      color: '#B3F5F2',
      bgColor: '#E6FFFB',
      text: `在${props.detail?.province}同行业招聘企业中排名前${indexCRegionRank.toFixed(2)}%`,
    }],
  }
})

const publicTemplateProps2 = computed<PublicTemplateProps>(() => {
  const indexD = props.jobRisk?.indexD || 0
  const indexDRank = (props.jobRisk?.indexDRank || 0) * 100
  const indexDRegionRank = (props.jobRisk?.indexDRegionRank || 0) * 100

  return {
    mainTitle: '行业竞争能力',
    subTitle: '行业竞争力包含5项指标，重点关注<span style="color: #FF8D1A;">市场占有率、负债增长、企业扩张能力</span>',
    ratingDescription: '分值说明:行业竞争能力总分，分值在[0,·100]区间，分值越高，则代表企业的竞争能力越强',
    ringDescribe: `${props.detail?.entname}行业竞争能力维度得分为 ${indexD} 分，其在行业中的竞争地位相对较${indexD > 50 ? '好' : '差'}`,
    ringProgressOptions: [{
      percentage: Number(indexDRank.toFixed(2)),
      fillColor: '#B37FEB',
      color: '#E8D5FF',
      bgColor: '#F5F0FF',
      text: `在全国同行业招聘企业中排名前${indexDRank.toFixed(2)}%`,
    }, {
      percentage: Number(indexDRegionRank.toFixed(2)),
      fillColor: '#36CFC9',
      color: '#B3F5F2',
      bgColor: '#E6FFFB',
      text: `在${props.detail?.province}同行业招聘企业中排名前${indexDRegionRank.toFixed(2)}%`,
    }],
  }
})

const publicTemplateProps3 = computed<PublicTemplateProps>(() => {
  const indexE = props.jobRisk?.indexE || 0
  const indexERank = (props.jobRisk?.indexERank || 0) * 100
  const indexERegionRank = (props.jobRisk?.indexERegionRank || 0) * 100

  return {
    mainTitle: '职业发展前景',
    subTitle: '职业发展力包含6项指标，重点关注<span style="color: #FF8D1A;">行业营收增长、政策支持强度、岗位招聘人数</span>',
    ratingDescription: '分值说明:职业发展前景总分，分值在[0，·100]区间，分值越高，该企业的职业发展前景越好',
    ringDescribe: `${props.detail?.entname}职业发展前景维度得分为 ${indexE} 分，其职业发展前景的广阔性和丰富的晋升机会相对较${indexE > 50 ? '好' : '差'}`,
    ringProgressOptions: [{
      percentage: Number(indexERank.toFixed(2)),
      fillColor: '#B37FEB',
      color: '#E8D5FF',
      bgColor: '#F5F0FF',
      text: `在全国同行业招聘企业中排名前${indexERank.toFixed(2)}%`,
    }, {
      percentage: Number(indexERegionRank.toFixed(2)),
      fillColor: '#36CFC9',
      color: '#B3F5F2',
      bgColor: '#E6FFFB',
      text: `在${props.detail?.province}同行业招聘企业中排名前${indexERegionRank.toFixed(2)}%`,
    }],
  }
})

// ECharts 相关
const RegionChartRef = ref<FixedLengthTuple<HTMLDivElement, 6>>()
const chartInstances = ref<echarts.ECharts[]>([])

// 渲染图表的函数
function renderCharts() {
  if (!RegionChartRef.value)
    return

  RegionChartRef.value.forEach((item, index) => {
    if (!chartInstances.value[index]) {
      chartInstances.value[index] = echarts.init(item)
    }

    // 根据索引确定使用哪个配置
    let config: any
    if (index < 2) {
      config = publicTemplateProps.value.ringProgressOptions[index]
    }
    else if (index < 4) {
      config = publicTemplateProps2.value.ringProgressOptions[index - 2]
    }
    else {
      config = publicTemplateProps3.value.ringProgressOptions[index - 4]
    }

    if (config) {
      chartInstances.value[index].setOption(createRingProgressOption(config.percentage, config.fillColor, config.color))
    }
  })
}

// 监听数据变化
watch([() => publicTemplateProps.value, () => publicTemplateProps2.value, () => publicTemplateProps3.value], () => {
  renderCharts()
}, { deep: true })

onMounted(() => {
  renderCharts()
})

// 创建圆形进度条的配置
function createRingProgressOption(percentage: number, color: string, bgColor: string) {
  return {
    series: [
      {
        type: 'gauge',
        startAngle: 90,
        endAngle: 450,
        clockwise: false,
        pointer: {
          show: false,
        },
        progress: {
          show: true,
          overlap: false,
          roundCap: true,
          clip: false,
          itemStyle: {
            borderWidth: 0,
            color,
          },
        },
        axisLine: {
          lineStyle: {
            width: 8,
            color: [[1, bgColor]],
          },
        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
        data: [
          {
            value: percentage,
            name: '',
            title: {
              show: false,
            },
            detail: {
              valueAnimation: true,
              fontSize: 16,
              fontWeight: 'bold',
              color: '#333',
              formatter: '{value}%',
              offsetCenter: [0, 0],
            },
          },
        ],
        radius: '80%',
        center: ['50%', '50%'],
      },
    ],
  }
}
</script>

<template>
  <Collapse title="04 雇主发展力">
    <!-- 技术成长潜力 -->
    <div class="section-container">
      <div class="main-title">技术成长潜力</div>
      <div class="sub-title" v-html="publicTemplateProps.subTitle" />

      <div class="setp-content">
        <div class="setp-content-left">
          <div class="content-slot">
            <div class="hexagon-image-container">
              <div class="image-text image-text-1">
                软件著作权
                <div class="gray">（{{ props.jobRisk?.c0202 }}）</div>
              </div>
              <div class="image-text image-text-2">
                专利权人
                <div class="gray">（{{ props.jobRisk?.c0101 }}）</div>
              </div>
              <div class="image-text image-text-3">
                作品著作权
                <div class="gray">（{{ props.jobRisk?.c0203 }}）</div>
              </div>
              <img src="@/assets/images/pc/development-power.png" alt="技术成长潜力">
            </div>
          </div>
          <div class="rating-description">
            {{ publicTemplateProps.ratingDescription }}
          </div>
        </div>

        <div class="setp-content-right">
          <div class="ring-describe">
            {{ publicTemplateProps.ringDescribe }}
          </div>

          <div class="echarts-container">
            <div
              v-for="(item, index) in publicTemplateProps.ringProgressOptions"
              :key="index"
              class="echarts-ring"
              :style="{ backgroundColor: item.bgColor }"
            >
              <div ref="RegionChartRef" class="chart-container" />
              <div class="echarts-ring-text">{{ item.text }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 行业竞争能力 -->
    <div class="section-container">
      <div class="main-title">行业竞争能力</div>
      <div class="sub-title" v-html="publicTemplateProps2.subTitle" />

      <div class="setp-content">
        <div class="setp-content-left">
          <div class="content-slot">
            <div class="hexagon-image-container">
              <div class="image-text image-text-1-2">
                市场占有率
                <div class="gray">（{{ props.jobRisk?.d0101 }}）</div>
              </div>
              <div class="image-text image-text-2-2">
                企业扩张能力
                <div class="gray">（{{ props.jobRisk?.d0102 }}）</div>
              </div>
              <div class="image-text image-text-3-2">
                负债总额增长率
                <div class="gray">（{{ props.jobRisk?.d0202 }}）</div>
              </div>
              <img src="@/assets/images/pc/industry-competitiveness.png" alt="行业竞争能力">
            </div>
          </div>
          <div class="rating-description">
            {{ publicTemplateProps2.ratingDescription }}
          </div>
        </div>

        <div class="setp-content-right">
          <div class="ring-describe">
            {{ publicTemplateProps2.ringDescribe }}
          </div>

          <div class="echarts-container">
            <div
              v-for="(item, index) in publicTemplateProps2.ringProgressOptions"
              :key="index"
              class="echarts-ring"
              :style="{ backgroundColor: item.bgColor }"
            >
              <div ref="RegionChartRef" class="chart-container" />
              <div class="echarts-ring-text">{{ item.text }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 职业发展前景 -->
    <div class="section-container">
      <div class="main-title">职业发展前景</div>
      <div class="sub-title" v-html="publicTemplateProps3.subTitle" />

      <div class="setp-content">
        <div class="setp-content-left">
          <div class="content-slot">
            <div class="hexagon-image-container">
              <div class="image-text image-text-1-3">
                行业营收增长率
                <div class="gray">（{{ props.jobRisk?.e0102 }}）</div>
              </div>
              <div class="image-text image-text-2-3">
                政策支持强度
                <div class="gray">（{{ props.jobRisk?.e0103 }}）</div>
              </div>
              <div class="image-text image-text-3-3">
                岗位招聘人数
                <div class="gray">（{{ props.jobRisk?.e0201 }}）</div>
              </div>
              <img src="@/assets/images/pc/career-development-prospects.png" alt="职业发展前景">
            </div>
          </div>
          <div class="rating-description">
            {{ publicTemplateProps3.ratingDescription }}
          </div>
        </div>

        <div class="setp-content-right">
          <div class="ring-describe">
            {{ publicTemplateProps3.ringDescribe }}
          </div>

          <div class="echarts-container">
            <div
              v-for="(item, index) in publicTemplateProps3.ringProgressOptions"
              :key="index"
              class="echarts-ring"
              :style="{ backgroundColor: item.bgColor }"
            >
              <div ref="RegionChartRef" class="chart-container" />
              <div class="echarts-ring-text">{{ item.text }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Collapse>
</template>

<style lang="scss" scoped>
.section-container {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

.main-title {
  color: #383838;
  line-height: 1.2;
  font-size: 18px;
  margin-top: 20px;
  font-weight: 500;
}

.sub-title {
  color: #383838;
  line-height: 1.4;
  margin-top: 16px;
  font-size: 14px;
}

.setp-content {
  margin-top: 20px;
  padding: 16px;
  background-color: #F6F9FF;
  border-radius: 8px;

  .setp-content-left {
    margin-bottom: 20px;

    .content-slot {
      height: 180px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 16px;
    }

    .rating-description {
      color: #837F81;
      font-size: 14px;
      line-height: 1.5;
    }
  }

  .setp-content-right {
    background-color: #ffffff;
    border-radius: 8px;
    padding: 16px;

    .ring-describe {
      font-size: 16px;
      line-height: 1.6;
      margin-bottom: 20px;
      text-align: center;
      color: #333;
    }

    .echarts-container {
      display: flex;
      flex-direction: row;
      gap: 16px;
      justify-content: space-around;

      .echarts-ring {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px;
        border-radius: 8px;
        flex: 1;
        max-width: 180px;

        .chart-container {
          width: 120px;
          height: 120px;
        }

        .echarts-ring-text {
          font-size: 12px;
          color: #666;
          font-weight: 400;
          margin-top: 12px;
          text-align: center;
          line-height: 1.4;
        }
      }
    }
  }

  // 在小屏幕设备上使用垂直布局
  @media (max-width: 360px) {
    .echarts-container {
      flex-direction: column;

      .echarts-ring {
        max-width: none;
        width: 100%;
      }
    }
  }
}

.hexagon-image-container {
  margin-bottom: 16px;
  position: relative;
  display: flex;
  justify-content: center;

  img {
    display: block;
    width: 100%;
    max-width: 300px;
    height: auto;
  }

  .image-text {
    position: absolute;
    font-size: 11px;
    color: #333;
    text-align: center;
    line-height: 1.2;
    font-weight: 500;
  }

  // 技术成长潜力的文字位置
  .image-text-1 {
    top: 8%;
    left: 59%;
    transform: translateX(-50%);
  }

  .image-text-2 {
    top: 76%;
    left: 39%;
    transform: translateX(-50%);
  }

  .image-text-3 {
    top: 76%;
    right: 21.5%;
    transform: translateX(50%);
  }

  // 行业竞争能力的文字位置
  .image-text-1-2 {
    top: -20%;
    left: 40%;
    transform: translateX(-50%);
  }

  .image-text-2-2 {
    top: 90%;
    left: 55%;
    transform: translateX(-50%);
  }

  .image-text-3-2 {
    top: -20%;
    right: 26%;
    transform: translateX(50%);
  }

  // 职业发展前景的文字位置
  .image-text-1-3 {
    top: -22%;
    left: 45%;
    transform: translateX(-50%);
  }

  .image-text-2-3 {
    top: 95%;
    left: 62%;
    transform: translateX(-50%);
  }

  .image-text-3-3 {
    top: -22%;
    right: 14%;
    transform: translateX(50%);
  }
}

// 在较大的移动设备上调整布局
@media (min-width: 480px) {
  .hexagon-image-container {
    img {
      max-width: 350px;
    }

    .image-text {
      font-size: 12px;
    }
  }

  .gray {
    color: #A6A6A6;
  }
}
</style>
