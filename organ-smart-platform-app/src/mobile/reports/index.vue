<script setup lang="ts">
import { Carousel, CarouselItem, Link, Scrollbar } from 'element-ui'
import { computed, ref } from 'vue'
import { getBnScore, getBnScoreJobRisk, getEnterpriseDetail } from '@/api/enterprise'
import { getUserInfo } from '@/api/user'
import FeedbackDialog from '@/components/FeedbackDialog/index.vue'
import VipPackageDialog from '@/components/VipPackageDialog/index.vue'
import { useRequest } from '@/hooks/useRequest'
import { getFullStaticUrl } from '@/utils/string'
import Score from './components/score/index.vue'
import Valuation from './components/valuation/index.vue'

const props = withDefaults(defineProps<{
  /** 企业的统代 */
  uniscid: string
  keyType?: string
}>(), {
  keyType: '2',
})

const enterpriseParams = {
  key: props.uniscid,
  keyType: props.keyType,
}

const { data: user, refreshAsync: refreshUser } = useRequest(getUserInfo, {
  defaultParams: ['1'],
})

/** 企业图标 */
const enterpriseIcon = getFullStaticUrl('zhjmsqPackage/icon_jmqy02.png')

/** 企业基本信息 */
const { data: detail } = useRequest(getEnterpriseDetail, {
  defaultParams: [enterpriseParams],
})

// 获取企业评分
const { data: score } = useRequest(getBnScore, {
  defaultParams: [enterpriseParams],
})

/** 企业招聘风险 */
const { data: jobRisk } = useRequest(getBnScoreJobRisk, {
  defaultParams: [enterpriseParams],
})

/** 当前分数 */
const currentScore = computed(() => score.value?.indexTotal || 0)

/** 计算背景遮罩宽度（剩余部分） */
const backgroundMaskWidth = computed(() => {
  return `${100 - (currentScore.value / 100) * 100}%`
})

const reportModel = ref<'valuation' | 'score'>('valuation')

function handleChangePreviewModel() {
  reportModel.value = reportModel.value === 'valuation' ? 'score' : 'valuation'
}

const currentReportModel = computed(() => {
  if (reportModel.value === 'score') {
    return {
      title: '企业综合实力评分报告',
      changeTitle: '雇主价值评估报告',
    }
  }
  else if (reportModel.value === 'valuation') {
    return {
      title: '雇主价值评估报告',
      changeTitle: '企业综合实力评分报告',
    }
  }
  else {
    return {}
  }
})

const reportsSamples = [
  {
    id: 1,
    title: '企业综合实力评分',
    subtitle: '了解企业信用，招聘更可靠',
    linkText: '查看企业信用报告样例',
    bg: require('@/assets/images/pc/reports-bg1.png'),
    bgColor: 'linear-gradient(180deg, rgba(218, 236, 255, 1) 0%, rgba(255, 255, 255, 0) 100%)',
    desc: [
      {
        title: '了解企业良好信誉',
        subtitle: '降低招聘后风险',
      },
      {
        title: '全面掌握企业风险因素',
        subtitle: '评估自己职业潜力',
      },
      {
        title: '提升自身的职业发展和',
        subtitle: '获取资源渠道',
      },
    ],
    reportContents: [
      {
        id: '01',
        title: '企业综合评分',
        description: '了解企业综合评分',
      },
      {
        id: '02',
        title: '基础信息',
        description: '了解企业基础信息',
      },
      {
        id: '03',
        title: '经营信息',
        description: '了解企业经营信息',
      },
      {
        id: '04',
        title: '经营风险',
        description: '了解企业经营风险',
      },
      {
        id: '05',
        title: '司法信息',
        description: '了解企业司法信息',
      },
      {
        id: '06',
        title: '知识产权',
        description: '了解企业知识产权',
      },
    ],
  },
  {
    id: 2,
    title: '雇主价值评估报告',
    subtitle: '助力求职者深度企业综合情况',
    linkText: '查看雇主价值评估报告样例',
    bg: require('@/assets/images/pc/reports-bg2.png'),
    bgColor: 'linear-gradient(180deg, rgba(231, 247, 251, 1) 0%, rgba(255, 255, 255, 1) 100%)',
    desc: [
      {
        title: '多维穿透雇主风险',
        subtitle: '关键决策零盲区',
      },
      {
        title: '绑定人才战略与业务增长',
        subtitle: '投资价值可视化',
      },
      {
        title: '量化福利竞争力',
        subtitle: '人才留存有据可依',
      },
    ],
    reportContents: [
      {
        id: '01',
        title: '雇主基本情况',
        description: '了解雇主基本情况',
      },
      {
        id: '02',
        title: '雇主福利信息',
        description: '了解雇主福利信息',
      },
      {
        id: '03',
        title: '雇主负面信息',
        description: '了解雇主负面信息',
      },
      {
        id: '04',
        title: '雇主发展力',
        description: '了解雇主发展力',
      },
    ],
  },
]

// VIP弹窗相关状态
const vipDialogVisible = ref(false)

// 意见反馈弹窗状态
const feedbackDialogVisible = ref(false)

// 显示VIP购买弹窗
function showVipDialog() {
  vipDialogVisible.value = true
}

// 显示意见反馈弹窗
function showFeedbackDialog() {
  feedbackDialogVisible.value = true
}

// 处理意见反馈成功
function handleFeedbackSuccess() {
  console.log('意见反馈提交成功')
}

// 处理支付成功
function handlePaymentSuccess(orderInfo: any) {
  console.log('VIP支付成功:', orderInfo)

  try {
    // 重新请求用户信息
    refreshUser()
    console.log('用户信息刷新已触发')
  }
  catch (error) {
    console.error('刷新用户信息失败:', error)
  }
}
</script>

<template>
  <div class="size-full mobile-reports-page flex-col">
    <div class="flex-1 overflow-hidden">
      <Scrollbar style="height: 100%;">
        <div class="mobile-enterprise-info">
          <div class="mobile-enterprise-basic">
            <img class="mobile-enterprise-icon" :src="enterpriseIcon" alt="企业图标">
            <div class="mobile-enterprise-details flex flex-align-center">
              <div class="mobile-company-name">{{ detail?.entname }}</div>
            </div>
          </div>
          <div class="link">
            <Link type="primary" @click="handleChangePreviewModel">在线预览{{ currentReportModel.changeTitle }} <i class="el-icon-arrow-right" /> </Link>
          </div>
        </div>
        <!-- 信用评分 -->
        <div class="mobile-credit-score">
          <div class="mobile-score-title">企业评分</div>

          <div class="mobile-score-bar-container">
            <div class="mobile-score-bar">
              <div class="mobile-score-background" />
              <div class="mobile-score-background-mask" :style="{ width: backgroundMaskWidth }" />
            </div>
            <div class="mobile-score-value">{{ currentScore }}分</div>
          </div>

          <div class="mobile-score-description">
            <p class="mobile-score-desc-title">企业评分：</p>
            <p class="mobile-score-desc-text">企业评分：海量的企业公开信息，通过大数据技术与算法模型对企业的综合实力进行定性评分产品，为企业提供精准、主观的评分。</p>
          </div>
        </div>

        <div class="mobile-common-header flex-center">
          {{ currentReportModel.title }}
        </div>

        <!-- 雇主价值评估报告 -->
        <Valuation
          v-if="reportModel === 'valuation'" :detail="detail" :job-risk="jobRisk"
          :is-expired="user?.isExpired"
        />

        <!-- 企业综合实力评分报告 -->
        <Score v-else-if="reportModel === 'score'" :detail="detail" :score="score" />
        <div v-if="user?.isExpired" class="disclaimers">
          免责声明：<br>
          本报告系由检测、数据分析和判断最终形成，我⽅已尽最⼤努⼒确保本报告内容的准确。<br>
          对评估结果的使⽤，须⾃⾏承担⻛险，我⽅不承担任何法律责任，特此声明！
        </div>
        <div v-else class="not-login-tips flex-col flex-center">
          <div class="button flex-center" @click="showVipDialog">查看更多信息</div>
          <div class="text flex-center">
            更多企业征信信息，开通国聘  <i class="el-icon-close" />   海南征信产品
          </div>
        </div>
        <!-- 报告样例 -->
        <Carousel trigger="click" height="650px" arrow="never">
          <CarouselItem v-for="item in reportsSamples" :key="item.id">
            <div
              class="reports-sample-item" :style="{
                background: item.bgColor,
              }"
            >
              <div class="title flex-center">
                {{ item.title }}
              </div>
              <div class="sub-title flex-center">
                {{ item.subtitle }}
              </div>
              <div class="sample-desc flex">
                <img :src="item.bg" alt="">
                <div class="desc-title flex-1 flex-col">
                  <div v-for="desc in item.desc" :key="desc.title" class="desc-title-item">
                    {{ desc.title }},{{ desc.subtitle }}
                  </div>
                </div>
              </div>
              <div class="sample-bottom">
                <div class="title">报告内容</div>
                <div class="content-list flex">
                  <div v-for="i in item.reportContents" :key="i.id" class="content-item">
                    <div class="content-title">{{ i.id }} {{ i.title }}</div>
                    <div class="content-desc">{{ i.description }}</div>
                  </div>
                </div>
              </div>
            </div>
          </CarouselItem>
        </Carousel>

        <!-- 联系信息 -->
        <div class="mobile-contact-info">
          <p class="mobile-contact-text">如有问题，点击<span class="mobile-feedback-link" @click="showFeedbackDialog">意见反馈</span>，或联系0898-68531162</p>
        </div>
      </Scrollbar>
    </div>

    <!-- VIP套餐弹窗 -->
    <VipPackageDialog
      :visible="vipDialogVisible"
      @update:visible="vipDialogVisible = $event"
      @payment-success="handlePaymentSuccess"
    />

    <!-- 意见反馈弹窗 -->
    <FeedbackDialog
      :visible="feedbackDialogVisible"
      @update:visible="feedbackDialogVisible = $event"
      @success="handleFeedbackSuccess"
    />
  </div>
</template>

<style scoped lang="scss">
// 移动端报告页面基础样式
.mobile-reports-page {
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
  overflow: hidden;
}

// 企业信息区域
.mobile-enterprise-info {
  padding: 30px 20px 10px;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 100% auto;
  background-image: url('@/assets/images/mobile/mobile-enterprise-bg.png');

  .link {
    display: flex;
    justify-content: flex-end;
    width: 100%;
    margin-top: 20px;
  }
}

// 企业基本信息
.mobile-enterprise-basic {
  display: flex;
  align-items: center;

  .mobile-enterprise-icon {
    width: 42px;
    height: 42px;
    margin-right: 12px;
    border-radius: 8px;
  }

  .mobile-enterprise-details {
    flex: 1;

    .mobile-company-name {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      line-height: 1;
    }
  }
}

// 信用评分
.mobile-credit-score {
  padding: 14px 20px;
  background-color: #ffffff;
  .mobile-score-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    text-align: left;
  }

  .mobile-score-bar-container {
    display: flex;
    align-items: center;
    margin: 6px 0;
    .mobile-score-bar {
      flex: 1;
      height: 10px;
      border-radius: 6px;
      position: relative;
      background-color: #e5e5e5;
      overflow: hidden;

      .mobile-score-background {
        position: absolute;
        left: 0;
        right: 0;
        height: 100%;
        background: linear-gradient(90deg, #1677ff 0%, #fe9a36 49.13%, #dd0000 100%);
      }

      .mobile-score-background-mask {
        position: absolute;
        top: 0;
        right: 0;
        height: 100%;
        background: #e5e5e5;
      }
    }

    .mobile-score-value {
      font-size: 20px;
      color: #dd0000;
      white-space: nowrap;
      display: flex;
      align-items: center;
      margin-left: 20px;
    }
  }

  .mobile-score-description {
    text-align: left;

    .mobile-score-desc-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin: 0 0 8px 0;
    }

    .mobile-score-desc-text {
      font-size: 14px;
      color: #808080;
      line-height: 1.4;
      margin: 0;
    }
  }
}

.mobile-common-header {
  margin-top: 6px;
  padding: 20px 0;
  color: #333333;
  font-size: 19px;
  font-weight: 500;
  background-color: #ffffff;
}

.disclaimers {
  color: #A6A6A6;
  padding: 20px 40px 40px;
  background-color: #ffffff;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}

.not-login-tips {
  padding: 20px 40px 40px;
  background-color: #ffffff;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  .button {
    width: 160px;
    height: 60px;
    color: #ffffff;
    border-radius: 6px;
    cursor: pointer;
    background-color: #1677FF;
  }
  .text {
    margin-top: 30px;
    line-height: 1;
  }
}

.reports-sample-item {
  width: 100%;
  height: 100%;
  padding: 20px 10px;
  .title {
    color: #333333;
    font-size: 22px;
    font-weight: 500;
  }
  .sub-title {
    font-size: 14px;
  }
  .sample-desc {
    margin-top: 20px;
    align-items: center;
    img {
      height: 160px;
    }
    .desc-title {
      justify-content: center;
      font-size: 14px;
      .desc-title-item {
        width: fit-content;
        padding: 6px 8px;
        border-radius: 10px;
        color: #1677FF;
        background-color: #FFFFFF;
        border: 1px solid #53B8D3;
        +.desc-title-item {
          margin-top: 10px;
        }
      }
    }
  }
  .sample-bottom {
    margin-top: 20px;
    .title {
      font-size: 22px;
      color: #222222;
      text-align: center;
    }
    .content-list {
      flex-wrap: wrap;
      margin-top: 20px;
      justify-content: space-between;
      .content-item {
        border: 1px solid #E5E8EB;
        border-radius: 8px;
        padding: 12px 10px;
        flex: 0 0 calc(50% - 6px);
        box-sizing: border-box;
        transition: all 0.2s ease;
        cursor: pointer;
        background: linear-gradient(180deg, rgba(226, 237, 255, 1) 0%, #ffffff 100%);
        border: 1px solid rgba(255, 255, 255, 1);
        box-shadow: 5px 5px 20px rgba(52, 110, 191, 0.3);
        &:nth-child(even) {
          margin-left: 12px;
        }
        &:nth-child(n+3) {
          margin-top: 20px;
        }
        .content-title {
          font-size: 18px;
          margin-left: 1em;
          font-weight: 500;
        }
        .content-desc {
          color: #A4A4A4;
          font-size: 14px;
          margin-left: 44px;
        }
      }
    }
  }

}

.mobile-contact-info {
    text-align: center;
    margin-top: 14px;
    padding: 20px 0;
    background-color: #ffffff;
    .mobile-contact-text {
      font-size: 14px;
      color: #666;
      margin: 0;
      line-height: 1.5;
      .mobile-feedback-link {
        color: #1677ff;
        text-decoration: none;
        cursor: pointer;
      }
    }
  }
</style>
