<script setup lang="ts">
import type { BasicData, BnScoreVO } from '@/api/enterprise'
// 评分报告
import * as echarts from 'echarts'
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'

import ComparisonTable from '@/components/ComparisonTable/index.vue'

const props = defineProps<{
  detail?: BasicData
  score?: BnScoreVO
}>()

// 雷达图相关
const radarChartRef = ref<HTMLDivElement>()
let radarChart: echarts.ECharts | null = null

// 企业综合评分雷达图数据
const scoreRadarData = computed(() => ({
  totalScore: props.score?.indexTotal || 0,
  radarData: [
    { name: '经营绩效', value: props.score?.indexB || 0 },
    { name: '经营稳定', value: props.score?.indexC || 0 },
    { name: '创新能力', value: props.score?.indexD || 0 },
    { name: '负面风险', value: props.score?.indexE || 0 },
    { name: '行业地位', value: props.score?.indexA || 0 },
  ],
}))

watch(() => scoreRadarData.value, () => {
  renderCharts()
})

function renderCharts() {
  if (!radarChartRef.value)
    return
  if (!radarChart) {
    radarChart = echarts.init(radarChartRef.value)
  }

  const option = {
    backgroundColor: 'transparent',
    radar: {
      indicator: scoreRadarData.value.radarData.map(item => ({
        name: item.name,
        min: 0,
        max: 100,
        nameGap: 10,
      })),
      center: ['50%', '50%'],
      radius: '65%',
      startAngle: 90,
      splitNumber: 5,
      shape: 'polygon',
      axisName: {
        formatter(name: string) {
          const data = scoreRadarData.value.radarData.find(item => item.name === name)
          return `{name|${name}}\n{value|(${data?.value || 0})}`
        },
        rich: {
          name: {
            fontSize: 12,
            color: '#666',
            lineHeight: 16,
          },
          value: {
            fontSize: 11,
            color: '#999',
            lineHeight: 14,
          },
        },
        fontSize: 12,
        color: '#666',
      },
      splitArea: {
        areaStyle: {
          color: ['rgba(114, 172, 209, 0.05)', 'rgba(114, 172, 209, 0.1)', 'rgba(114, 172, 209, 0.15)', 'rgba(114, 172, 209, 0.2)'],
        },
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(114, 172, 209, 0.3)',
          width: 1,
        },
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(114, 172, 209, 0.3)',
          width: 1,
        },
      },
    },
    series: [
      {
        type: 'radar',
        data: [
          {
            value: scoreRadarData.value.radarData.map(item => item.value),
            areaStyle: {
              color: 'rgba(114, 172, 209, 0.3)',
            },
            lineStyle: {
              color: '#72ACD1',
              width: 2,
            },
            itemStyle: {
              color: '#72ACD1',
              borderColor: '#72ACD1',
              borderWidth: 2,
            },
            symbol: 'circle',
            symbolSize: 6,
          },
        ],
      },
    ],
  }

  radarChart.setOption(option)
}

// 组件挂载后初始化图表
onMounted(() => {
  nextTick(() => {
    renderCharts()
  })
})

// 组件卸载时销毁图表
onUnmounted(() => {
  if (radarChart) {
    radarChart.dispose()
    radarChart = null
  }
})

const regions = computed(() => ([
  { key: 'country', label: '全国' },
  { key: 'province', label: props.detail?.province },
  { key: 'city', label: props.detail?.city },
]))

const tableData = computed(() => {
  const detail = props.score?.detailsA
  return [
    { name: '注册资本', country: detail?.index_a_1, province: detail?.index_a_2, city: detail?.index_a_3 },
    { name: '实缴资本', country: detail?.index_a_4, province: detail?.index_a_5, city: detail?.index_a_6 },
    { name: '所有者权益', country: detail?.index_a_7, province: detail?.index_a_8, city: detail?.index_a_9 },
    { name: '总资产', country: detail?.index_a_10, province: detail?.index_a_11, city: detail?.index_a_12 },
    { name: '营业收入', country: detail?.index_a_13, province: detail?.index_a_14, city: detail?.index_a_15 },
    { name: '净利润', country: detail?.index_a_16, province: detail?.index_a_17, city: detail?.index_a_18 },
  ]
})

const scoreConfigList = computed(() => {
  const detailB = props.score?.detailsB
  const detailC = props.score?.detailsC
  const detailD = props.score?.detailsD
  const detailE = props.score?.detailsE

  return [
    {
      title: '经营绩效',
      score: props.score?.indexB || 0,
      subTitle: '经营绩效：基于“营业利润率”、“净利润”、“净资产受益率”等指标在全国、福建省、福州市得分综合计算得出。',
      tableData: [
        { name: '营业利润率', country: detailB?.index_b_1, province: detailB?.index_b_2, city: detailB?.index_b_3 },
        { name: '净利润率', country: detailB?.index_b_4, province: detailB?.index_b_5, city: detailB?.index_b_6 },
        { name: '净资产收益率', country: detailB?.index_b_7, province: detailB?.index_b_8, city: detailB?.index_b_9 },
        { name: '资产负债率', country: detailB?.index_b_10, province: detailB?.index_b_11, city: detailB?.index_b_12 },
        { name: '所有者权益比率', country: detailB?.index_b_13, province: detailB?.index_b_14, city: detailB?.index_b_15 },
        { name: '总资产周转率', country: detailB?.index_b_16, province: detailB?.index_b_17, city: detailB?.index_b_18 },
        { name: '净资产周转率', country: detailB?.index_b_19, province: detailB?.index_b_20, city: detailB?.index_b_21 },
      ],
    },
    {
      title: '经营稳定',
      score: props.score?.indexC || 0,
      subTitle: '经营稳定：基于"营业收入变异系数"、"净利润变异系数"等指标在全国、福建省、福州市得分综合计算得出。',
      tableData: [
        { name: '股东或法人变化次数', country: detailC?.index_c_1, province: detailC?.index_c_2, city: detailC?.index_c_3 },
        { name: '经营范围或行业变更', country: detailC?.index_c_4, province: detailC?.index_c_5, city: detailC?.index_c_6 },
        { name: '经营场所或登记机关变更', country: detailC?.index_c_7, province: detailC?.index_c_8, city: detailC?.index_c_9 },
        { name: '营业收入增长情况', country: detailC?.index_c_10, province: detailC?.index_c_11, city: detailC?.index_c_12 },
        { name: '净利润增长情况', country: detailC?.index_c_13, province: detailC?.index_c_14, city: detailC?.index_c_15 },
      ],
    },
    {
      title: '创新能力',
      score: props.score?.indexD || 0,
      subTitle: '创新能力：基于"专利"、"软件"、"许可证"等指标在全国、福建省、福州市得分综合计算得出。',
      tableData: [
        { name: '专利', country: detailD?.index_d_1, province: detailD?.index_d_2, city: detailD?.index_d_3 },
        { name: '商标', country: detailD?.index_d_4, province: detailD?.index_d_5, city: detailD?.index_d_6 },
        { name: '许可证', country: detailD?.index_d_7, province: detailD?.index_d_8, city: detailD?.index_d_9 },
      ],
    },
    {
      title: '负面风险',
      score: props.score?.indexE || 0,
      subTitle: '负面风险：基于"行政处罚"、"司法风险"等指标在全国、福建省、福州市得分综合计算得出。',
      tableData: [
        { name: '行政处罚次数', country: detailE?.index_e_1, province: detailE?.index_e_2, city: detailE?.index_e_3 },
        { name: '行政处罚金额', country: detailE?.index_e_4, province: detailE?.index_e_5, city: detailE?.index_e_6 },
        { name: '被执行人涉案数量', country: detailE?.index_e_7, province: detailE?.index_e_8, city: detailE?.index_e_9 },
        { name: '被执行人涉案金额', country: detailE?.index_e_10, province: detailE?.index_e_11, city: detailE?.index_e_12 },
        { name: '失信被执行人涉案数量', country: detailE?.index_e_13, province: detailE?.index_e_14, city: detailE?.index_e_15 },
        { name: '对外投资数量', country: detailE?.index_e_16, province: detailE?.index_e_17, city: detailE?.index_e_18 },
        { name: '关联企业吊销数量', country: detailE?.index_e_19, province: detailE?.index_e_20, city: detailE?.index_e_21 },
        { name: '关联企业失信被执行人', country: detailE?.index_e_22, province: detailE?.index_e_23, city: detailE?.index_e_24 },
        { name: '关联企业行政处罚', country: detailE?.index_e_25, province: detailE?.index_e_26, city: detailE?.index_e_27 },
      ],
    },
  ]
})
</script>

<template>
  <div class="score">
    <div class="setp-title">
      01 企业综合评分概述
    </div>
    <div class="overview">
      企业综合评价是结合⾏业特点和企业实际发展情况，从⾏业地位、经营绩效、经营稳定、创新能⼒、负⾯⻛险五个维度构建了⼀套完善的企业综合 评价指标体系。
      ⾏业地位维度从企业⾃身的基本特征和财务特征出发，衡量企业在所处⾏业中的整体地位情况；经营绩效维度从企业的财务数据出发，考察企业盈
      利能⼒、偿债能⼒以及运营能⼒；经营稳定维度从企业的⼯商信息与财务数据出发，考察与企业经营相关信息的变更频率和公司持续盈利能⼒；创
      新能⼒维度从企业专利、商标、软件著作权以及许可证等多个⽅⾯，综合考察企业创新能⼒⾼低；负⾯⻛险维度从⽋税记录、 ⼯商处罚记录、负⾯ ⽹络舆情与关联企业相关处罚等⽅⾯的信息出发，对企业经营⻛险和潜在⻛险进⾏评估。
    </div>

    <div class="setp-title">
      02 评估结论
    </div>
    <div class="score-radar-container">
      <div class="chart-section">
        <!-- 左侧雷达图 -->
        <div class="radar-chart-wrapper">
          <div ref="radarChartRef" class="radar-chart" />
          <div class="chart-description">
            <p>综合评分：企业在经营绩效、经营稳定、创新能力、负面风险、行业地位五个维度的综合表现</p>
            <p>分值越高，代表企业在该维度的表现越优秀</p>
          </div>
        </div>

        <!-- 右侧进度条列表 -->
        <div class="metrics-panel">
          <div class="total-score">
            总分：{{ scoreRadarData.totalScore }}
          </div>

          <div class="metrics-list">
            <div
              v-for="metric in scoreRadarData.radarData"
              :key="metric.name"
              class="metric-item"
            >
              <div class="metric-info">
                <span class="metric-name">{{ metric.name }}：</span>
                <span class="metric-value">{{ metric.value }}</span>
              </div>
              <div class="progress-bar">
                <div class="progress-track">
                  <div
                    class="progress-fill"
                    :style="{ width: `${metric.value}%` }"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="setp-title">
      03 分项评估表
    </div>
    <div class="main-title">
      行业地位
    </div>
    <div class="sub-title">
      {{ props.detail?.entname }}行业地位评分为<span class="highlight">{{ props.score?.indexA || 0 }}</span>分。
    </div>

    <div class="score-overview">
      <div class="title">评分：<span class="highlight">{{ props.score?.indexA || 0 }}</span></div>
      <div>
        ⾏业地位：基于“注册资本”、“实缴资本”、“所有者权益”等指标在全国、福建省、福州市得分综合计算得出。
      </div>
    </div>

    <ComparisonTable class="mt-30px" :table-data="tableData" :regions="regions" />

    <div v-for="item in scoreConfigList" :key="item.title">
      <div class="main-title">
        {{ item.title }}
      </div>
      <div class="score-overview">
        <div class="title">评分：<span class="highlight">{{ item.score }}</span></div>
        <div>
          {{ item.subTitle }}
        </div>
      </div>
      <ComparisonTable class="mt-30px" :table-data="item.tableData" :regions="regions" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.score {
  padding: 20px 40px;
  background-color: #ffffff;
  .mt-30px {
    margin-top: 30px;
  }
  .setp-title {
    color: #1677FF;
    font-size: 26px;
    font-weight: 500;
    line-height: 1em;
    margin-top: 30px;
  }
  .main-title {
    color: #383838;
    line-height: 1em;
    font-size: 20px;
    margin-top: 30px;
    font-weight: 500;
  }
  .sub-title {
    color: #383838;
    line-height: 1em;
    // font-size: 20px;
    margin-top: 20px;
    font-weight: 500;
  }
  .highlight {
    color: #FF8D1A;
  }
  .overview {
    padding: 33px 40px;
    color: #383838;
    margin-top: 30px;
    background-color: #F7FAFF;
    text-indent: 2em;
    line-height: 1.8em;
    letter-spacing: 2px;
  }
  .score-overview {
    background-color: #F7FAFF;
    padding: 20px;
    color: #383838;
    font-weight: 500;
    margin-top: 30px;
    .title {
      font-size: 18px;
    }
  }

  // 雷达图容器样式
  .score-radar-container {
    margin-top: 30px;
    padding: 30px;
    background: #f8f9fc;
    border-radius: 12px;
  }

  .chart-section {
    width: 100%;
    height: 400px;
    display: flex;
    align-items: stretch;
  }

  .chart-section > * + * {
    margin-left: 40px;
  }

  .radar-chart-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .radar-chart {
    width: 400px;
    height: 300px;
    margin-bottom: 20px;
  }

  .chart-description {
    text-align: center;
    color: #666;
    font-size: 14px;
    line-height: 1.5;

    p {
      margin: 0;
      margin-bottom: 4px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .metrics-panel {
    flex: 0.6;
    display: flex;
    flex-direction: column;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .total-score {
    font-size: 20px;
    font-weight: 600;
    color: #4a90e2;
    margin-bottom: 25px;
    padding: 10px 30px;
    background-color: #E7EFFF;
  }

  .metrics-list {
    flex: 1;
    padding: 0 20px 20px;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
  }

  .metrics-list > * + * {
    margin-top: 20px;
  }

  .metric-item {
    display: flex;
    flex-direction: column;
  }

  .metric-item > * + * {
    margin-top: 8px;
  }

  .metric-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
  }

  .metric-name {
    color: #333;
    font-weight: 500;
  }

  .metric-value {
    color: #666;
    font-weight: 600;
  }

  .progress-bar {
    width: 100%;
  }

  .progress-track {
    width: 100%;
    height: 8px;
    background-color: #e8e8e8;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
  }

  .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4a90e2 0%, #7bb3f0 100%);
    border-radius: 4px;
    transition: width 0.8s ease-in-out;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      width: 2px;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 0 4px 4px 0;
    }
  }
}
</style>0
