<script setup lang="ts">
import type { PublicTemplateProps } from './PublicTemplate.vue'
import type { BasicData, JobRiskVO } from '@/api/enterprise'
import { computed } from 'vue'
import PublicTemplate from './PublicTemplate.vue'

const props = defineProps<{
  detail?: BasicData
  jobRisk?: JobRiskVO
}>()

const publicTemplateProps = computed<PublicTemplateProps>(() => {
  const indexC = props.jobRisk?.indexC || 0
  const indexCRank = (props.jobRisk?.indexCRank || 0) * 100
  const indexCRegionRank = (props.jobRisk?.indexCRegionRank || 0) * 100
  return {
  /**
   * 步骤标题
   */
    setpTitle: '04 雇主发展力',
    /**
     * 标题
     */
    mainTitle: '技术成长潜力',
    /**
     * 副标题，会被v-html渲染
     */
    subTitle: '技术成长潜力包含9项指标，重点关注<span style="color: #FF8D1A;">专利权利人、专利授权、软件著作权</span>',

    ratingDescription: '分值说明:技术成长潜力总分，分值在[0,·100]区间，分值越高，则代表技术成长潜力越大',
    ringDescribe: `${props.detail?.entname}技术成长潜力维度得分为 ${indexC} 分，其员工技术技能提升和职业发展的机会相对较${indexC > 50 ? '好' : '差'}。`,

    ringProgressOptions: [{
      percentage: Number(indexCRank.toFixed(2)),
      fillColor: '#B37FEB',
      color: '#E8D5FF',
      bgColor: '#F5F0FF',
      text: `在全国同行业招聘企业中排名前${indexCRank.toFixed(2)}%`,
    }, {
      percentage: Number(indexCRegionRank.toFixed(2)),
      fillColor: '#36CFC9',
      color: '#B3F5F2',
      bgColor: '#E6FFFB',
      text: `在${props.detail?.province}同行业招聘企业中排名前${indexCRegionRank.toFixed(2)}%`,
    }],
  }
})

const publicTemplateProps2 = computed<PublicTemplateProps>(() => {
  const indexD = props.jobRisk?.indexD || 0
  const indexDRank = (props.jobRisk?.indexDRank || 0) * 100
  const indexDRegionRank = (props.jobRisk?.indexDRegionRank || 0) * 100
  return {
  /**
   * 标题
   */
    mainTitle: '行业竞争能力',
    /**
     * 副标题，会被v-html渲染
     */
    subTitle: '行业竞争力包含5项指标，重点关注<span style="color: #FF8D1A;">市场占有率、负债增长、企业扩张能力</span>',

    ratingDescription: '分值说明:行业竞争能力总分，分值在[0,·100]区间，分值越高，则代表企业的竞争能力越强',
    ringDescribe: `${props.detail?.entname}中科极限元(杭州)智能科技股份有限公司企业负面信息维度得分为 ${indexD} 分，其负面事件或不良记录相对较${indexD > 50 ? '多' : '少'}。`,

    ringProgressOptions: [{
      percentage: Number(indexDRank.toFixed(2)),
      fillColor: '#B37FEB',
      color: '#E8D5FF',
      bgColor: '#F5F0FF',
      text: `在全国同行业招聘企业中排名前${indexDRank.toFixed(2)}%`,
    }, {
      percentage: Number(indexDRegionRank.toFixed(2)),
      fillColor: '#36CFC9',
      color: '#B3F5F2',
      bgColor: '#E6FFFB',
      text: `在${props.detail?.province}同行业招聘企业中排名前${indexDRegionRank.toFixed(2)}%`,
    }],
  }
})

const publicTemplateProps3 = computed<PublicTemplateProps>(() => {
  const indexE = props.jobRisk?.indexE || 0
  const indexERank = (props.jobRisk?.indexERank || 0) * 100
  const indexERegionRank = (props.jobRisk?.indexERegionRank || 0) * 100

  return {
  /**
   * 标题
   */
    mainTitle: '职业发展前景',
    /**
     * 副标题，会被v-html渲染
     */
    subTitle: '职业发展力包含6项指标，重点关注<span style="color: #FF8D1A;">行业营收增长、政策支持强度、岗位招聘人数</span>',

    ratingDescription: '分值说明:职业发展前景总分，分值在[0，·100]区间，分值越高，该企业的职业发展前景越好',
    ringDescribe: `${props.detail?.entname}职业发展前景维度得分为 ${indexE} 分，其职业发展前景的广阔性和丰富的晋升机会相对较${indexE > 50 ? '好' : '差'}。`,

    ringProgressOptions: [{
      percentage: Number(indexERank.toFixed(2)),
      fillColor: '#B37FEB',
      color: '#E8D5FF',
      bgColor: '#F5F0FF',
      text: `在全国同行业招聘企业中排名前${indexERank.toFixed(2)}%`,
    }, {
      percentage: Number(indexERegionRank.toFixed(2)),
      fillColor: '#36CFC9',
      color: '#B3F5F2',
      bgColor: '#E6FFFB',
      text: `在${props.detail?.province}同行业招聘企业中排名前${indexERegionRank.toFixed(2)}%`,
    }],
  }
})
</script>

<template>
  <div>
    <PublicTemplate v-bind="publicTemplateProps">
      <div class="hexagon-image-container">
        <div class="image-text image-text-1">
          软件著作权
          <div class="gray">（{{ props.jobRisk?.c0202 }}）</div>
        </div>
        <div class="image-text image-text-2">
          专利权人
          <div class="gray">（{{ props.jobRisk?.c0101 }}）</div>
        </div>
        <div class="image-text image-text-3">
          作品著作权
          <div class="gray">（{{ props.jobRisk?.c0203 }}）</div>
        </div>
        <img src="@/assets/images/pc/development-power.png">
      </div>
    </PublicTemplate>
    <PublicTemplate v-bind="publicTemplateProps2">
      <div class="hexagon-image-container">
        <div class="image-text image-text-1-2">
          市场占有率
          <div class="gray">（{{ props.jobRisk?.d0101 }}）</div>
        </div>
        <div class="image-text image-text-2-2">
          企业扩张能力
          <div class="gray">（{{ props.jobRisk?.d0102 }}）</div>
        </div>
        <div class="image-text image-text-3-2">
          负债总额增长率
          <div class="gray">（{{ props.jobRisk?.d0202 }}）</div>
        </div>
        <img src="@/assets/images/pc/industry-competitiveness.png">
      </div>
    </PublicTemplate>
    <PublicTemplate v-bind="publicTemplateProps3">
      <div class="hexagon-image-container">
        <div class="image-text image-text-1-3">
          行业营收增长率
          <div class="gray">（{{ props.jobRisk?.e0102 }}）</div>
        </div>
        <div class="image-text image-text-2-3">
          政策支持强度
          <div class="gray">（{{ props.jobRisk?.e0103 }}）</div>
        </div>
        <div class="image-text image-text-3-3">
          岗位招聘人数
          <div class="gray">（{{ props.jobRisk?.e0201 }}）</div>
        </div>
        <img src="@/assets/images/pc/career-development-prospects.png">
      </div>
    </PublicTemplate>
  </div>
</template>

<style lang="scss" scoped>
.hexagon-image-container {
  margin-bottom: 20px;
  position: relative;
  img {
    display: block;
    width: 440px;
    height: auto;
  }
  .image-text {
    position: absolute;
  }
  .image-text-1 {
    top: 30px;
    left: 210px;
  }
  .image-text-2 {
    top: 250px;
    left: 120px;
  }
  .image-text-3 {
    top: 250px;
    left: 320px;
  }

  .image-text-1-2 {
    top: -30px;
    left: 106px;
  }
  .image-text-2-2 {
    top: 156px;
    left: 180px;
  }
  .image-text-3-2 {
    top: -30px;
    left: 260px;
  }

  .image-text-1-3 {
    top: 130px;
    left: 116px;
  }
  .image-text-2-3 {
    top: -30px;
    left: 220px;
  }
  .image-text-3-3 {
    top: 130px;
    left: 320px;
  }

  .gray {
    color: #A6A6A6;
  }
}
</style>
