<script setup lang="ts">
import * as echarts from 'echarts'
import { watch } from 'vue'

export interface PublicTemplateProps {
  /**
   * 步骤标题
   */
  setpTitle?: string
  /**
   * 标题
   */
  mainTitle?: string
  /**
   * 副标题，会被v-html渲染
   */
  subTitle?: string

  /** 评分描述 */
  ratingDescription?: string
  /**
   * 圆形图的介绍
   */
  ringDescribe?: string

  ringProgressOptions?: FixedLengthTuple<{
    percentage: number
    color: string
    fillColor: string
    bgColor: string
    text: string
  }, 2>
}

const props = defineProps<PublicTemplateProps>()

const RegionChartRef = ref<FixedLengthTuple<HTMLDivElement, 2>>()
const chartInstances = ref<echarts.ECharts[]>([])

// 渲染图表的函数
function renderCharts() {
  if (!RegionChartRef.value || !props.ringProgressOptions)
    return

  RegionChartRef.value.forEach((item, index) => {
    if (!chartInstances.value[index]) {
      chartInstances.value[index] = echarts.init(item)
    }

    const config = props.ringProgressOptions[index]
    if (config) {
      chartInstances.value[index].setOption(createRingProgressOption(config.percentage, config.fillColor, config.color))
    }
  })
}

onMounted(() => {
  renderCharts()
})

// 监听props.ringProgressOptions的变化
watch(() => props.ringProgressOptions, () => {
  renderCharts()
}, { deep: true })

// 创建圆形进度条的配置
function createRingProgressOption(percentage: number, color: string, bgColor: string) {
  return {
    series: [
      {
        type: 'gauge',
        startAngle: 90, // 从顶部开始（12点方向）
        endAngle: 450, // 逆时针一圈（90 + 360 = 450）
        clockwise: false, // 逆时针方向
        pointer: {
          show: false, // 隐藏指针
        },
        progress: {
          show: true,
          overlap: false,
          roundCap: true, // 圆角端点
          clip: false,
          itemStyle: {
            borderWidth: 0,
            color,
          },
        },
        axisLine: {
          lineStyle: {
            width: 12,
            color: [[1, bgColor]], // 背景色
          },
        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
        data: [
          {
            value: percentage,
            name: '',
            title: {
              show: false,
            },
            detail: {
              valueAnimation: true,
              fontSize: 20,
              fontWeight: 'bold',
              color: '#333',
              formatter: '{value}%',
              offsetCenter: [0, 0],
            },
          },
        ],
        radius: '80%',
        center: ['50%', '50%'],
      },
    ],
  }
}
</script>

<template>
  <div class="public-template">
    <div v-if="setpTitle" class="setp-title">
      {{ setpTitle }}
    </div>
    <slot name="header" />
    <div v-if="mainTitle" class="main-title">
      {{ mainTitle }}
    </div>
    <div v-if="subTitle" class="sub-title" v-html="subTitle" />
    <div v-if="ringProgressOptions.length" class="setp-content">
      <div class="setp-content-item setp-content-item-left flex-col">
        <div class="flex-1 flex-center">
          <slot />
        </div>
        <div class="setp-content-item-left-text">{{ ratingDescription }}</div>
      </div>
      <div class="setp-content-item setp-content-item-right flex-col">
        <div class="ring-describe">{{ ringDescribe }}</div>
        <div class="echarts-box flex">
          <div
            v-for="(item, index) in ringProgressOptions" :key="index" class="echarts-ring"
            :style="{
              backgroundColor: item.bgColor,
            }"
          >
            <div ref="RegionChartRef" class="chart-container" />
            <div class="echarts-ring-text">{{ item.text }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.public-template {
  width: 100%;
  padding-bottom: 30px;
  .setp-title {
    color: #1677FF;
    font-size: 26px;
    font-weight: 500;
    line-height: 1em;
    margin-top: 30px;
  }
  .main-title {
    color: #383838;
    line-height: 1em;
    font-size: 20px;
    margin-top: 30px;
    font-weight: 500;
  }
  .sub-title {
    color: #383838;
    line-height: 1em;
    margin-top: 20px;
  }
  .setp-content {
    display: flex;
    width: 100%;
    height: 514px;
    margin-top: 40px;
    padding: 30px 40px;
    background-color: #F6F9FF;
    .setp-content-item {
      flex: 1;
    }
    .setp-content-item-left {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .setp-content-item-left-text {
        color: #837F81;
        font-size: 17px;
        height: 60px;
      }
    }
    .setp-content-item-right {
      border-radius: 8px;
      margin-left: 20px;
      justify-content: space-between;
      padding: 20px 30px;
      background-color: #ffffff;
      .ring-describe {
        padding: 0 20px;
        line-height: 2em;
        font-size: 18px;
      }
      .echarts-box {
        width: 100%;
        $size: 270px;
        height: $size;
        justify-content: space-between;
        .echarts-ring {
          width: $size;
          height: $size;
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 30px 20px;
          border-radius: 12px;
          .chart-container {
            width: 140px;
            height: 140px;
          }
          .echarts-ring-text {
            font-size: 13px;
            color: #666;
            font-weight: 400;
            margin-top: 20px;
            text-align: center;
          }
        }
      }
    }
  }
}
</style>
