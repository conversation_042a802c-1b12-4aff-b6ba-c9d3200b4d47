<script setup lang="ts">
import type { BasicData, JobRiskVO } from '@/api/enterprise'

import * as echarts from 'echarts'
import { cloneDeep } from 'lodash'
import { nextTick, onMounted, onUnmounted, ref } from 'vue'

import CommonDescriptions from '@/components/CommonDescriptions/index.vue'
// 估值报告
import { publicTemplate } from '@/hooks/useReportDetail/template'
import { formatNumberWithCommas } from '@/utils/number'

import Step2 from './Step2.vue'
import Step3 from './Step3.vue'
import Step4 from './Step4.vue'

const props = defineProps<{
  detail?: BasicData
  jobRisk?: JobRiskVO
  isExpired?: boolean
}>()

const publicTab = cloneDeep(publicTemplate)

// 雷达图相关
const radarChartRef = ref<HTMLDivElement>()
let radarChart: echarts.ECharts | null = null

const valuationData = computed(() => ({
  totalScore: props.jobRisk?.indexTotal || 0,
  radarData: [
    { name: '企业负面信息', value: props.jobRisk?.indexA || 0 },
    { name: '薪资吸引能力', value: props.jobRisk?.indexB || 0 },
    { name: '技术成长潜力', value: props.jobRisk?.indexC || 0 },
    { name: '行业竞争能力', value: props.jobRisk?.indexD || 0 },
    { name: '职业发展前景', value: props.jobRisk?.indexE || 0 },
  ],
}))

watch(() => valuationData.value, () => {
  renderCharts()
})

/** 渲染雷达图 */
function renderCharts() {
  if (!radarChartRef.value)
    return
  if (!radarChart) {
    radarChart = echarts.init(radarChartRef.value)
  }
  const option = {
    backgroundColor: 'transparent',
    radar: {
      indicator: valuationData.value.radarData.map(item => ({
        name: item.name,
        min: 0,
        max: 100,
        nameGap: 10,
      })),
      center: ['50%', '50%'],
      radius: '65%',
      startAngle: 90,
      splitNumber: 5,
      shape: 'polygon',
      axisName: {
        formatter(name: string) {
          const data = valuationData.value.radarData.find(item => item.name === name)
          return `{name|${name}}\n{value|(${data?.value || 0})}`
        },
        rich: {
          name: {
            fontSize: 12,
            color: '#666',
            lineHeight: 16,
          },
          value: {
            fontSize: 11,
            color: '#999',
            lineHeight: 14,
          },
        },
        fontSize: 12,
        color: '#666',
      },
      splitArea: {
        areaStyle: {
          color: ['rgba(114, 172, 209, 0.05)', 'rgba(114, 172, 209, 0.1)', 'rgba(114, 172, 209, 0.15)', 'rgba(114, 172, 209, 0.2)'],
        },
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(114, 172, 209, 0.3)',
          width: 1,
        },
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(114, 172, 209, 0.3)',
          width: 1,
        },
      },
    },
    series: [
      {
        type: 'radar',
        data: [
          {
            value: valuationData.value.radarData.map(item => item.value),
            areaStyle: {
              color: 'rgba(114, 172, 209, 0.3)',
            },
            lineStyle: {
              color: '#72ACD1',
              width: 2,
            },
            itemStyle: {
              color: '#72ACD1',
              borderColor: '#72ACD1',
              borderWidth: 2,
            },
            symbol: 'circle',
            symbolSize: 6,
          },
        ],
      },
    ],
  }

  radarChart.setOption(option)
}

// 组件挂载后初始化图表
onMounted(() => {
  nextTick(() => {
    renderCharts()
  })
})

// 组件卸载时销毁图表
onUnmounted(() => {
  if (radarChart) {
    radarChart.dispose()
    radarChart = null
  }
})
</script>

<template>
  <div class="valuation">
    <div class="title">
      01 雇主基本情况
    </div>
    <div
      v-for="tabs in publicTab" :key="tabs.label"
      class="tabs"
    >
      <div v-for="tab in tabs.children" :key="tab.label" class="tab-item">
        <CommonDescriptions :data="detail || {}" :columns="tab.columns">
          <template #prop-regcap="{ value }">
            {{ formatNumberWithCommas(value) }}万人民币
          </template>
        </CommonDescriptions>
      </div>
    </div>

    <!-- 描述 -->
    <div class="description">
      <div class="description-title">雇主价值整体评估总分</div>
      <div class="description-sub-title">雇主价值总分主要反应企业在其<span class="description-highlight">福利信息、负面信息、雇主发展力</span>等五大核心维度的综合实力</div>
      <div class="description-title">评估结论</div>
      <div class="description-sub-title conclusion-item" :class="{ 'conclusion-expired': !isExpired }">
        {{ props.detail?.entname }}雇主价值评分为<span class="description-highlight">{{ props.jobRisk?.indexTotal }}</span>分。
        <!-- ，属于稳健价值型企业 -->
        <!-- 结论遮罩 -->
        <div v-if="!isExpired" class="conclusion-mask" />
      </div>
    </div>

    <!-- 雇主价值评估图表 -->
    <div class="valuation-chart-container" :class="{ 'chart-expired': !isExpired }">
      <!-- 过期遮罩层 -->
      <div v-if="!isExpired" class="expired-mask" />

      <div class="chart-section">
        <!-- 左侧雷达图 -->
        <div class="radar-chart-wrapper">
          <div ref="radarChartRef" class="radar-chart" />
          <div class="chart-description">
            <p>分值说明：雇主价值总分，分值在[0，100]区间，</p>
            <p>分值越高，则代表招聘场景下企业的综合实力越强。</p>
          </div>
        </div>

        <!-- 右侧进度条列表 -->
        <div class="metrics-panel">
          <div class="total-score">
            总分：{{ props.jobRisk?.indexTotal }}
          </div>

          <div class="metrics-list">
            <div
              v-for="metric in valuationData.radarData"
              :key="metric.name"
              class="metric-item"
            >
              <div class="metric-info">
                <span class="metric-name">{{ metric.name }}：</span>
                <span class="metric-value">{{ metric.value }}</span>
              </div>
              <div class="progress-bar">
                <div class="progress-track">
                  <div
                    class="progress-fill"
                    :style="{ width: `${metric.value}%` }"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 雇主福利信息 -->
    <Step2 :is-expired="isExpired" :detail="detail" :job-risk="jobRisk" />

    <template v-if="isExpired">
      <!-- 03 雇主负面信息 -->
      <Step3 :detail="detail" :job-risk="jobRisk" />

      <!-- 04 雇主发展力 -->
      <Step4 :detail="detail" :job-risk="jobRisk" />
    </template>
  </div>
</template>

<style lang="scss" scoped>
.valuation {
  padding: 20px 40px;
  background-color: #ffffff;
}

.title {
  color: #1677FF;
  font-size: 26px;
  font-weight: 500;
  line-height: 1em;
  margin-bottom: 30px;
}

.tabs {
  .tab-item {
    .block-div {
      width: 100%;
      height: 60px;
    }

    .not-vip-mask {
      position: absolute;
      top: 0;
      left: 0;
      background: rgba(255, 255, 255, 0.8);
      .tips-button {
        width: 288px;
        height: 85px;
        border-radius: 6px;
        background: #1677FF;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 28px 36px;
        font-size: 24px;
        font-weight: 500;
        letter-spacing: 0px;
        line-height: 28.13px;
        color: rgba(255, 255, 255, 1);
        cursor: pointer;
      }

      .tips-text {
        font-size: 20px;
        font-weight: 400;
        letter-spacing: 0px;
        color: rgba(34, 34, 34, 1);
        margin-top: 14px;
      }
    }
  }
}

.description {
  .description-title {
    color: #383838;
    margin-top: 28px;
    font-size: 20px;
    font-weight: 500;
  }
  .description-sub-title {
    margin-top: 6px;
    color: #383838;

  }
  .description-highlight {
    color: #FF8D1A;
  }

  // 结论项样式
  .conclusion-item {
    position: relative;

    // 过期状态样式
    &.conclusion-expired {
      filter: blur(4px);
      pointer-events: none;
      user-select: none;
    }
  }
}

// 结论遮罩样式
.conclusion-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(2px);
  z-index: 10;
}

// 雇主价值评估图表样式
.valuation-chart-container {
  margin-top: 30px;
  padding: 30px;
  background: #f8f9fc;
  border-radius: 12px;
  position: relative;

  // 过期状态样式
  &.chart-expired {
    .chart-section {
      filter: blur(4px);
      pointer-events: none;
      user-select: none;
    }
  }
}

// 过期遮罩样式
.expired-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(2px);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;

}

.chart-section {
  width: 100%;
  height: 400px;
  display: flex;
  align-items: stretch;
}

.chart-section > * + * {
  margin-left: 40px;
}

.radar-chart-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.radar-chart {
  width: 400px;
  height: 300px;
  margin-bottom: 20px;
}

.chart-description {
  text-align: center;
  color: #666;
  font-size: 14px;
  line-height: 1.5;

  p {
    margin: 0;
    margin-bottom: 4px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.metrics-panel {
  flex: 0.6;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.total-score {
  font-size: 20px;
  font-weight: 600;
  color: #4a90e2;
  margin-bottom: 25px;
  padding: 10px 30px;
  background-color: #E7EFFF;
}

.metrics-list {
  flex: 1;
  padding: 0 20px 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}

.metrics-list > * + * {
  margin-top: 20px;
}

.metric-item {
  display: flex;
  flex-direction: column;
}

.metric-item > * + * {
  margin-top: 8px;
}

.metric-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.metric-name {
  color: #333;
  font-weight: 500;
}

.metric-value {
  color: #666;
  font-weight: 600;
}

.progress-bar {
  width: 100%;
}

.progress-track {
  width: 100%;
  height: 8px;
  background-color: #e8e8e8;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4a90e2 0%, #7bb3f0 100%);
  border-radius: 4px;
  transition: width 0.8s ease-in-out;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 2px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 0 4px 4px 0;
  }
}

// 雇主福利信息样式
.employer-benefits {
  margin-top: 40px;
  padding: 30px;
  background-color: #ffffff;
  border-radius: 12px;

  .title {
    padding: 20px 0;
    color: #1677FF;
    font-size: 28px;
    font-weight: 500;
    margin-bottom: 10px;
  }

  .subtitle {
    font-size: 18px;
    color: #333;
    margin-bottom: 40px;
    font-weight: 500;
  }

}
</style>
