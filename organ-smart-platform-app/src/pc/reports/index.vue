<script setup lang="ts">
import { <PERSON><PERSON>, <PERSON>rollbar } from 'element-ui'
import { computed, ref } from 'vue'
import { getBnScore, getBnScoreJobRisk, getEnterpriseDetail } from '@/api/enterprise'
import { getUserInfo } from '@/api/user'
import FeedbackDialog from '@/components/FeedbackDialog/index.vue'
import VipPackageDialog from '@/components/VipPackageDialog/index.vue'
import { useRequest } from '@/hooks/useRequest'
import { calculateMaskWidth, calculateScoreNodes, createScoreConfig } from '@/utils/score'
import { getFullStaticUrl } from '@/utils/string'
import Score from './components/score.vue'
import Valuation from './components/valuation/index.vue'

const props = withDefaults(defineProps<{
  /** 企业的统代 */
  uniscid: string
  keyType?: string
}>(), {
  keyType: '2'
})

const enterpriseParams = {
  key: props.uniscid,
  keyType: props.keyType,
}

/** 企业图标 */
const enterpriseIcon = getFullStaticUrl('zhjmsqPackage/icon_jmqy02.png')

const { data: user, refreshAsync: refreshUser } = useRequest(getUserInfo, {
  defaultParams: ['1'],
})

/** 企业基本信息 */
const { data: detail } = useRequest(getEnterpriseDetail, {
  defaultParams: [enterpriseParams],
})

/** 企业评分 */
const { data: score } = useRequest(getBnScore, {
  defaultParams: [enterpriseParams],
})

/** 企业招聘风险 */
const { data: jobRisk } = useRequest(getBnScoreJobRisk, {
  defaultParams: [enterpriseParams],
})

const reportModel = ref<'valuation' | 'score'>('valuation')

function handleChangePreviewModel() {
  reportModel.value = reportModel.value === 'valuation' ? 'score' : 'valuation'
}

const currentReportModel = computed(() => {
  if (reportModel.value === 'score') {
    return {
      title: '企业综合实力评分报告',
      changeTitle: '雇主价值评估报告',
    }
  }
  else if (reportModel.value === 'valuation') {
    return {
      title: '雇主价值评估报告',
      changeTitle: '企业综合实力评分报告',
    }
  }
  else {
    return {}
  }
})

const reportsSamples = [
  {
    id: 1,
    title: '企业综合实力\n评分',
    subtitle: '了解企业信用，招聘更可靠',
    linkText: '查看企业信用报告样例',
    bg: require('@/assets/images/pc/reports-bg1.png'),
    bgColor: 'linear-gradient(180deg, rgba(218, 236, 255, 1) 0%, rgba(255, 255, 255, 0) 100%)',
    reportContents: [
      {
        id: '01',
        title: '企业综合评分',
        description: '了解企业综合评分',
      },
      {
        id: '02',
        title: '基础信息',
        description: '了解企业基础信息',
      },
      {
        id: '03',
        title: '经营信息',
        description: '了解企业经营信息',
      },
      {
        id: '04',
        title: '经营风险',
        description: '了解企业经营风险',
      },
      {
        id: '05',
        title: '司法信息',
        description: '了解企业司法信息',
      },
      {
        id: '06',
        title: '知识产权',
        description: '了解企业知识产权',
      },
    ],
  },
  {
    id: 2,
    title: '雇主价值评估\n报告',
    subtitle: '助力求职者深度企业综合情况',
    linkText: '查看雇主价值评估报告样例',
    bg: require('@/assets/images/pc/reports-bg2.png'),
    bgColor: 'linear-gradient(180deg, rgba(231, 247, 251, 1) 0%, rgba(255, 255, 255, 1) 100%)',
    reportContents: [
      {
        id: '01',
        title: '雇主基本情况',
        description: '了解雇主基本情况',
      },
      {
        id: '02',
        title: '雇主福利信息',
        description: '了解雇主福利信息',
      },
      {
        id: '03',
        title: '雇主负面信息',
        description: '了解雇主负面信息',
      },
      {
        id: '04',
        title: '雇主发展力',
        description: '了解雇主发展力',
      },
    ],
  },
]

// 分数配置
const scoreConfig = createScoreConfig({
  maxScore: 100, // 最高分
  nodeCount: 3, // 节点数量
  showDecimal: false, // 不显示小数
})

/** 计算背景遮罩宽度（剩余部分） */
const backgroundMaskWidth = computed(() => {
  if (!score.value?.indexTotal) {
    return '100%' // 无分数时显示完整遮罩
  }

  return calculateMaskWidth(score.value.indexTotal, scoreConfig.maxScore)
})

/** 计算分数节点 */
const scoreNodes = computed(() => {
  return calculateScoreNodes(scoreConfig.maxScore, scoreConfig.nodeCount)
})

// VIP弹窗相关状态
const vipDialogVisible = ref(false)

// 意见反馈弹窗状态
const feedbackDialogVisible = ref(false)

// 显示VIP购买弹窗
function showVipDialog() {
  vipDialogVisible.value = true
}

// 显示意见反馈弹窗
function showFeedbackDialog() {
  feedbackDialogVisible.value = true
}

// 处理意见反馈成功
function handleFeedbackSuccess() {
  console.log('意见反馈提交成功')
}

// 处理支付成功
function handlePaymentSuccess(orderInfo: any) {
  console.log('VIP支付成功:', orderInfo)

  try {
    // 重新请求用户信息
    refreshUser()
    console.log('用户信息刷新已触发')
  }
  catch (error) {
    console.error('刷新用户信息失败:', error)
  }
}
</script>

<template>
  <div class="size-full reports-page">
    <div class="reports-content media-content-max-width">
      <div><Button class="mt-space">返回</Button></div>

      <div class="enterprise-info flex flex-align-center">
        <img class="enterprise-icon" :src="detail?.entLog || enterpriseIcon" alt="企业图标">
        <div class="company-name">{{ detail?.entname }}</div>
      </div>

      <div class="credit-score">
        <div class="flex flex-align-center" style="justify-content: space-between;margin-bottom: 14px;">
          <div class="score-label">企业信用评分</div>
          <div class="score-value">{{ score?.indexTotal }}分</div>
        </div>
        <!-- TODO 尚不知道 评分 最高值 -->
        <div class="score-bar">
          <div class="score-background" />
          <div class="score-background-mask" :style="{ width: backgroundMaskWidth }" />
        </div>
        <div class="score-range">
          <span
            v-for="(node, index) in scoreNodes"
            :key="index"
          >
            {{ node }}
          </span>
        </div>
      </div>
      <!-- 可滚动区域 -->
      <div class="srcoll-content">
        <Scrollbar style="height: 100%;">
          <div class="flex header">
            <div class="flex-1" />
            <div class="flex-1 flex-center title">{{ currentReportModel.title }}</div>
            <div class="flex-1 flex-justify-end">
              <Button
                plain type="primary" icon="el-icon-refresh"
                @click="handleChangePreviewModel"
              >
                在线预览{{ currentReportModel.changeTitle }}
              </Button>
            </div>
          </div>

          <!-- 雇主价值评估报告 -->
          <Valuation
            v-if="reportModel === 'valuation'" :detail="detail" :job-risk="jobRisk"
            :is-expired="user?.isExpired"
          />

          <!-- 企业综合实力评分报告 -->
          <Score v-else-if="reportModel === 'score'" :detail="detail" :score="score" />

          <div v-if="user?.isExpired" class="disclaimers">
            免责声明：<br>
            本报告系由检测、数据分析和判断最终形成，我⽅已尽最⼤努⼒确保本报告内容的准确。<br>
            对评估结果的使⽤，须⾃⾏承担⻛险，我⽅不承担任何法律责任，特此声明！
          </div>
          <div v-else class="not-login-tips flex-col flex-center">
            <div class="button flex-center" @click="showVipDialog">查看更多信息</div>
            <div class="text flex-center">
              更多企业征信信息，开通国聘  <i class="el-icon-close" />   海南征信产品
            </div>
          </div>

          <!-- 报告样例 -->
          <div class="reports-sample flex">
            <div
              v-for="item in reportsSamples" :key="item.id" class="reports-sample-item"
              :style="{
                background: item.bgColor,
              }"
            >
              <div class="sample-top flex">
                <div class="sample-desc">
                  <div class="title">{{ item.title }}</div>
                  <div class="subtitle">{{ item.subtitle }}</div>

                  <div class="link">
                    <i class="el-icon-paperclip" /> {{ item.linkText }}
                  </div>
                </div>
                <img class="sample-img" :src="item.bg" alt="">
              </div>
              <div class="sample-bottom">
                <div class="title">报告内容</div>
                <div class="content-list flex">
                  <div v-for="i in item.reportContents" :key="i.id" class="content-item">
                    <div class="content-title">{{ i.id }} {{ i.title }}</div>
                    <div class="content-desc">{{ i.description }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="feedback flex-center">
            <span class="feedback-text">如有问题，点击</span>
            <span class="feedback-link" @click="showFeedbackDialog">意见反馈</span>
            <span class="feedback-text">，或联系</span>
            <span class="feedback-phone">0898-68531162</span>
          </div>
        </Scrollbar>
      </div>
    </div>

    <!-- VIP套餐购买弹窗 -->
    <VipPackageDialog
      :visible="vipDialogVisible"
      @update:visible="vipDialogVisible = $event"
      @payment-success="handlePaymentSuccess"
    />

    <!-- 意见反馈弹窗 -->
    <FeedbackDialog
      :visible="feedbackDialogVisible"
      @update:visible="feedbackDialogVisible = $event"
      @success="handleFeedbackSuccess"
    />
  </div>
</template>

<style lang="scss" scoped>
// 滚动条样式优化
:deep(.el-scrollbar) {
  .el-scrollbar__wrap {
    padding-right: 8px;
  }

  .el-scrollbar__bar {
    &.is-vertical {
      right: 2px;
      width: 6px;
    }

    .el-scrollbar__thumb {
      background-color: rgba(144, 147, 153, 0.3);
      border-radius: 3px;
    }
  }
}

.reports-page {
  background-color: #F6F7FB;
  background-image: url('@/assets/images/pc/bg1.png');
  background-position: top center;
  background-repeat: no-repeat;
  background-size: 100% auto;

  .reports-content {
    display: flex;
    flex-direction: column;

    .enterprise-info {
      margin-top: 40px;
      .enterprise-icon {
        width: 48px;
        height: 48px;
        margin-right: 16px;
        border-radius: 8px;
        object-fit: contain;

      }
      .company-name {
        font-size: 28px;
        font-weight: 600;
        color: #222222;
        white-space: nowrap;
      }
    }

    .credit-score {
        padding: 20px 26px 20px;
        background-color: #ffffff;
        text-align: right;
        width: 300px;
        border-radius: 10px;
        margin-top: 30px;

        .score-label {
          color: #666666;
        }

        .score-value {
          font-size: 18px;
          // font-weight: 600;
          color: #FF4D4F;
        }

        .score-bar {
          width: 100%;
          height: 8px;
          border-radius: 4px;
          position: relative;
          background-color: #E5E5E5;
          overflow: hidden;
          margin-top: 6px;

          // background: linear-gradient(90deg, #1677FF 0%, #FE9A36 49.13%, #DD0000 100%);
          .score-background {
            position: absolute;
            left: 0;
            right: 0px;
            height: 100%;
            background: linear-gradient(90deg, #1677FF 0%, #FE9A36 49.13%, #DD0000 100%);
          }

          .score-background-mask {
            position: absolute;
            top: 0;
            right: 0;
            height: 100%;
            background-color: #E5E5E5;
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
            transition: width 0.3s ease;
            z-index: 3;
            box-shadow: 2px 0 0 0 #E5E5E5;

            // 使用 clip-path 创建向内凹的圆角效果
            clip-path: path('M 8 0 L 100% 0 L 100% 100% L 8 100% Q 0 100% 0 92% L 0 8% Q 0 0 8 0 Z');
          }
        }

        .score-range {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 12px;
          color: #999999;
          width: 100%;
          position: relative;
          margin-top: 14px;

          span:nth-child(1) {
            text-align: left;
          }

          span:nth-child(2) {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
          }

          span:nth-child(3) {
            text-align: right;
          }
        }
      }

    .srcoll-content {
      flex: 1;
      overflow: hidden;
      margin-top: 40px;
      :deep(.el-scrollbar__wrap) {
        overflow-x: hidden;
      }

      :deep(.el-scrollbar__bar) {
        opacity: 1;
      }

      :deep(.el-scrollbar__thumb) {
        background-color: rgba(144, 147, 153, 0.3);
        border-radius: 4px;

        &:hover {
          background-color: rgba(144, 147, 153, 0.5);
        }
      }
    }
  }

  .header {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    padding: 40px 40px 30px;
    background-color: #ffffff;
    .title {
      font-size: 30px;
      font-weight: 500;
    }
  }
  .disclaimers {
    color: #A6A6A6;
    padding: 20px 40px 40px;
    background-color: #ffffff;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
  }
  .not-login-tips {
    padding: 20px 40px 40px;
    background-color: #ffffff;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    .button {
      width: 160px;
      height: 60px;
      color: #ffffff;
      border-radius: 6px;
      cursor: pointer;
      background-color: #1677FF;
    }
    .text {
      margin-top: 30px;
      line-height: 1;
    }
  }

  .reports-sample {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    // border-radius-top-right: 10px;
    padding: 20px 40px;
    margin-top: 10px;
    background-color: #ffffff;
    .reports-sample-item {
      flex: 1;
      border: 4px solid #F1F6FF;
      border-radius: 2px;
      padding: 24px 24px 44px 44px;

      +.reports-sample-item {
        margin-left: 20px;
      }
      .sample-top {
        // padding-left: 20px;
        justify-content: space-between;
        .sample-desc {
          display: flex;
          flex-direction: column;
          justify-content: center;
          .title {
            color: #222222;
            font-size: 32px;
            font-weight: bold;
            white-space: pre-line;
          }
          .subtitle {
            color: #666666;
            margin-top: 20px;
          }
          .link {
            color: #1677FF;
            cursor: pointer;
            margin-top: 24px;
          }
        }
        .sample-img {
          width: 290px;
        }
      }
      .sample-bottom {
        .title {
          font-size: 22px;
          color: #222222;
        }
        .content-list {
          flex-wrap: wrap;
          margin-top: 20px;
          .content-item {
            border: 1px solid #E5E8EB;
            border-radius: 8px;
            padding: 20px 16px;
            flex: 0 0 calc(50% - 30px);
            box-sizing: border-box;
            transition: all 0.2s ease;
            cursor: pointer;
            background: linear-gradient(180deg, rgba(226, 237, 255, 1) 0%, #ffffff 100%);
            border: 1px solid rgba(255, 255, 255, 1);
            box-shadow: 5px 5px 20px rgba(52, 110, 191, 0.3);
            &:nth-child(even) {
              margin-left: 30px;
            }
            &:nth-child(n+3) {
              margin-top: 30px;
            }
            .content-title {
              font-size: 18px;
              margin-left: 1em;
              font-weight: 500;
            }
            .content-desc {
              color: #A4A4A4;
              font-size: 14px;
              margin-left: 44px;
            }
          }
        }
      }

    }
  }

  .feedback {
    height: 100px;
    background-color: #ffffff;
    font-size: 20px;
    margin-top: 10px;

    .feedback-text {
      color: #666666;
    }

    .feedback-link {
      color: #1890FF;
      cursor: pointer;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }

}
</style>
