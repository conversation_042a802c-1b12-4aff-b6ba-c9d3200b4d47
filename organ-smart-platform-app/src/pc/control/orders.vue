<script setup lang="ts">
import type { InvoiceCreateReqDTO } from '@/api/invoice'
import { Button, Message } from 'element-ui'
import { ref } from 'vue'
import { getMembershipOrderPage } from '@/api/control'
import { createInvoice } from '@/api/invoice'
import { defineCommonTablePropsColumn } from '@/components/CommonTable/hooks'
import CommonTable from '@/components/CommonTable/index.vue'
import InvoiceHeaderSelector from '@/components/InvoiceHeaderSelector/index.vue'
import { formatDateTime } from '@/utils/date'
import { formatNumberWithCommas } from '@/utils/number'

// 发票抬头选择弹窗状态
const headerSelectorVisible = ref(false)
const currentOrderId = ref('')
const currentOrderData = ref<any>(null)

// 表格引用
const tableRef = ref()

// 处理申请开票
function handleApplyInvoice(row: any) {
  currentOrderId.value = row.id
  currentOrderData.value = row
  headerSelectorVisible.value = true
}

// 处理发票抬头选择确认
async function handleHeaderConfirm(headerId: number) {
  try {
    console.log('选择的发票抬头ID:', headerId)
    console.log('订单ID:', currentOrderId.value)
    console.log('订单数据:', currentOrderData.value)

    // 构建创建发票的参数
    const invoiceParams: InvoiceCreateReqDTO = {
      orderId: Number(currentOrderId.value),
      invoiceHeaderId: headerId,
      invoiceAmount: currentOrderData.value?.actualAmount || 0,
      remark: `订单${currentOrderData.value?.orderNo}申请开票`,
    }

    // 调用创建发票接口
    await createInvoice(invoiceParams)

    Message.success('申请开票成功')

    // 刷新订单列表
    if (tableRef.value) {
      tableRef.value.refresh()
    }
  }
  catch (error: any) {
    console.error('申请开票失败:', error)
    Message.error(error.message || '申请开票失败')
  }
}

// 表格列配置
const tableColumns = defineCommonTablePropsColumn([
  { label: '订单编号', prop: 'orderNo' },
  { label: '会员套餐', prop: 'membershipName' },
  {
    label: '实付金额',
    prop: 'actualAmount',
    formatter(_row: any, _column: any, cellValue: any) {
      return cellValue ? `¥${formatNumberWithCommas(cellValue, 2)}` : '-'
    },
  },
  {
    label: '支付时间',
    prop: 'paymentTime',
    formatter(_row: any, _column: any, cellValue: any) {
      // 使用 dayjs 格式化时间，精确到秒
      return formatDateTime(cellValue)
    },
  },
  { label: '操作', prop: 'action', width: '120px' },
])
</script>

<template>
  <div class="control-child-page size-full">
    <div class="table-container">
      <CommonTable
        ref="tableRef"
        :fetch="getMembershipOrderPage"
        :columns="tableColumns"
        stripe
        border
      >
        <template #header>
          <div class="header flex flex-align-center">
            <div class="title">
              我的订单
            </div>
          </div>
        </template>

        <!-- 操作列插槽 -->
        <template #action="{ row }">
          <Button
            v-if="row.canInvoice"
            type="primary"
            size="mini"
            plain
            @click="handleApplyInvoice(row)"
          >
            申请开票
          </Button>
        </template>
      </CommonTable>
    </div>

    <!-- 发票抬头选择弹窗 -->
    <InvoiceHeaderSelector
      :visible="headerSelectorVisible"
      :order-id="currentOrderId"
      @update:visible="headerSelectorVisible = $event"
      @confirm="handleHeaderConfirm"
    />
  </div>
</template>

<style lang="scss" scoped>
.control-child-page {
  overflow: hidden;
}

.table-container {
  flex: 1;
  overflow: hidden;
}

.header {
  justify-content: space-between;
  padding: 20px 0;

  .title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }
}
</style>
