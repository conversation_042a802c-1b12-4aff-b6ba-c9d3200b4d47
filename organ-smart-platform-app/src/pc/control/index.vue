<script setup lang="ts">
import { Avatar, Button, Tag } from 'element-ui'
// 获取当前实例
import { getCurrentInstance, ref } from 'vue'

import { getUserInfo } from '@/api/user'
import VipPackageDialog from '@/components/VipPackageDialog/index.vue'
import { useRequest } from '@/hooks/useRequest'
import { formatDateTime } from '@/utils/date'
import { getFullStaticUrl } from '@/utils/string'

const { data: user, refreshAsync: refreshUser } = useRequest(getUserInfo, {
  defaultParams: ['1'],
})

const vm = getCurrentInstance().proxy

/** logo */
const hyxLogo = getFullStaticUrl('crc/login-logo.png')

// VIP弹窗状态
const vipDialogVisible = ref(false)

// 退出登录
function handleLogout() {
  vm.$message.success('退出登录')
}

// 显示VIP弹窗
function showVipDialog() {
  vipDialogVisible.value = true
}

// 支付成功回调
function handlePaymentSuccess(orderInfo: any) {
  console.log('支付成功:', orderInfo)
  vm.$message.success('支付成功，正在刷新用户状态...')

  // 刷新用户状态
  refreshUser().then(() => {
    console.log('用户状态刷新完成')
    vm.$message.success('会员状态已更新')
  }).catch((error) => {
    console.error('刷新用户状态失败:', error)
    vm.$message.error('刷新用户状态失败，请刷新页面')
  })

  // 关闭弹窗
  vipDialogVisible.value = false
}

const side = [
  {
    title: '报告订单查看',
    path: '/pc/control/review',
  },
  {
    title: '我的订单',
    path: '/pc/control/orders',
  },
  {
    title: '我的发票',
    path: '/pc/control/invoices',
  },
  {
    title: '意见反馈',
    path: '/pc/control/feedback',
  },
  {
    title: '账号管理',
    path: '/pc/control/account',
  },
]
const instance = getCurrentInstance()

// 处理菜单点击事件
function handleMenuClick(path: string) {
  if (instance?.proxy?.$route.path === path) {
    return
  }
  if (instance?.proxy?.$router) {
    instance.proxy.$router.push(path)
  }
}
</script>

<template>
  <div class="size-full control-page">
    <div class="media-content-max-width flex-col">
      <div class="mt-space"><Button>返回</Button></div>
      <!-- 主要内容区域 -->
      <div class="header">
        <div class="sign flex-center">
          <img :src="hyxLogo" alt="logo">
          <div class="authorize flex-col flex-center">
            <p>海南省征信平台</p>
            <p>人行备案企业征信机构</p>
          </div>
        </div>
        <div class="title flex-center enterprise-credit-title">
          查看企业信用信息
        </div>
        <div class="subtitle flex-center">
          甄选优质企业岗位·抢占优质岗位先机
        </div>
      </div>

      <!-- 顶部用户信息栏 -->
      <div class="top-user-bar">
        <div class="user-section">
          <Avatar
            class="user-avatar" :size="48"
            src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"
          />
          <div class="user-info">
            <div class="user-name-row">
              <span class="user-name">{{ user?.membershipTypeDesc || '会员用户' }}</span>
              <Tag
                :type="user?.isExpired ? 'danger' : 'warning'"
                size="mini"
                class="vip-tag"
                :class="{ clickable: user?.isExpired }"
                @click="user?.isExpired && showVipDialog()"
              >
                {{ user?.isExpired ? '已过期' : user?.membershipTypeDesc?.includes('VIP') ? 'VIP' : '会员' }}
              </Tag>
            </div>
            <div class="user-expire">
              <span v-if="user?.isExpired" class="expired-text">会员已过期</span>
              <span v-else>会员有效期至{{ formatDateTime(user?.expireTime) }}</span>
            </div>
          </div>
        </div>
        <Button type="primary" plain @click="handleLogout">退出登录</Button>
      </div>

      <div class="layout flex flex-1">
        <div class="side">
          <div class="side-menu">
            <div
              v-for="(item, index) in side"
              :key="index"
              class="menu-item"
              :class="{ active: $route.path === item.path }"
              @click="handleMenuClick(item.path)"
            >
              {{ item.title }}
            </div>
          </div>
        </div>
        <div class="main flex-1">
          <router-view />
        </div>
      </div>
    </div>

    <!-- VIP弹窗 -->
    <VipPackageDialog
      :visible="vipDialogVisible"
      @update:visible="vipDialogVisible = $event"
      @paymentSuccess="handlePaymentSuccess"
    />
  </div>
</template>

<style lang="scss" scoped>
.control-page {
  background-color: #F6F7FB;
  background-image: url('@/assets/images/pc/bg2.png');
  background-position: top center;
  background-repeat: no-repeat;
  background-size: 100% auto;
}
.media-content-max-width {
  padding-bottom: 100px;
  overflow: hidden;
}
// 主要内容区域
.header {
  .sign {
    margin-top: 20px;

    img {
      height: 60px;
    }

    .authorize {
      margin-left: 30px;
      color: #fff;
      font-size: 14px;
      padding: 6px 10px;
      background-color: #b50a0c;
    }
  }

  .title {
    line-height: 1;
    color: #1658D9;
    font-size: 70px;
    margin-top: 10px;
  }

  .subtitle {
    color: #383838;
    font-size: 26px;
  }
}

.top-user-bar {
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(0, 0, 0, 0.06);

  .user-section {
    display: flex;
    align-items: center;
    margin-right: 16px;

    .user-avatar {
      margin-right: 12px;
    }

    .user-info {
      height: 100%;

      .user-name-row {
        display: flex;
        align-items: center;
        margin-bottom: 2px;

        .user-name {
          margin-right: 6px;
          font-size: 14px;
          font-weight: 500;
          color: #333;
        }
      }

      .user-expire {
        font-size: 12px;
        color: #999;
        line-height: 1.2;

        .expired-text {
          color: #f56c6c;
        }
      }
    }
  }

  .vip-tag {
    margin-left: 8px;

    &.clickable {
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.05);
        box-shadow: 0 2px 8px rgba(245, 34, 45, 0.3);
      }
    }
  }
}

.layout {
  overflow: hidden;
  margin-top: 10px;
  .side {
    width: 260px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px 0;
    background-size: cover;
    background-position: center;
    background-image: url('@/assets/images/side-bg.png');

    .side-menu {
      .menu-item {
        padding: 12px 20px;
        font-size: 20px;
        color: #333;
        cursor: pointer;
        transition: all 0.2s ease;
        border-left: 3px solid transparent;

        &:hover {
          background: rgba(25, 118, 210, 0.08);
        }

        &.active {
          color: #1658D9;
        }
      }
    }
  }

  .main {
    margin-left: 20px;
    background: white;
    border-radius: 8px;
    padding: 0 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    overflow: hidden;
  }
}

// Element UI 样式覆盖 - 根据UI图片调整
:deep(.el-avatar) {
  border: 2px solid #e0e0e0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

:deep(.el-tag--mini) {
  height: 18px;
  line-height: 16px;
  padding: 0 6px;
  font-size: 11px;
  font-weight: 600;
}

:deep(.el-tag--warning) {
  background-color: #ff6b35 !important;
  border-color: #ff6b35 !important;
  color: white !important;
  border-radius: 9px;
}

:deep(.el-button--text) {
  padding: 6px 12px;
  font-weight: 400;

  &:hover {
    background-color: rgba(25, 118, 210, 0.08) !important;
  }
}
</style>
