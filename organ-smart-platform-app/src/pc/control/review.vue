<script setup lang="ts">
import { Button, Input } from 'element-ui'
import { ref } from 'vue'
import { getReportViewRecordPage } from '@/api/control'
import { defineCommonTablePropsColumn } from '@/components/CommonTable/hooks'
import CommonTable from '@/components/CommonTable/index.vue'
import { formatDateTime } from '@/utils/date'

const search = ref('')

// 表格引用
const tableRef = ref()

// 表格列配置
const tableColumns = defineCommonTablePropsColumn([
  { label: '企业名称', prop: 'enterpriseName' },
  {
    label: '查看时间',
    prop: 'createTime',
    width: '200px',
    formatter(_row: any, _column: any, cellValue: any) {
      // 使用 dayjs 格式化时间，精确到秒
      return formatDateTime(cellValue)
    },
  },
  { label: '查看结果', prop: 'viewResult', width: '120px' },
  { label: '操作', prop: 'action', width: '150px' },
])

function handleSearch(e: { isComposing?: boolean }, initializeRequest?: (params?: Record<string, any>) => void) {
  if (e.isComposing)
    return
  // 传递搜索参数给 initializeRequest
  if (initializeRequest) {
    initializeRequest({
      enterpriseName: search.value,
    })
  }
  else {
    tableRef.value?.initializeRequest({
      enterpriseName: search.value,
    })
  }
}

// 处理函数
function handleView(row: any) {
  console.log('查看详情:', row)
  // 这里可以跳转到详情页面或打开弹窗
}
</script>

<template>
  <div class="control-child-page size-full">
    <div class="table-container">
      <CommonTable
        ref="tableRef"
        :fetch="getReportViewRecordPage"
        :columns="tableColumns"
        stripe
        border
      >
        <template #header="{ initializeRequest }">
          <div class="header flex flex-align-center">
            <div class="title">
              企业征信报告查看记录
            </div>
            <div>
              <Input
                v-model="search" prefix-icon="el-icon-search" placeholder="搜索企业名称"
                clearable
                @keydown.enter.native="handleSearch($event, initializeRequest)"
              />
            </div>
          </div>
        </template>
        <template #action="{ row }">
          <Button
            type="primary" icon="el-icon-view" size="mini"
            @click="handleView(row)"
          />
          <Button
            type="danger" icon="el-icon-delete" size="mini"
          />
        </template>
      </CommonTable>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.control-child-page {
  overflow: hidden;
}

.table-container {
  flex: 1;
  overflow: hidden;
}

.header {
  justify-content: space-between;
  padding: 20px 0;

  .title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }
}

.btn-primary, .btn-secondary {
  padding: 4px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  margin-right: 8px;

  &:last-child {
    margin-right: 0;
  }
}

.btn-primary {
  background: #409eff;
  color: white;

  &:hover {
    background: #66b1ff;
  }
}

.btn-secondary {
  background: #909399;
  color: white;

  &:hover {
    background: #a6a9ad;
  }
}
</style>
