<script setup lang="ts">
import { getFeedbackPage } from '@/api/control'
import { defineCommonTablePropsColumn } from '@/components/CommonTable/hooks'
import CommonTable from '@/components/CommonTable/index.vue'
import { formatDateTime } from '@/utils/date'

// 表格列配置
const tableColumns = defineCommonTablePropsColumn([
  { label: '反馈类型', prop: 'feedbackTypeDesc' },
  {
    label: '反馈时间',
    prop: 'createTime',
    formatter(_row: any, _column: any, cellValue: any) {
      // 使用 dayjs 格式化时间，精确到秒
      return formatDateTime(cellValue)
    },
  },
  {
    label: '联系电话',
    prop: 'contactPhone',
    formatter(_row: any, _column: any, cellValue: any) {
      // 对联系电话中间四位数进行掩码处理
      if (!cellValue)
        return ''
      const phone = String(cellValue)
      if (phone.length === 11) {
        // 手机号格式：前3位 + **** + 后4位
        return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
      }
      else if (phone.length >= 7) {
        // 其他电话号码：保留前3位和后4位，中间用*号替代
        const start = phone.substring(0, 3)
        const end = phone.substring(phone.length - 4)
        const middle = '*'.repeat(phone.length - 7)
        return start + middle + end
      }
      return phone
    },
  },
])

// 处理函数
function handleView(row: any) {
  // 这里可以跳转到详情页面或打开弹窗
}

function handleReply(row: any) {
  // 这里可以打开回复弹窗
}
</script>

<template>
  <div class="control-child-page size-full">
    <div class="table-container">
      <CommonTable
        :fetch="getFeedbackPage"
        :columns="tableColumns"
        stripe
        border
      >
        <template #header>
          <div class="header">
            <div class="title">
              意见反馈管理
            </div>
          </div>
        </template>
      </CommonTable>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.control-child-page {
  overflow: hidden;
}

.table-container {
  flex: 1;
  overflow: hidden;
}

.header {
  justify-content: space-between;
  padding: 20px 0;

  .title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }
}

.btn-primary, .btn-secondary {
  padding: 4px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  margin-right: 8px;

  &:last-child {
    margin-right: 0;
  }
}

.btn-primary {
  background: #409eff;
  color: white;

  &:hover {
    background: #66b1ff;
  }
}

.btn-secondary {
  background: #909399;
  color: white;

  &:hover {
    background: #a6a9ad;
  }
}
</style>
