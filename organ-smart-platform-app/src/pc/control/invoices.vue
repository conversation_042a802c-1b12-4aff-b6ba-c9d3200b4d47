<script setup lang="ts">
import { Button, Tag } from 'element-ui'
import { getInvoicePage } from '@/api/control'
import { defineCommonTablePropsColumn } from '@/components/CommonTable/hooks'
import CommonTable from '@/components/CommonTable/index.vue'
import { formatDateTime } from '@/utils/date'

// 发票状态映射
const statusMap = {
  pending: { text: '待处理', type: 'warning' },
  PENDING: { text: '待处理', type: 'warning' },
  processing: { text: '处理中', type: 'primary' },
  issued: { text: '已开票', type: 'success' },
  rejected: { text: '已拒绝', type: 'danger' },
}

// 获取状态信息的辅助函数
function getStatusInfo(status: string) {
  return statusMap[status as keyof typeof statusMap] || { text: status, type: 'info' }
}

// 表格列配置
const tableColumns = defineCommonTablePropsColumn([
  { label: '发票申请编号', prop: 'invoiceNo' },
  { label: '发票金额', prop: 'invoiceAmount', width: '220px' },
  {
    label: '申请时间',
    prop: 'createTime',
    width: '200px',
    formatter(_row: any, _column: any, cellValue: any) {
      // 使用 dayjs 格式化时间，精确到秒
      return formatDateTime(cellValue)
    },
  },
  {
    label: '开票状态',
    prop: 'invoiceStatus',
    width: '120px',
    formatter(_row: any, _column: any, cellValue: any) {
      return getStatusInfo(cellValue).text
    },
  },
  { label: '操作', prop: 'action', width: '100px' },
])

// 处理函数
function handleView(row: any) {
  // 这里可以跳转到详情页面或打开弹窗
}

function handleDownload(row: any) {
  // 这里可以触发发票下载
}

function handleApply() {
  // 这里可以打开申请开票弹窗
}
</script>

<template>
  <div class="control-child-page size-full">
    <div class="table-container">
      <CommonTable
        :fetch="getInvoicePage"
        :columns="tableColumns"
        stripe
        border
      >
        <template #header>
          <div class="header flex flex-align-center">
            <div class="title">
              我的发票
            </div>
            <Button @click="$router.push('/pc/control/invoicesHeader')">抬头管理</Button>
          </div>
        </template>
        <template #invoiceStatus="{ row }">
          <Tag
            :type="getStatusInfo(row.invoiceStatus).type"
            size="small"
          >
            {{ getStatusInfo(row.invoiceStatus).text }}
          </Tag>
        </template>
        <template #action="{ row }">
          <Button
            v-if="row.invoiceStatus === 'issued'"
            type="success" icon="el-icon-download" size="mini"
            @click="handleDownload(row)"
          >
            下载
          </Button>
        </template>
      </CommonTable>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.control-child-page {
  overflow: hidden;
}

.table-container {
  flex: 1;
  overflow: hidden;
}

.header {
  justify-content: space-between;
  padding: 20px 0;

  .title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }

  .header-actions {
    display: flex;
    align-items: center;
  }
}

.btn-primary, .btn-secondary {
  padding: 4px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  margin-right: 8px;

  &:last-child {
    margin-right: 0;
  }
}

.btn-primary {
  background: #409eff;
  color: white;

  &:hover {
    background: #66b1ff;
  }
}

.btn-secondary {
  background: #909399;
  color: white;

  &:hover {
    background: #a6a9ad;
  }
}
</style>
