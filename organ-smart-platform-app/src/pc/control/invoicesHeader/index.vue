<script setup lang="ts">
import type { InvoiceHeaderUpdateReqDTO } from '@/api/invoice/header'
import { Button, Message, Tag } from 'element-ui'
import { ref } from 'vue'
import { deleteInvoiceHeader, getInvoiceHeaderPage, HeaderType, updateInvoiceHeader } from '@/api/invoice/header'
import { defineCommonTablePropsColumn } from '@/components/CommonTable/hooks'
import CommonTable from '@/components/CommonTable/index.vue'
import InvoiceHeaderForm from '@/components/InvoiceHeaderForm/index.vue'
import { formatDateTime } from '@/utils/date'

// 抬头类型映射
const headerTypeMap = {
  [HeaderType.Personal]: '个人',
  [HeaderType.Company]: '企业',
}

// 表格列配置
const tableColumns = defineCommonTablePropsColumn([
  {
    label: '抬头名称',
    prop: 'headerName',
  },
  {
    label: '抬头类型',
    prop: 'headerType',
    width: '100px',
    formatter(_row: any, _column: any, cellValue: HeaderType) {
      return headerTypeMap[cellValue] || cellValue
    },
  },
  {
    label: '纳税人识别号',
    prop: 'taxNumber',
    width: '200px',
  },
  {
    label: '是否默认',
    prop: 'isDefault',
    width: '80px',
  },
  {
    label: '创建时间',
    prop: 'createTime',
    width: '180px',
    formatter(_row: any, _column: any, cellValue: any) {
      return formatDateTime(cellValue)
    },
  },
  {
    label: '创建人',
    prop: 'creator',
    width: '120px',
  },
  {
    label: '操作',
    prop: 'action',
    width: '180px',
    // fixed: 'right',
  },
])

// 表格引用
const tableRef = ref()

// 弹窗状态
// 弹窗状态
const dialogVisible = ref(false)
const editData = ref<any>(null)

// 操作处理函数
function handleCreate() {
  editData.value = null // 新增时清空编辑数据
  dialogVisible.value = true
}

function handleEdit(row: any) {
  editData.value = row // 编辑时传入行数据
  dialogVisible.value = true
}

// 表单操作成功回调
function handleFormSuccess() {
  // 刷新表格数据
  if (tableRef.value) {
    tableRef.value.refresh()
  }
}

const vm = getCurrentInstance().proxy

async function handleDelete(row: any) {
  try {
    const confirmed = await vm.$confirm(
      `确定要删除发票抬头"${row.headerName}"吗？`,
      '删除确认',
    )

    if (confirmed) {
      // 调用删除API
      await deleteInvoiceHeader(row.id)
      Message.success('删除成功')

      // 刷新表格数据
      if (tableRef.value) {
        tableRef.value.refresh()
      }
    }
  }
  catch (error: any) {
    Message.error(error.message || '删除失败')
  }
}

async function handleSetDefault(row: any) {
  try {
    await vm.$confirm(
      `确定要将"${row.headerName}"设为默认发票抬头吗？`,
      '设为默认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info',
      },
    )

    // 使用对象展开语法，只覆盖需要修改的字段
    const updateData: InvoiceHeaderUpdateReqDTO = {
      ...row, // 展开所有原有字段
      isDefault: true, // 只修改这个字段
    }

    // 调用更新API设为默认
    await updateInvoiceHeader(updateData)
    Message.success('设置成功')

    // 刷新表格数据
    if (tableRef.value) {
      tableRef.value.refresh()
    }
  }
  catch (error: any) {
    // 用户取消操作或设置失败
    if (error !== 'cancel') {
      Message.error(error.message || '设置失败')
    }
  }
}
</script>

<template>
  <div class="control-child-page size-full">
    <div class="table-container">
      <CommonTable
        ref="tableRef"
        :fetch="getInvoiceHeaderPage"
        :columns="tableColumns"
      >
        <template #header>
          <div class="header flex flex-align-center flex-justify-between">
            <div class="title">
              发票抬头管理
            </div>
            <div class="header-actions">
              <Button type="primary" @click="handleCreate">
                新增抬头
              </Button>
            </div>
          </div>
        </template>

        <!-- 是否默认列插槽 -->
        <template #isDefault="{ row }">
          <Tag
            v-if="row.isDefault"
            type="success"
            size="small"
          >
            默认
          </Tag>
          <span v-else class="text-muted">-</span>
        </template>

        <!-- 操作列插槽 -->
        <template #action="{ row }">
          <div class="action-buttons">
            <el-link
              type="primary"
              @click="handleEdit(row)"
            >
              编辑
            </el-link>
            <el-link
              v-if="!row.isDefault"
              type="success"
              @click="handleSetDefault(row)"
            >
              设为默认
            </el-link>
            <el-link
              style="color: #f56c6c;"
              @click="handleDelete(row)"
            >
              删除
            </el-link>
          </div>
        </template>
      </CommonTable>
    </div>

    <!-- 新增/编辑发票抬头表单 -->
    <InvoiceHeaderForm
      :visible="dialogVisible"
      :edit-data="editData"
      @update:visible="dialogVisible = $event"
      @success="handleFormSuccess"
    />
  </div>
</template>

<style lang="scss" scoped>
.control-child-page {
  overflow: hidden;
}

.table-container {
  flex: 1;
  overflow: hidden;
}

.header {
  justify-content: space-between;
  padding: 20px 0;

  .title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }

  .header-actions {
    display: flex;
    align-items: center;
  }
}

.action-buttons {
  display: flex;
  gap: 8px;

  .Button--text {
    padding: 0;
    min-height: auto;

    &:hover {
      background-color: transparent;
    }
  }
}

.text-muted {
  color: #999;
  font-size: 12px;
}
</style>
