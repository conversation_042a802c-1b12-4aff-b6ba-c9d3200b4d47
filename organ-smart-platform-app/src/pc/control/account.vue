<script setup lang="ts">
// 账号管理页面
</script>

<template>
  <div class="account-page">
    <div class="page-header">
      <h2>账号管理</h2>
    </div>
    <div class="page-content">
      <!-- 账号管理内容区域 -->
      <div class="account-settings">
        <p>账号管理功能待开发...</p>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.account-page {
  .page-header {
    margin-bottom: 20px;

    h2 {
      font-size: 20px;
      font-weight: 500;
      color: #333;
    }
  }

  .page-content {
    .account-settings {
      padding: 20px;
      background: #f8f9fa;
      border-radius: 8px;
      text-align: center;
      color: #666;
    }
  }
}
</style>
