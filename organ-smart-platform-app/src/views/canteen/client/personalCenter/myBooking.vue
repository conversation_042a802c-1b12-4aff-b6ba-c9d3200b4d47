<template>
  <div
    class="canteen-page-with-header canteen-page-with-tabbar my-booking-page"
  >
    <MobileHeader title="我的预约" />
    <div class="content">我的预约</div>
    <BookingCard />
    <TabBar />
  </div>
</template>

<script setup lang="ts">
import MobileHeader from "@/components/MobileHeader/index.vue";
import TabBar from "@/components/TabBar/index.vue";
import BookingCard from "./_comp/myBookings/bookingCard.vue";
</script>

<style scoped lang="scss">
.my-booking-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}
</style>
