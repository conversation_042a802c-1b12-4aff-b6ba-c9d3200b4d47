<template>
  <div v-if="visible" class="user-info-overlay" @click="handleOverlayClick">
    <div class="user-info-dialog" @click.stop>
      <!-- 用户信息 -->
      <div class="info-content">
        <!-- 顶部 渐变背景+图片 -->
        <div class="header-section">
          <span class="info-title">个人信息</span>
          <!-- <img class="header-img" src="@/assets/images/mobile/canteen.png" /> -->
        </div>

        <!-- 用户信息卡片 -->
        <div class="user-info-card">
          <div class="card-content">
            <div class="info-item" v-for="info in userInfoList">
              <span class="label">{{ info.label }}</span>
              <span class="value">{{ info.value }}</span>
            </div>
            <!-- 关闭按钮 -->
            <div class="close-btn-wrapper">
              <div
                class="canteen-btn btn-primary btn-normal"
                @click="handleClose"
              >
                确认
              </div>
            </div>
          </div>
        </div>

        <!-- 关闭按钮 -->
        <!-- <div class="close-btn-wrapper">
          <div class="canteen-btn btn-primary btn-normal" @click="handleClose">
            确认
          </div>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { IUser } from "@/api/canteen/client/types/user";

/**
 * 用户信息弹窗组件Props接口
 */
interface IProps {
  /** 是否显示弹窗 */
  value: boolean;
  /** 用户信息数据 */
  userInfo: IUser;
}

/**
 * 组件事件接口
 */
interface IEmits {
  (e: "input", visible: boolean): void;
}

const props = withDefaults(defineProps<IProps>(), {
  value: false,
});
const emit = defineEmits<IEmits>();

/** 是否显示弹窗 */
const visible = computed({
  get() {
    return props.value;
  },
  set(value) {
    emit("input", value);
  },
});

/** 需要显示的用户信息列表 */
const userInfoList = computed(() => {
  if (!props.userInfo) return [];

  return [
    { label: "姓名", value: props.userInfo.username },
    {
      label: "单位",
      value: props.userInfo.dept,
    },
    {
      label: "开卡食堂",
      value: props.userInfo.canteenName,
    },
    { label: "开卡区域", value: props.userInfo.diningRegion },
  ];
});

/**
 * 处理遮罩层点击事件
 */
const handleOverlayClick = () => {
  emit("input", false);
};

/** 关闭弹窗 */
const handleClose = () => {
  emit("input", false);
};
</script>

<style scoped lang="scss">
.user-info-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;

  .user-info-dialog {
    background: #ffffff;
    border-radius: 10px;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;

    .info-content {
      overflow: hidden;
      padding: 0 20px;
      background: linear-gradient(180deg, #e6f3ff 0%, #ffffff 100%);

      .header-section {
        height: 70px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        overflow: hidden;
        // background: linear-gradient(180deg, #b1ccf6ff 0%, #ffffff 100%);

        .info-title {
          font-size: 20px;
          font-weight: 600;
          color: #383838;
          opacity: 1;
          // padding-left: 20px;
        }

        .header-img {
          width: auto;
          height: 100%;
          object-fit: cover;
          opacity: 0.75;
        }
      }

      // 用户信息卡片
      .user-info-card {
        width: 100%;
        z-index: 2;
        border-radius: 4px;
        padding: 20px;
        background-color: #ffffff;

        .info-item {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 6px 0;
          font-size: 16px;
          border-bottom: 1px solid #f0f8ff;
          margin-bottom: 10px;

          &:last-child {
            margin-bottom: 0;
          }

          .label {
            flex-shrink: 0;
            width: 80px;
            color: #808080;
          }

          .value {
            text-align: right;
            color: #1d1e20;

            &.canteen-tag {
              color: var(--color-primary);
            }
          }
        }
      }
    }

    .close-btn-wrapper {
      text-align: center;
      padding: 20px 0 0;
    }
  }
}
</style>
