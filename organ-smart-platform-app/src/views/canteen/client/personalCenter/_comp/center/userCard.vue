<template>
  <div class="user-card">
    <div class="user-info">
      <div class="user-avatar">
        <img
          src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"
        />
      </div>
      <div>
        <div class="user-name">{{ userInfo.username }}</div>
        <div v-if="userInfo.dept" class="user-dept">{{ userInfo.dept }}</div>
        <div v-if="userInfo.canteenName" class="user-canteen">
          {{ `开卡食堂：${userInfo.canteenName}` }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { IUser } from "@/api/canteen/client/types/user";
import { withDefaults } from "vue";

/** props */
interface IProps {
  /** 用户信息 */
  userInfo: IUser;
}

const props = withDefaults(defineProps<IProps>(), {
  userInfo: () => ({} as IUser),
});
</script>

<style scoped lang="scss">
.user-card {
  width: 100%;
  position: relative;
  height: 100px;
  z-index: 1;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("@/assets/images/canteen/canteen.png");
    background-size: auto 100%;
    background-position: right;
    background-repeat: no-repeat;
    opacity: 0.25; /* 控制背景图透明度 */
    z-index: -1;
  }

  .user-info {
    height: 100%;
    display: flex;
    align-items: center;
    flex: 1;

    .user-avatar {
      width: 85px;
      height: 85px;
      border-radius: 50%;
      overflow: hidden;
      margin-right: 16px;
      border: 1px solid #ffffff;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .user-name {
      font-size: 20px;
      font-weight: 500;
      color: #383838;

      margin-bottom: 8px;
    }

    .user-dept,
    .user-canteen {
      font-size: 16px;
      color: #808080;
    }
  }
}
</style>
