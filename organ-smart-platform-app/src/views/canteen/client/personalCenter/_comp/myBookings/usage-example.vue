<template>
  <div class="booking-list-example">
    <h2>预约卡片组件使用示例</h2>
    
    <div class="booking-list">
      <BookingCard
        v-for="booking in bookingList"
        :key="booking.id"
        :booking-info="booking"
        @afterUpdate="handleAfterUpdate"
      />
    </div>

    <!-- 空状态 -->
    <div v-if="bookingList.length === 0" class="empty-state">
      <p>暂无预约记录</p>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <p>加载中...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import BookingCard from './bookingCard.vue';
import { IUserBooking } from '@/api/canteen/client/types/booking';
import { getUserBookingPage } from '@/api/canteen/client/booking';
import { useToast } from '@/components/Toast';

/** Toast 提示 */
const toast = useToast();

/** 预约列表数据 */
const bookingList = ref<IUserBooking[]>([]);

/** 加载状态 */
const loading = ref(false);

/** 分页参数 */
const pagination = ref({
  pageNo: 1,
  pageSize: 10,
  total: 0,
});

/**
 * 获取预约列表数据
 */
const fetchBookingList = async () => {
  try {
    loading.value = true;
    
    const response = await getUserBookingPage({
      pageNo: pagination.value.pageNo,
      pageSize: pagination.value.pageSize,
    });
    
    bookingList.value = response.list;
    pagination.value.total = response.total;
    
  } catch (error) {
    console.error('获取预约列表失败:', error);
    toast.error('获取预约列表失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

/**
 * 处理预约更新后事件
 * 当子组件取消预约成功后，刷新列表数据
 */
const handleAfterUpdate = () => {
  console.log('预约数据已更新，重新获取列表');
  fetchBookingList();
};

/**
 * 组件挂载时获取数据
 */
onMounted(() => {
  fetchBookingList();
});
</script>

<style scoped lang="scss">
.booking-list-example {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;

  h2 {
    color: #1d1e20;
    margin-bottom: 20px;
    text-align: center;
  }

  .booking-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #999;

    p {
      font-size: 16px;
      margin: 0;
    }
  }

  .loading-state {
    text-align: center;
    padding: 40px 20px;
    color: #666;

    p {
      font-size: 14px;
      margin: 0;
    }
  }
}
</style>
