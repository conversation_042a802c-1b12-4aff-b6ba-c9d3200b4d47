# BookingCard 预约卡片组件 - 取消预约功能完善

## 📋 功能概述

已成功完善 `bookingCard.vue` 组件的取消预约功能，实现了完整的业务流程和用户体验优化。

## 🎯 实现的功能

### ✅ 核心业务逻辑

1. **取消预约流程**
   - 点击"取消预约"按钮 → 弹出确认弹框
   - 用户点击"确认取消" → 调用取消预约接口
   - 用户点击"我再想想" → 关闭确认弹框
   - 取消成功后 → 向父组件抛出 `afterUpdate` 事件

2. **防重复提交机制**
   - Loading状态管理，防止重复点击
   - 按钮禁用状态和视觉反馈
   - 接口调用期间的状态保护

3. **用户体验优化**
   - 详细的确认信息展示
   - 友好的错误提示
   - 成功操作的反馈提示

### 🔧 技术实现

#### 1. 组件导入和依赖
```typescript
import ConfirmDialog from "@/components/ConfirmDialog/index.vue";
import { cancelBooking } from "@/api/canteen/client/mealPeriod";
import { useToast } from "@/components/Toast";
```

#### 2. 状态管理
```typescript
/** 是否显示取消预约确认弹框 */
const showCancelDialog = ref(false);

/** 取消预约loading状态 */
const cancelLoading = ref(false);

/** Toast 提示 */
const toast = useToast();
```

#### 3. 事件接口定义
```typescript
interface IEmits {
  /** 预约更新后事件（用于刷新父组件数据） */
  (e: "afterUpdate"): void;
}
```

#### 4. 核心业务方法

**点击取消预约按钮**
```typescript
const handleCancelBooking = () => {
  // 如果正在取消中，则不允许再次点击
  if (cancelLoading.value) {
    return;
  }
  showCancelDialog.value = true;
};
```

**确认取消预约**
```typescript
const handleConfirmCancel = async () => {
  if (cancelLoading.value) {
    return; // 防止重复提交
  }

  try {
    cancelLoading.value = true;
    
    // 调用取消预约接口
    const result = await cancelBooking(props.bookingInfo.id);
    
    if (result.success) {
      toast.success(result.message || "预约已成功取消");
      
      // 关闭确认弹框
      showCancelDialog.value = false;
      
      // 通知父组件刷新数据
      emit("afterUpdate");
    } else {
      toast.error(result.message || "取消预约失败，请稍后重试");
    }
  } catch (error) {
    console.error("取消预约失败:", error);
    toast.error("取消预约失败，请稍后重试");
  } finally {
    cancelLoading.value = false;
  }
};
```

## 🎨 UI/UX 设计

### 1. 确认弹框设计
```vue
<ConfirmDialog
  :visible.sync="showCancelDialog"
  title="取消预约"
  :content="`确定要取消 ${bookingInfo.canteenName} 的预约吗？\n\n餐次：${bookingInfo.mealName}\n时间：${bookingInfo.mealTime}\n日期：${formattedDiningDate}`"
  confirm-text="确认取消"
  cancel-text="我再想想"
  confirm-type="danger"
  :close-on-click-modal="false"
  @confirm="handleConfirmCancel"
  @cancel="handleCancelDialogClose"
/>
```

**设计特点：**
- 危险操作使用红色按钮（`confirm-type="danger"`）
- 详细的预约信息展示，让用户明确知道要取消的内容
- 友好的按钮文案："确认取消" 和 "我再想想"
- 禁止点击遮罩关闭，确保用户必须做出明确选择

### 2. 按钮状态设计
```vue
<div
  class="canteen-btn small btn-primary danger action-btn"
  :class="{ loading: cancelLoading }"
  @click="handleCancelBooking"
>
  {{ cancelLoading ? "取消中..." : "取消预约" }}
</div>
```

**状态样式：**
```scss
.action-btn {
  font-size: 16px;
  transition: all 0.3s ease;

  &.loading {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
  }
}
```

## 📱 使用方式

### 1. 基础使用
```vue
<template>
  <BookingCard
    :booking-info="bookingData"
    @afterUpdate="handleAfterUpdate"
  />
</template>

<script setup lang="ts">
import BookingCard from './bookingCard.vue';

const handleAfterUpdate = () => {
  // 刷新预约列表数据
  fetchBookingList();
};
</script>
```

### 2. 在列表中使用
```vue
<template>
  <div class="booking-list">
    <BookingCard
      v-for="booking in bookingList"
      :key="booking.id"
      :booking-info="booking"
      @afterUpdate="handleAfterUpdate"
    />
  </div>
</template>
```

## 🔄 业务流程

### 取消预约完整流程

1. **用户操作**
   ```
   用户点击"取消预约"按钮
   ↓
   显示确认弹框，展示预约详细信息
   ↓
   用户选择："确认取消" 或 "我再想想"
   ```

2. **确认取消流程**
   ```
   用户点击"确认取消"
   ↓
   按钮进入loading状态，显示"取消中..."
   ↓
   调用 cancelBooking(bookingId) 接口
   ↓
   接口成功：显示成功提示 + 关闭弹框 + 触发 afterUpdate 事件
   ↓
   接口失败：显示错误提示
   ↓
   恢复按钮正常状态
   ```

3. **父组件响应**
   ```
   接收到 afterUpdate 事件
   ↓
   重新获取预约列表数据
   ↓
   更新UI显示
   ```

## 🛡️ 安全和错误处理

### 1. 防重复提交
- 使用 `cancelLoading` 状态控制
- 按钮禁用和视觉反馈
- 接口调用前的状态检查

### 2. 错误处理
- Try-catch 包装异步操作
- 详细的错误日志记录
- 用户友好的错误提示

### 3. 状态管理
- 确保 loading 状态在 finally 中重置
- 成功操作后的状态清理
- 弹框状态的正确管理

## 📝 注意事项

1. **接口依赖**：确保 `cancelBooking` 接口正常可用
2. **权限控制**：只有 `CONFIRMED` 状态的预约才显示取消按钮
3. **数据刷新**：父组件需要监听 `afterUpdate` 事件并刷新数据
4. **用户体验**：提供清晰的操作反馈和状态提示

## 🚀 扩展建议

1. **批量取消**：支持选择多个预约进行批量取消
2. **取消原因**：添加取消原因选择功能
3. **撤销操作**：短时间内允许撤销取消操作
4. **操作记录**：记录用户的取消操作历史
