<template>
  <div v-if="visible" class="user-info-overlay" @click="handleOverlayClick">
    <div class="user-info-dialog" @click.stop>
      <!-- 就餐码 -->
      <QrCodeInfoCard :bookingInfo="bookingInfo" :showFooter="false" />
    </div>
  </div>
</template>

<script setup lang="ts">
import QrCodeInfoCard from "@/views/canteen/client/diningCode/_comp/qrCodeInfoCard.vue";
import { IUserBooking } from "@/api/canteen/client/types/booking";

/**
 * 用户信息弹窗组件Props接口
 */
interface IProps {
  /** 是否显示弹窗 */
  value: boolean;
  /** 预约信息 */
  bookingInfo: IUserBooking;
  /** 是否显示footer */
  showFooter?: boolean;
}

/**
 * 组件事件接口
 */
interface IEmits {
  (e: "input", visible: boolean): void;
}

const props = withDefaults(defineProps<IProps>(), {
  value: false,
});
const emit = defineEmits<IEmits>();

/** 是否显示弹窗 */
const visible = computed({
  get() {
    return props.value;
  },
  set(value) {
    emit("input", value);
  },
});

/**
 * 处理遮罩层点击事件
 */
const handleOverlayClick = () => {
  emit("input", false);
};

/** 关闭弹窗 */
const handleClose = () => {
  emit("input", false);
};
</script>

<style scoped lang="scss">
.user-info-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;

  .user-info-dialog {
    background: #ffffff;
    border-radius: 10px;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
  }
}
</style>
