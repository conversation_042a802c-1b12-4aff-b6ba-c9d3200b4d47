<template>
  <div class="booking-card">BookingCard</div>
</template>

<script setup lang="ts">
import { IUserBooking } from "@/api/canteen/client/types/booking";
import { BookingStatusEnum } from "@/views/canteen/configs/enums/BookingEnums";
import { withDefaults } from "vue";

/** 外部传入的预约信息 */
interface IProps {
  bookingInfo: IUserBooking;
}

/** 预约状态的样式配置 */
interface IStatusStyle {
  backgroundColor: string;
  color: string;
}

/** 默认的预约状态样式配置 */
const statusStyleConfig: Record<BookingStatusEnum | "DEFAULT", IStatusStyle> = {
  [BookingStatusEnum.CONFIRMED]: {
    backgroundColor: "#FFF7EB",
    color: "#F39E16",
  },
  [BookingStatusEnum.CANCELLED]: {
    backgroundColor: "#FEF0F0",
    color: "#FF4D4F",
  },
  [BookingStatusEnum.VERIFIED]: {
    backgroundColor: "#F0F9EB",
    color: "#1CC960",
  },
  [BookingStatusEnum.EXPIRED]: {
    backgroundColor: "#F4F4F5",
    color: "#A6A6A6",
  },
  DEFAULT: {
    backgroundColor: "var(--color-primary-plain-bg)",
    color: "var(--color-primary)",
  },
};

const props = withDefaults(defineProps<IProps>(), {
  bookingInfo: () => ({} as IUserBooking),
});

/** 获取预约状态的样式配置 */
const getStatusStyle = (status: BookingStatusEnum) => {
  if (status in statusStyleConfig) {
    return statusStyleConfig[status];
  }
  return statusStyleConfig.DEFAULT;
};

/** 点击查看就餐码 */
const handleViewDiningCode = () => {
  console.log("handleViewDiningCode");
};

/** 点击取消预约 */
const handleCancelBooking = () => {
  console.log("handleCancelBooking");
};
</script>

<style scoped lang="scss">
.booking-card {
  padding: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  background-color: #fff;
}
</style>
