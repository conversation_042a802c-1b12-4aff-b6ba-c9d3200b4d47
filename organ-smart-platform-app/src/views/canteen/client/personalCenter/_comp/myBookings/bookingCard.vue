<template>
  <div class="booking-card">
    <div class="card-header">
      <div class="canteen-name">{{ bookingInfo.canteenName }}</div>
      <div class="canteen-tag booking-status" :style="getStatusStyle">
        {{ bookingInfo.statusLabel }}
      </div>
    </div>
    <div class="dining-info">
      <div class="canteen-tag plain">{{ bookingInfo.mealName }}</div>
      <div>{{ bookingInfo.mealTime }}</div>
      <div>{{ formattedDiningDate }}</div>
    </div>
    <div
      v-if="bookingInfo.status === BookingStatusEnum.CONFIRMED"
      class="actions"
    >
      <div class="canteen-btn small action-btn" @click="handleViewDiningCode">
        查看就餐码
      </div>
      <div
        class="canteen-btn small btn-primary danger action-btn"
        @click="handleCancelBooking"
      >
        取消预约
      </div>
    </div>

    <!-- 就餐码弹窗 -->
    <DingCodeDialog v-model="showQRCode" :bookingInfo="bookingInfo" />
  </div>
</template>

<script setup lang="ts">
import DingCodeDialog from "./dingCodeDialog.vue";
import { IUserBooking } from "@/api/canteen/client/types/booking";
import { BookingStatusEnum } from "@/views/canteen/configs/enums/BookingEnums";
import dayjs from "dayjs";
import { withDefaults } from "vue";

/** 外部传入的预约信息 */
interface IProps {
  bookingInfo: IUserBooking;
}

/** 预约状态的样式配置 */
interface IStatusStyle {
  backgroundColor: string;
  color: string;
}

/** 默认的预约状态样式配置 */
const statusStyleConfig: Record<BookingStatusEnum | "DEFAULT", IStatusStyle> = {
  [BookingStatusEnum.CONFIRMED]: {
    backgroundColor: "#FFF7EB",
    color: "#F39E16",
  },
  [BookingStatusEnum.CANCELLED]: {
    backgroundColor: "#FEF0F0",
    color: "#FF4D4F",
  },
  [BookingStatusEnum.VERIFIED]: {
    backgroundColor: "#F0F9EB",
    color: "#1CC960",
  },
  [BookingStatusEnum.EXPIRED]: {
    backgroundColor: "#F4F4F5",
    color: "#A6A6A6",
  },
  DEFAULT: {
    backgroundColor: "var(--color-primary-plain-bg)",
    color: "var(--color-primary)",
  },
};

const props = withDefaults(defineProps<IProps>(), {
  bookingInfo: () => ({} as IUserBooking),
});

/** 获取预约状态的样式配置 */
const getStatusStyle = computed(() => {
  const status = props.bookingInfo.status;
  if (status in statusStyleConfig) {
    return statusStyleConfig[status];
  }
  return statusStyleConfig.DEFAULT;
});

/** 格式化就餐日期 */
const formattedDiningDate = computed(() => {
  if (!props.bookingInfo.bookingDate) return "";
  return dayjs(props.bookingInfo.bookingDate).format("M月D日");
});

/** 是否显示就餐码 */
const showQRCode = ref(false);

/** 点击查看就餐码 */
const handleViewDiningCode = () => {
  showQRCode.value = true;
};

/** 点击取消预约 */
const handleCancelBooking = () => {
  console.log("handleCancelBooking");
};
</script>

<style scoped lang="scss">
.booking-card {
  padding: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  background-color: #fff;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .canteen-name {
      font-size: 18px;
      color: #000000;
      margin: 0;
    }
  }

  .dining-info {
    display: flex;
    // justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    column-gap: 16px;

    color: #383838;
  }

  .actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    column-gap: 16px;

    .action-btn {
      font-size: 16px;
    }
  }
}
</style>
