<template>
  <div class="personal-center-page">
    <div class="user-card-section">
      <!-- 用户卡片 -->
      <UserCard :user-info="userInfo" />
    </div>

    <!-- 我的预约 -->
    <GroupCell :cell-list="appointmentCellList" @cell-click="handleCellClick">
      <!-- 自定义第一个Cell的左侧内容 -->
      <template v-slot:left-0="{ cell, index }">
        <div class="custom-left">
          <!-- 使用图片 -->
          <div
            v-if="appointmentCellList[0].leftIconImage"
            class="icon-img-wrapper"
            :style="{
              '--wrapper-bg-color': appointmentCellList[0].leftIconBgColor,
            }"
          >
            <img
              :src="appointmentCellList[0].leftIconImage"
              alt="图标"
              class="cell-left-img"
            />
          </div>
          <i
            v-else-if="appointmentCellList[0].leftIconName"
            class="el-icon-tickets"
            style="color: var(--color-primary); margin-right: 8px"
          ></i>
          <span>{{ cell.title }}</span>
        </div>
      </template>
    </GroupCell>

    <!-- 其他功能 个人信息+消息中心 -->
    <GroupCell :cell-list="otherCellList" @cell-click="handleCellClick">
      <template
        v-for="(cellItem, cellIdx) in otherCellList"
        #[`left-${cellIdx}`]="{ cell, index }"
      >
        <div class="custom-left">
          <div
            v-if="cellItem.leftIconImage"
            class="icon-img-wrapper"
            :style="{
              '--wrapper-bg-color': cellItem.leftIconBgColor,
            }"
          >
            <img
              :src="cellItem.leftIconImage"
              alt="图标"
              class="cell-left-img"
            />
          </div>
          <i
            v-else-if="cellItem.leftIconName"
            :class="cellItem.leftIconName"
            style="margin-right: 8px"
            :style="cellItem.leftIconStyle"
          ></i>
          <span>{{ cell.title }}</span>
        </div>
      </template>

      <template
        v-for="(cellItem, cellIdx) in otherCellList"
        #[`right-${cellIdx}`]="{ cell, index }"
      >
        <div class="custom-right">
          <span>{{ cellItem.value }}</span>
        </div>
      </template>
    </GroupCell>

    <!-- 联系客服 -->
    <GroupCell
      :cell-list="customerServiceCellList"
      @cell-click="handleCellClick"
    >
      <template
        v-for="(cellItem, cellIdx) in customerServiceCellList"
        #[`left-${cellIdx}`]="{ cell, index }"
      >
        <div class="custom-left">
          <div
            v-if="cellItem.leftIconImage"
            class="icon-img-wrapper"
            :style="{
              '--wrapper-bg-color': cellItem.leftIconBgColor,
            }"
          >
            <img
              v-if="cellItem.leftIconImage"
              :src="cellItem.leftIconImage"
              alt="图标"
              class="cell-left-img"
            />
          </div>
          <i
            v-else-if="cellItem.leftIconName"
            :class="cellItem.leftIconName"
            style="margin-right: 8px"
            :style="cellItem.leftIconStyle"
          ></i>
          <span>{{ cell.title }}</span>
        </div>
      </template>

      <template
        v-for="(cellItem, cellIdx) in customerServiceCellList"
        #[`right-${cellIdx}`]="{ cell, index }"
      >
        <div class="custom-right">
          <span>{{ cellItem.value }}</span>
        </div>
      </template>
    </GroupCell>

    <!-- 用户信息弹窗 -->
    <UserInfoDialog v-model="showUserInfo" :user-info="userInfo" />

    <!-- 客服弹窗 -->
    <CustomerServiceDialog
      v-model="showCustomerService"
      :customer-service-list="customerServiceList"
    />

    <TabBar />
  </div>
</template>

<script setup lang="ts">
import { CellClickPayload, CellData } from "@/components/GroupCell/types";
import GroupCell from "@/components/GroupCell/index.vue";
import TabBar from "@/components/TabBar/index.vue";
import UserCard from "./_comp/center/userCard.vue";
import UserInfoDialog from "./_comp/center/userInfoDIalog.vue";
import CustomerServiceDialog from "./_comp/center/customerServiceDialog.vue";
import { getCurrentUserInfo } from "@/api/canteen/client/user";
import { IUser } from "@/api/canteen/client/types/user";
import { ICustomerService } from "@/api/canteen/client/types/systemConfig";
import { getMyBookingCount } from "@/api/canteen/client/booking";
import { getUnreadNotificationCount } from "@/api/canteen/client/notification";
import { getCustomerServiceList } from "@/api/canteen/client/systemConfig";
import { BIZ_MODULE_ROUTE_PREFIX } from "@/views/canteen/configs/constants";
import BagImg from "@/assets/images/canteen/bag.png";
import PhoneImg from "@/assets/images/canteen/phone.png";
import UserImg from "@/assets/images/canteen/user.png";
import EmailImg from "@/assets/images/canteen/email.png";

/** 功能列表实际配置类型 */
export interface FunctionCellData<T = any> extends CellData<T> {
  /** 左侧图标样式 */
  leftIconStyle?: Record<string, any>;
  /** 左侧图标名称 */
  leftIconName?: string;
  /** 左侧图标图片 */
  leftIconImage?: string;
  /** 点击事件自定义处理器 */
  clickHandler?: (cell: FunctionCellData, index: number) => void;
  /** 左侧是slot */
  leftSlot?: boolean;
  /** 右侧是slot */
  rightSlot?: boolean;
  /** 左侧图标背景 */
  leftIconBgColor?: string;
}

/** 路由管理 */
const router = useRouter();

/** loading控制器 */
const loadingController = ref({
  /** 用户信息加载loading */
  userInfo: false,
  /** 我的预约数加载loading */
  myBookingCount: false,
  /** 通知数加载loading */
  notificationCount: false,
  /** 客服列表加载loading */
  customerServiceList: false,
});

/** 更新loading并刷新页面 */
const updateLoadingAndRefresh = (
  key: keyof typeof loadingController.value,
  value: boolean
) => {
  loadingController.value = Object.assign({}, loadingController.value, {
    [key]: value,
  });
};

// ================== 功能列表 开始 ==================
/** cell额外带的数据 */
interface CellExtData {
  path?: string;
  callBackFn?: (payload: { cell: CellData; index: number }) => void;
}

/** 预约相关列表 */
// const appointmentCellList = ref<CellData<CellExtData>[]>([
const appointmentCellList = computed(() => {
  return [
    {
      title: "我的预约",
      leftIconImage: BagImg,
      leftIconBgColor: "#3BA3FF",
      badge: myBookingCount.value ? String(myBookingCount.value) : void 0,
      data: {
        path: `${BIZ_MODULE_ROUTE_PREFIX}/client/personalCenter/myBooking`,
      },
    },
  ] as FunctionCellData<CellExtData>[];
});

/** 其他功能列表 */
// const otherCellList = ref<FunctionCellData<CellExtData>[]>([
const otherCellList = computed(
  () =>
    [
      {
        title: "个人信息",
        value: "",
        leftIconImage: UserImg,
        leftIconBgColor: "#1CC960",
        // leftIconName: "el-icon-user",
        rightIcon: "el-icon-arrow-right",
        leftIconStyle: {
          color: "var(--color-primary)",
        },
        data: {
          callBackFn: (_payload: CellClickPayload<CellExtData>) => {
            handleViewUserInfo();
          },
        },
      },
      {
        title: "消息中心",
        value: "",
        leftIconImage: EmailImg,
        leftIconBgColor: "#FFC300",
        // leftIconName: "el-icon-bell",
        badge: notificationCount.value
          ? String(notificationCount.value)
          : void 0,
        rightIcon: "el-icon-arrow-right",
        leftIconStyle: {
          color: "#e6a23c",
        },
        data: {
          path: `${BIZ_MODULE_ROUTE_PREFIX}/client/personalCenter/notifications`,
        },
      },
    ] as FunctionCellData<CellExtData>[]
);

/** 联系客服 */
const customerServiceCellList = computed(
  () =>
    [
      {
        title: "联系客服",
        value: "",
        leftIconImage: PhoneImg,
        leftIconBgColor: "#3BA3FF",
        // leftIconName: "el-icon-user",
        rightIcon: "el-icon-arrow-right",
        leftIconStyle: {
          color: "var(--color-primary)",
        },
        data: {
          callBackFn: (_payload: CellClickPayload<CellExtData>) => {
            handleViewCustomerService();
          },
        },
      },
    ] as FunctionCellData<CellExtData>[]
);

/**
 * Cell点击事件处理
 * @param payload 点击事件数据
 */
const handleCellClick = (payload: CellClickPayload<CellExtData>) => {
  // 可以根据不同的数据类型执行不同的操作
  const { cell } = payload;
  if (cell.data?.path) {
    // 跳转到指定页面
    router.push({
      path: cell.data.path,
    });
  } else if (cell.data?.callBackFn) {
    // 执行回调函数
    cell.data.callBackFn(payload);
  } else {
    console.log("没有指定操作");
  }
};

/** 个人信息弹框显示状态 */
const showUserInfo = ref(false);

/** 弹框显示个人信息 */
const handleViewUserInfo = () => {
  showUserInfo.value = true;
};

/** 客服列表弹框显示状态 */
const showCustomerService = ref(false);
/** 弹框显示客服列表 */
const handleViewCustomerService = () => {
  showCustomerService.value = true;
};
// ================== 功能列表 结束 ==================

/** 一些需要的展示数据 */
/** 用户信息 */
const userInfo = ref<IUser>();
/** 我的预约数 */
const myBookingCount = ref(0);
/** 通知数 */
const notificationCount = ref(0);
/** 客服列表 */
const customerServiceList = ref<ICustomerService[]>([]);

/** 获取用户数据 */
const fetchUserInfo = async () => {
  try {
    if (loadingController.value.userInfo) return;
    updateLoadingAndRefresh("userInfo", true);
    userInfo.value = await getCurrentUserInfo();
  } catch (error) {
    console.error("获取用户信息失败:", error);
  } finally {
    updateLoadingAndRefresh("userInfo", false);
  }
};

/** 获取我的预约数 */
const fetchMyBookingCount = async () => {
  try {
    if (loadingController.value.myBookingCount) return;
    updateLoadingAndRefresh("myBookingCount", true);
    const count = await getMyBookingCount();
    myBookingCount.value = count;
  } catch (error) {
    console.error("获取我的预约数失败:", error);
  } finally {
    updateLoadingAndRefresh("myBookingCount", false);
  }
};

/** 获取通知数 */
const fetchNotificationCount = async () => {
  try {
    if (loadingController.value.notificationCount) return;
    updateLoadingAndRefresh("notificationCount", true);
    const count = await getUnreadNotificationCount();
    notificationCount.value = count;
  } catch (error) {
    console.error("获取通知数失败:", error);
  } finally {
    updateLoadingAndRefresh("notificationCount", false);
  }
};

/** 获取客服列表 */
const fetchCustomerServiceList = async () => {
  try {
    if (loadingController.value.customerServiceList) return;
    updateLoadingAndRefresh("customerServiceList", true);
    const list = await getCustomerServiceList();
    customerServiceList.value = list;
  } catch (error) {
    console.error("获取客服列表失败:", error);
  } finally {
    updateLoadingAndRefresh("customerServiceList", false);
  }
};

/** 初始化数据 */
const initPageData = async () => {
  fetchUserInfo();
  fetchMyBookingCount();
  fetchNotificationCount();
  fetchCustomerServiceList();
};

initPageData();
</script>

<style scoped lang="scss">
.personal-center-page {
  padding-bottom: 50px;

  .user-card-section {
    padding: 16px 0 16px 16px;
  }

  .custom-left {
    display: flex;
    align-items: center;
  }

  .cell-left-img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .icon-img-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 36px;
    width: 36px;
    margin-right: 8px;
    border-radius: 12px;
    padding: 8px;
    background-color: var(--wrapper-bg-color);
  }
}
</style>
