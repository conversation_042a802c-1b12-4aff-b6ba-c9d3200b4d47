<template>
  <div class="booking-notice">
    <div class="notice-header">
      <!-- <i class="el-icon-info"></i> -->
      <i class="el-icon-warning-outline"></i>
      <span>{{ title }}</span>
    </div>
    <ul class="notice-list">
      <li v-for="item in content" :key="item">{{ item }}</li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import { withDefaults } from "vue";

/** props 类型 */
interface IProps {
  /** 预约须知内容 */
  content?: string[];
  /** 预约须知标题 */
  title?: string;
}

/** 预约须知默认内容 */
const noticeContent = [
  "请在预约餐次就餐时间内到达所预约食堂就餐",
  "如需取消预约，请提前2小时操作",
  "预约后逾期未就餐将影响您的信用分",
  "恶意预约将被限制使用预约功能",
];

/** props */
withDefaults(defineProps<IProps>(), {
  title: "预约须知",
  content: () => [
    "请在预约餐次就餐时间内到达所预约食堂就餐",
    "如需取消预约，请提前2小时操作",
    "预约后逾期未就餐将影响您的信用分",
    "恶意预约将被限制使用预约功能",
  ],
});
</script>

<style scoped lang="scss">
.booking-notice {
  background: #ffffff;
  border-radius: 8px;
  //   padding: 20px;
  //   border-left: 4px solid var(--color-primary);
  //   box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  .notice-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    color: var(--color-primary);

    i {
      font-size: 18px;
      color: var(--color-primary);
      margin-right: 4px;
      // 上下翻转
      transform: rotate(180deg);
    }

    span {
      font-size: 16px;
      font-weight: 500;
    }
  }

  .notice-list {
    margin: 0;
    padding-left: 20px;

    li {
      font-size: 14px;
      color: #808080;
      line-height: 1.4;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>
