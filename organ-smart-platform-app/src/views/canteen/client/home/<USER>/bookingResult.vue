<template>
  <div
    v-if="visible"
    class="booking-result-overlay"
    @click="handleOverlayClick"
  >
    <div class="booking-result-dialog" @click.stop>
      <!-- 预约结果 -->
      <div class="result-content success">
        <!-- 顶部 渐变背景+图片 -->
        <div class="resule-header">
          <img
            v-if="result.successCount === 0"
            src="@/assets/images/canteen/failed.png"
            class="result-img"
          />
          <img
            v-else
            src="@/assets/images/canteen/success.png"
            class="result-img"
          />
        </div>

        <!-- 预约结果文本 -->
        <div class="result-text">{{ resultText }}</div>

        <div class="content-pd">
          <!-- 部分成功时tab按钮区域 -->
          <div class="tab-switcher">
            <div
              v-if="result.success && result.successCount > 0"
              class="tab-btn"
              :class="{ active_success: activeTab === 'success' }"
              @click.stop="activeTab = 'success'"
            >
              预约成功
            </div>
            <div
              v-if="result.failedCount > 0"
              class="tab-btn"
              :class="{ active_failed: activeTab === 'failed' }"
              @click.stop="activeTab = 'failed'"
            >
              预约失败
            </div>
          </div>

          <!-- 预约信息卡片 -->
          <div
            :class="[
              'booking-info-card',
              activeTab === 'failed' ? 'failed' : 'success',
            ]"
          >
            <div class="card-header">
              <div class="card-title">预约信息</div>
            </div>
            <div class="card-content">
              <div
                class="info-item"
                v-for="info in activeTab === 'failed'
                  ? failedInfoList
                  : successInfoList"
              >
                <span class="label">{{ info.label }}</span>
                <span class="value">{{ info.value }}</span>
              </div>
            </div>
            <div class="card-footer">
              <!-- 操作按钮 -->
              <div class="result-actions">
                <button class="btn-secondary" @click="handleConfirm">
                  返回首页
                </button>
                <button
                  v-show="activeTab === 'failed'"
                  class="btn-primary"
                  @click="handleContinueBooking"
                >
                  继续预约
                </button>
                <button
                  v-show="activeTab === 'success'"
                  class="btn-primary"
                  @click="handleViewQRCode"
                >
                  查看就餐码
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { IBatchBookingResult } from "@/api/canteen/client/types/booking";
import { getDateDisplayText } from "../shared/utils";
import dayjs from "dayjs";
import { BIZ_MODULE_ROUTE_PREFIX } from "@/views/canteen/configs/constants";

/**
 * 预约结果弹窗组件Props接口
 */
interface IProps {
  /** 是否显示弹窗 */
  value: boolean;
  /** 预约结果数据 */
  result: IBatchBookingResult;
  /** 食堂名称 */
  canteenName: string;
}

/**
 * 组件事件接口
 */
interface IEmits {
  (e: "input", visible: boolean): void;
  /** 确认按钮点击事件 如果传递了payload，则跳转到payload指定的页面, 否则默认跳转就餐码页面 */
  (e: "confirm", payload?: string): void;
  (e: "continue-booking"): void;
}

const props = withDefaults(defineProps<IProps>(), {
  value: false,
});
const emit = defineEmits<IEmits>();

/** 当前激活的标签页 */
const activeTab = ref<"success" | "failed">("success");

/** 是否显示弹窗 */
const visible = computed({
  get() {
    return props.value;
  },
  set(value) {
    emit("input", value);
  },
});

/** 预约结果文本 */
const resultText = computed(() => {
  if (props.result?.success) {
    if (props.result.failedCount === 0) {
      return "预约成功";
    } else if (props.result.successCount > 0) {
      return "部分预约成功";
    } else {
      return "预约失败";
    }
  } else {
    return "预约失败";
  }
});

/** 预约失败显示的信息列表 */
const failedInfoList = computed(() => {
  if (props.result.failedMeals.length === 0) {
    return [];
  }
  const bookingDate = props.result.failedMeals[0].bookingDate;
  return [
    { label: "餐厅", value: props.canteenName },
    {
      label: "日期",
      value: `${getDateDisplayText(bookingDate)} ${dayjs(bookingDate).format(
        "M/D"
      )}`,
    },
    {
      label: "餐次",
      value: props.result.failedMeals.map((item) => item.mealTime).join("、"),
    },
    {
      label: "失败数量",
      value: `${props.result.failedCount}个餐次`,
    },
  ];
});

/** 预约成功显示的信息列表 */
const successInfoList = computed(() => {
  if (props.result.successMeals.length === 0) {
    return [];
  }
  return [
    { label: "餐厅", value: props.canteenName },
    { label: "预约数量", value: `${props.result.successCount}个餐次` },
  ];
});

/**
 * 处理遮罩层点击事件
 */
const handleOverlayClick = () => {
  // 不做处理，不允许点击遮罩层关闭，只能通过点击按钮关闭
  // emit("input", false);
};

/**
 * 处理确定按钮点击事件
 */
const handleConfirm = () => {
  emit("confirm", `${BIZ_MODULE_ROUTE_PREFIX}/client/home`);
  emit("input", false);
};

/**
 * 处理继续预约按钮点击事件
 */
const handleContinueBooking = () => {
  emit("continue-booking");
  emit("input", false);
};

/**
 * 处理查看就餐码按钮点击事件
 */
const handleViewQRCode = () => {
  emit("confirm");
  emit("input", false);
};
</script>

<style scoped lang="scss">
.booking-result-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;

  .booking-result-dialog {
    background: #ffffff;
    border-radius: 10px;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;

    .content-pd {
      padding: 20px;
    }

    .result-content {
      padding: 0 0 24px;
      text-align: center;
      position: relative;
      border-radius: 12px;

      .resule-header {
        height: 100px;
        padding-top: 12px;
        background: linear-gradient(
          180deg,
          rgba(177, 204, 246, 1) 0%,
          rgba(255, 255, 255, 0) 100%
        );

        .result-img {
          width: auto;
          height: 100%;
          object-fit: cover;
        }
      }
      .result-text {
        color: #383838;
        font-size: 20px;
        font-weight: 500;
        margin-top: 6px;
      }

      // 标签切换器
      .tab-switcher {
        display: flex;
        column-gap: 20px;
        padding: 4px 0;
        margin-bottom: 20px;
        // backdrop-filter: blur(10px);

        .tab-btn {
          flex: 1;
          padding: 12px 16px;
          background: transparent;
          color: #ccc;
          border: 1px solid #ddd;
          font-size: 16px;
          font-weight: 500;
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.3s ease;

          &.active_success {
            color: var(--color-primary);
            background-color: var(--color-primary-plain-bg);
            border: 1px solid var(--color-primary);
          }

          &.active_failed {
            color: #f67676;
            background-color: #fef0f0;
            border: 1px solid #f67676;
          }

          &:not(.active_success):hover,
          &:not(.active_failed):hover {
            background: rgba(176, 188, 205, 0.1);
          }
        }
      }

      // 预约信息卡片
      .booking-info-card {
        z-index: 2;
        border-radius: 4px;
        padding: 20px;
        text-align: left;
        // backdrop-filter: blur(10px);

        &.success {
          background-color: #f0f8ff;
        }

        &.failed {
          background-color: #fff5f5;
        }

        .card-header {
          margin-bottom: 16px;

          .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #383838;
            margin: 0;
          }
        }

        .info-item {
          display: flex;
          align-items: center;
          padding: 6px 0;

          .label {
            flex-shrink: 0;
            width: 80px;
            font-size: 16px;
            color: #808080;
          }

          .value {
            flex-grow: 1;
            font-size: 16px;
            color: #1d1e20;
          }
        }
      }

      .card-footer {
        padding-top: 30px;
      }

      // 操作按钮
      .result-actions {
        z-index: 2;
        display: flex;
        gap: 12px;

        button {
          flex: 1;
          padding: 10px 16px;
          border-radius: 24px;
          font-size: 16px;
          // font-weight: 500;
          border: none;
          cursor: pointer;
          transition: all 0.3s ease;

          &.btn-primary {
            background-color: var(--color-primary);
            color: #ffffff;

            &:hover {
              opacity: 0.8;
            }
          }

          &.btn-secondary {
            background-color: transparent;
            color: var(--color-primary);
            border: 1px solid var(--color-primary);
            // backdrop-filter: blur(10px);

            &:hover {
              opacity: 0.8;
            }
          }
        }
      }
    }
  }
}
</style>
