<template>
  <div class="status-tag" :style="tagStyle">
    {{ text }}
  </div>
</template>

<script setup lang="ts">
import { computed, withDefaults } from "vue";

/**
 * StatusTag组件的Props接口定义
 */
interface Props {
  /** 标签文本 */
  text: string;
  /** 背景颜色 */
  backgroundColor?: string;
  /** 字体颜色 */
  textColor?: string;
  /** 标签类型，预设样式 */
  type?: "available" | "partial" | "full" | "closed";
}

// Props定义和默认值
const props = withDefaults(defineProps<Props>(), {
  backgroundColor: "",
  textColor: "",
  type: "available",
});

/**
 * 预设的标签样式配置
 */
const presetStyles = {
  available: {
    backgroundColor: "#E8F5E8",
    textColor: "#52C41A",
  },
  partial: {
    backgroundColor: "#FFF7E6",
    textColor: "#FA8C16",
  },
  full: {
    backgroundColor: "#FFF2F0",
    textColor: "#FF4D4F",
  },
  closed: {
    backgroundColor: "#FFF7E6",
    textColor: "#FA8C16",
  },
};

/**
 * 计算标签的样式
 */
const tagStyle = computed(() => {
  // 如果指定了自定义颜色，使用自定义颜色
  if (props.backgroundColor || props.textColor) {
    return {
      backgroundColor:
        props.backgroundColor || presetStyles[props.type].backgroundColor,
      color: props.textColor || presetStyles[props.type].textColor,
    };
  }

  // 否则使用预设样式
  const preset = presetStyles[props.type];
  return {
    backgroundColor: preset.backgroundColor,
    color: preset.textColor,
  };
});
</script>

<style lang="scss" scoped>
.status-tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  line-height: 20px;
  text-align: center;
  white-space: nowrap;
  transition: all 0.2s ease;

  // 确保最小宽度
  min-width: 48px;

  // 字体平滑
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 移动端适配
@media (max-width: 768px) {
  .status-tag {
    font-size: 11px;
    padding: 1px 6px;
    line-height: 18px;
    min-width: 44px;
  }
}
</style>
