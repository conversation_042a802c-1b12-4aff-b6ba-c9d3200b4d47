import dayjs from "dayjs";
import { ref } from "vue";

/** 选择的日期 */
export const selectedDate = ref<Date>();

/**
 * 获取当前日期的展示字符串 显示 今天/明天/周几
 * @param date 日期
 * @return 日期展示字符串
 */

export const getDateDisplayText = (
  date: string | number | Date | dayjs.Dayjs
) => {
  try {
    if (!date) return "";
    let datePrefix = "";
    // 如果date是今天，则显示"明天"，如果date是明天，则显示"后天"，其他情况显示星期几
    const diffDateToday = dayjs(date)
      .startOf("day")
      .diff(dayjs().startOf("day"), "day");

    switch (diffDateToday) {
      case 0:
        datePrefix = "今天";
        break;
      case 1:
        datePrefix = "明天";
        break;
      //   case 2:
      //     datePrefix = "后天";
      //     break;
      default:
        const weekDays = [
          "周日",
          "周一",
          "周二",
          "周三",
          "周四",
          "周五",
          "周六",
        ];
        datePrefix = weekDays[dayjs(date).day()];
        break;
    }

    return `${datePrefix}`;
  } catch (error) {
    console.error("getDateDisplayText error:", error);
    return "";
  }
};
