<template>
  <div class="canteen-card" @click="handleCardClick">
    <!-- 卡片头部：食堂名称和状态标签 -->
    <div class="card-header">
      <h3 class="canteen-name">{{ canteenData.name }}</h3>
      <StatusTag
        :text="canteenData.status.text"
        :type="canteenData.status.type"
      />
    </div>

    <!-- 食堂位置 -->
    <div class="location">
      <i class="el-icon-location-outline"></i>
      <span>{{ canteenData.location }}</span>
    </div>

    <!-- 今日可预约餐次 -->
    <div class="meal-section">
      <p class="meal-title">今日可预约餐次</p>

      <!-- 餐次网格布局 -->
      <div class="meal-grid">
        <div
          v-for="meal in canteenData.meals"
          :key="meal.type"
          class="meal-item"
          :class="{ disabled: meal.disabled }"
          @click.stop="handleMealClick(meal)"
        >
          <MealCard :mealData="meal"></MealCard>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { withDefaults } from "vue";
import StatusTag from "./statusTag.vue";
import MealCard from "./mealCard.vue";

/**
 * 餐次状态类型
 */
type MealStatus = "available" | "partial" | "full" | "closed";

/**
 * 餐次数据接口
 */
interface MealData {
  /** 餐次类型标识 */
  type: string;
  /** 餐次名称 */
  name: string;
  /** 餐次状态 */
  status: MealStatus;
  /** 状态标签（可选） */
  statusTag?: {
    text: string;
    type: "available" | "partial" | "full" | "closed";
  };
  /** 剩余位置描述 */
  remaining: string;
  /** 用餐时间 */
  time: string;
  /** 是否禁用 */
  disabled?: boolean;
}

/**
 * 食堂数据接口
 */
interface CanteenData {
  /** 食堂ID */
  id: string;
  /** 食堂名称 */
  name: string;
  /** 食堂位置 */
  location: string;
  /** 食堂状态 */
  status: {
    text: string;
    type: "available" | "partial" | "full" | "closed";
  };
  /** 餐次列表 */
  meals: MealData[];
}

/**
 * 组件Props接口
 */
interface Props {
  /** 食堂数据 */
  canteenData: CanteenData;
}

/**
 * 事件定义
 */
interface Emits {
  (e: "meal-click", payload: { canteen: CanteenData; meal: MealData }): void;
  (e: "card-click", canteen: CanteenData): void;
}

// Props定义
const props = withDefaults(defineProps<Props>(), {});

// 事件定义
const emit = defineEmits<Emits>();

/**
 * 获取餐次状态对应的CSS类名
 * @param status 餐次状态
 * @returns CSS类名
 */
const getMealStatusClass = (status: MealStatus): string => {
  const statusMap = {
    available: "status-available",
    partial: "status-partial",
    full: "status-full",
    closed: "status-closed",
  };
  return statusMap[status] || "status-available";
};

/**
 * 处理餐次点击事件
 * @param meal 被点击的餐次数据
 */
const handleMealClick = (meal: MealData): void => {
  if (!meal.disabled) {
    emit("meal-click", { canteen: props.canteenData, meal });
  }
};

/**
 * 处理卡片点击事件
 */
const handleCardClick = (): void => {
  emit("card-click", props.canteenData);
};
</script>

<style lang="scss" scoped>
.canteen-card {
  border-radius: 12px;
  padding: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  cursor: pointer;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #e8f4ff 0%, #ffffff 100%);
  border: 4px solid #ffffff;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;

  .canteen-name {
    font-size: 16px;
    font-weight: 600;
    color: #333333;
    margin: 0;
    line-height: 1.2;
    flex: 1;
  }
}

.location {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #999999;
  margin-bottom: 12px;

  i {
    margin-right: 4px;
    font-size: 12px;
  }
}

.meal-section {
  flex: 1;

  .meal-title {
    font-size: 12px;
    color: #999999;
    margin: 0 0 8px 0;
  }
}

.meal-grid {
  display: flex;
  gap: 8px;
}

.meal-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  cursor: pointer;
  transition: opacity 0.2s ease;

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &:not(.disabled):hover {
    .meal-name {
      color: #409eff;
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .canteen-card {
    padding: 10px;
    border-radius: 10px;
  }

  .card-header {
    .canteen-name {
      font-size: 15px;
    }
  }

  .location {
    font-size: 11px;
    margin-bottom: 10px;
  }

  .meal-section .meal-title {
    font-size: 11px;
  }

  .meal-header .meal-name {
    font-size: 13px;
  }

  .meal-remaining {
    font-size: 11px;
  }

  .meal-time {
    font-size: 10px;
  }
}
</style>
