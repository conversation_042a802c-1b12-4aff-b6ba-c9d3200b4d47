<template>
  <div class="booking-card">
    <div class="card-header">
      <h3 class="card-title">预约信息</h3>
    </div>

    <div class="booking-info">
      <!-- 餐厅信息 -->
      <div class="info-row">
        <span class="label">餐厅</span>
        <span class="value">{{ canteenName }}</span>
      </div>

      <!-- 日期信息 -->
      <div class="info-row">
        <span class="label">日期</span>
        <span class="value">{{ formattedDate }}</span>
      </div>

      <!-- 餐次信息 -->
      <div class="info-row">
        <span class="label">餐次({{ selectedMeals.length }}个)</span>
        <span class="value">{{ mealTimesText }}</span>
      </div>

      <!-- 预约数量 -->
      <div class="info-row sum-row">
        <span class="label">预约数量</span>
        <span class="value highlight">
          {{ selectedMealCount || selectedMeals.length }}个餐次
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { IMealPeriodVO } from "../shared/constants";
import { getDateDisplayText } from "../shared/utils";
import dayjs from "dayjs";

/**
 * 预约信息卡片组件Props接口
 */
interface IProps {
  /** 食堂名称 */
  canteenName: string;
  /** 选择的日期 */
  selectedDate: Date;
  /** 选择的餐次列表（当前日期） */
  selectedMeals: IMealPeriodVO[];
}

const props = withDefaults(defineProps<IProps>(), {
  selectedMeals: () => [],
});

/** 当前日期选中的餐次数 */
const selectedMealCount = computed(() => {
  return props.selectedMeals.length;
});

/**
 * 格式化日期显示
 */
const formattedDate = computed(() => {
  if (!props.selectedDate) return "";
  return `${getDateDisplayText(props.selectedDate)} ${dayjs(
    props.selectedDate
  ).format("M/D")}`;
});

/**
 * 格式化餐次时间文本
 */
const mealTimesText = computed(() => {
  if (props.selectedMeals.length === 0) {
    return "未选择";
  }

  return props.selectedMeals
    .map((meal) => `${meal.startTime} - ${meal.endTime}`)
    .join("、");
});
</script>

<style scoped lang="scss">
.booking-card {
  background: #ffffff;
  border-radius: 8px;
  margin-bottom: 16px;

  .card-header {
    margin-bottom: 16px;

    .card-title {
      font-size: 16px;
      font-weight: 500;
      color: #383838;
      margin: 0;
    }
  }

  .booking-info {
    .info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 8px;
      border-bottom: 1px solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }

      .label {
        font-size: 14px;
        color: #808080;
        flex-shrink: 0;
      }

      .value {
        font-size: 14px;
        color: #1d1e20;
        text-align: right;
        flex: 1;
        margin-left: 16px;

        &.highlight {
          color: var(--color-primary);
          // font-weight: 500;
        }

        .total-hint {
          font-size: 12px;
          color: #999999;
          font-weight: normal;
        }
      }
    }

    .sum-row {
      background-color: #f0f8ff;
    }
  }
}
</style>
