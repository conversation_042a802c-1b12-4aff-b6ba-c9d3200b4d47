<template>
  <div class="meal-card-wrapper">
    <div
      class="meal-card"
      :class="{
        selected: isSelected,
        disabled: !isBookingAvailable,
        full: isTempFull || isAdvanceFull,
      }"
      @click="handleClick"
    >
      <!-- 餐次图标和名称 -->
      <div class="meal-header">
        <div class="meal-icon">
          <i :class="mealIconClass"></i>
        </div>
        <div class="meal-info">
          <div class="meal-name">{{ mealData.mealName }}</div>
          <div class="meal-time">
            {{ `${mealData.startTime}-${mealData.endTime}` }}
          </div>
        </div>
      </div>

      <!-- 状态按钮区域 -->
      <div class="meal-action">
        <!-- 预约未开始状态 -->
        <div v-if="!isBookingAvailable && !isBookingExpired">
          <!-- <div v-if="!isBookingAvailable"> -->
          <div class="action-btn not-started">预约未开始</div>
          <div class="temp-tip text-center">
            剩余<span style="margin: 0 3px">{{
              mealData.tempBookingSeats || 0
            }}</span
            >位
          </div>
        </div>
        <!-- 临时预约已结束 -->
        <div v-else-if="isBookingExpired">
          <div class="action-btn closed">预约已结束</div>
        </div>
        <!-- 预约已开始 -->
        <div v-else>
          <!-- 可预约状态（提前预约 或者 临时预约且有剩余席位） -->
          <div
            v-if="
              isAdvanceBookingTime ||
              (isTempBookingTime && mealData.quotaLeft > 0)
            "
          >
            <div class="action-btn available" :class="{ selected: isSelected }">
              {{ isSelected ? "已选" : "可预约" }}
              <i v-if="isSelected" class="el-icon-check"></i>
            </div>
            <div v-if="isTempBooking" class="temp-tip">
              剩余<span class="num text-primary">{{
                mealData.quotaLeft || 0
              }}</span
              >位
            </div>
            <!-- <div
              v-if="
                isAdvanceBookingTime &&
                !isEmpty(mealData.advanceBookingSeats) &&
                mealData.advanceBookingSeats > 0
              "
              class="temp-tip"
            >
              剩余{{ mealData.advanceBookingSeats }}位
            </div> -->
          </div>
          <!-- 已约满状态 -->
          <div v-else class="full-section">
            <div class="action-btn full">已约满</div>
            <div class="urge-section">
              <button
                v-if="!mealData.isUrged"
                class="urge-btn"
                @click.stop="handleUrgeClick"
              >
                一键催发
              </button>
              <button v-else class="urge-btn urged" disabled>已催发</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 催发提示信息（在卡片外部） -->
    <div v-if="isTempFull && mealData.isUrged" class="urge-tip-external">
      <i class="el-icon-circle-check"></i>
      {{ `${mealData.mealName}催发申请已提交，我们会尽快为您增加名额` }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import dayjs from "dayjs";
import { isEmpty } from "@/utils/validate";
import { IMealPeriodVO } from "../shared/constants";

/**
 * 餐次卡片组件Props接口
 */
interface Props {
  /** 餐次数据 */
  mealData: IMealPeriodVO;
  /** 是否选中 */
  isSelected?: boolean;
  /** 日期键值 */
  dateKey: string;
}

/**
 * 组件事件接口
 */
interface Emits {
  (e: "select", mealData: IMealPeriodVO, dateKey: string): void;
  (e: "urge", mealData: IMealPeriodVO, dateKey: string): void;
}

/** 定义Props */
const props = withDefaults(defineProps<Props>(), {
  // isSelected: false,
});

/** 是否选中 */
const isSelected = computed(() => {
  return props.mealData.selected;
});

/** 定义Emits */
const emit = defineEmits<Emits>();

/** 是否在临时预约时间段内 */
const isTempBookingTime = computed(() => {
  // 根据临时预约开始时间和结束时间判断是否是在临时预约时间段内
  // 临时预约结束时间可能小于开始时间，表示跨天
  const now = dayjs();
  const openTime = dayjs(
    `${props.dateKey} ${props.mealData.tempBookingOpenTime}:00`
  );
  const closeTime = dayjs(
    `${props.dateKey} ${props.mealData.tempBookingCloseTime}:00`
  );
  if (openTime.isAfter(closeTime)) {
    return now.isAfter(openTime) && now.isBefore(closeTime.add(1, "day"));
  } else {
    return now.isAfter(openTime) && now.isBefore(closeTime);
  }
});

/** 是否在提前预约时间段内 */
const isAdvanceBookingTime = computed(() => {
  // 根据提前预约开始时间和结束时间判断是否是在提前预约时间段内
  // 提前预约结束时间可能小于开始时间，表示跨天
  const now = dayjs();
  // 如果是未来的日期，则提前预约时间段无效
  const isFutureDate = dayjs(props.dateKey)
    .startOf("day")
    .isAfter(dayjs().startOf("day"));
  // 提前预约的开始日期全部默认为今天
  const openTime = dayjs(
    `${dayjs().format("YYYY-MM-DD")} ${
      props.mealData.advanceBookingOpenTime
    }:00`
  );
  const closeTime = dayjs(
    `${props.dateKey} ${props.mealData.advanceBookingCloseTime}:00`
  );
  if (isFutureDate) {
    return now.isBefore(closeTime);
  }
  if (openTime.isAfter(closeTime)) {
    return now.isAfter(openTime) && now.isBefore(closeTime.add(1, "day"));
  } else {
    return now.isAfter(openTime) && now.isBefore(closeTime);
  }
});

/** 计算是否是临时预约 */
const isTempBooking = computed(() => {
  // 提前预约已结束且临时预约未结束，则为临时预约
  return !isAdvanceBookingTime.value && isTempBookingTime.value;
});

/** 是否在可预约时间内 */
const isBookingAvailable = computed(() => {
  return isAdvanceBookingTime.value || isTempBookingTime.value;
});

/** 临时预约已约满 */
const isTempFull = computed(() => {
  return (
    isTempBooking.value &&
    !isEmpty(props.mealData.quotaLeft) &&
    props.mealData.quotaLeft === 0
  );
});

/** 提前预约已约满 */
const isAdvanceFull = computed(() => {
  return (
    isAdvanceBookingTime.value &&
    !isEmpty(props.mealData.advanceBookingSeats) &&
    props.mealData.advanceBookingSeats === 0
  );
});

/** 是否已过预约时间段 */
const isBookingExpired = computed(() => {
  // 不在预约时间内，且当前时间已超过临时预约结束时间，则为已过预约时间段
  return (
    !isBookingAvailable.value &&
    dayjs().isAfter(
      dayjs(`${props.dateKey} ${props.mealData.tempBookingCloseTime}:00`)
    )
  );
});

/**
 * 计算是否禁用
 */
const isDisabled = computed(() => {
  // 未开始 或者 已约满
  return !isBookingAvailable.value || isTempFull.value || isAdvanceFull.value;
});

/**
 * 计算餐次图标类名
 */
const mealIconClass = computed(() => {
  const mealName = props.mealData.mealName.toLowerCase();
  if (mealName.includes("早") || mealName.includes("breakfast")) {
    return "el-icon-sunrise-1";
  } else if (mealName.includes("午") || mealName.includes("lunch")) {
    return "el-icon-sunny";
  } else if (mealName.includes("晚") || mealName.includes("dinner")) {
    return "el-icon-moon";
  }
  return "el-icon-food";
});

/**
 * 处理卡片点击事件
 */
const handleClick = () => {
  // 只有可预约状态才能点击选择
  if (!isDisabled.value) {
    emit("select", props.mealData, props.dateKey);
  }
};

/**
 * 处理催发点击事件
 */
const handleUrgeClick = () => {
  emit("urge", props.mealData, props.dateKey);
};
</script>

<style scoped lang="scss">
.meal-card-wrapper {
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }
}

.meal-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #ffffff;
  border: 1px solid #e5e5e5;
  border-radius: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  .meal-header {
    display: flex;
    align-items: center;
    flex: 1;

    .meal-icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: #f0f8ff;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;

      i {
        font-size: 20px;
        color: var(--color-primary);
      }
    }

    .meal-info {
      .meal-name {
        font-size: 16px;
        font-weight: 500;
        color: #333333;
        margin-bottom: 4px;
      }

      .meal-time {
        font-size: 14px;
        color: #666666;
      }
    }
  }

  .text-primary {
    color: var(--color-primary);
  }

  .text-center {
    text-align: center;
  }

  .meal-action {
    display: flex;
    flex-direction: column;
    align-items: flex-end;

    .action-btn {
      padding: 4px 14px;
      border-radius: 9999px;
      font-size: 14px;
      // font-weight: 500;
      display: flex;
      align-items: center;
      gap: 4px;

      &.available {
        background: #e8f4fd;
        color: var(--color-primary);
        border: 1px solid var(--color-primary);

        &.selected {
          background: var(--color-primary);
          color: #ffffff;
        }
      }

      &.not-started {
        background: #fff7eb;
        color: #f39c12;
        // border: 1px solid #fa8c16;
        border: none;
      }

      &.full {
        background: rgba(245, 108, 108, 0.2);
        color: #f56c6c;
        // border: 1px solid #f56c6c;
        border: none;
      }

      &.closed {
        background: #f5f5f5;
        color: #999999;
        border: 1px solid #e5e5e5;
      }
    }

    .full-section {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 8px;

      .urge-section {
        .urge-btn {
          padding: 4px 14px;
          border-radius: 9999px;
          font-size: 14px;
          border: none;
          cursor: pointer;
          transition: all 0.3s ease;

          &:not(.urged) {
            background: #67c23a;
            color: #ffffff;

            &:hover {
              // background: #f0f9eb;
              // color: #67c23a;
              opacity: 0.8;
            }
          }

          &.urged {
            background: #f0f9eb;
            color: #67c23a;
            cursor: not-allowed;
          }
        }
      }
    }
  }

  &.selected {
    border-color: var(--color-primary);
    background: linear-gradient(
      180deg,
      rgba(232, 243, 255, 1) 0%,
      rgba(255, 255, 255, 0) 100%
    );
    .meal-header .meal-icon {
      background: var(--color-primary);

      i {
        color: #ffffff;
      }
    }
  }

  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &.full {
    cursor: not-allowed;
  }

  &:not(.disabled):not(.full):hover {
    border-color: var(--color-primary);
    cursor: pointer;
    box-shadow: 0 4px 16px rgba(58, 136, 245, 0.1);
  }

  .temp-tip {
    margin-top: 8px;
    font-size: 14px;
    color: #999999;

    .num {
      margin: 0 3px;
      font-weight: 500;
      // color: var(--color-primary);
    }
  }
}

.urge-tip-external {
  margin-top: 6px;
  padding: 6px 12px;
  background: #f0f9eb;
  font-size: 12px;
  color: #67c23aff;
  display: flex;
  align-items: center;
  gap: 6px;

  i {
    font-size: 14px;
    color: #67c23aff;
  }
}
</style>
