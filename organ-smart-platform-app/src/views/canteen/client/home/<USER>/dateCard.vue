<template>
  <div
    class="date-card"
    :class="{
      active: isActive,
      disabled: isDisabled,
    }"
    @click="handleClick"
  >
    <div class="date-label">{{ dateLabel }}</div>
    <div class="date-number">{{ dateNumber }}</div>

    <!-- 选中状态勾选标志 -->
    <div v-if="hasSelected" class="selected-indicator">
      <i class="el-icon-success"></i>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { getDateDisplayText } from "../shared/utils";

/**
 * 日期卡片组件Props接口
 */
interface Props {
  /** 日期对象 */
  date: Date;
  /** 是否选中 */
  isActive?: boolean;
  /** 是否禁用 */
  isDisabled?: boolean;
  /** 是否有选中的餐次 */
  hasSelected?: boolean;
}

/**
 * 组件事件接口
 */
interface Emits {
  (e: "select", date: Date): void;
}

const props = withDefaults(defineProps<Props>(), {
  isActive: false,
  isDisabled: false,
  hasSelected: false,
});

const emit = defineEmits<Emits>();

/**
 * 计算日期标签（今天、明天、周几）
 */
const dateLabel = computed(() => {
  return getDateDisplayText(props.date);
});

/**
 * 计算日期数字（月/日格式）
 */
const dateNumber = computed(() => {
  const month = props.date.getMonth() + 1;
  const day = props.date.getDate();
  return `${month}/${day}`;
});

/**
 * 处理点击事件
 */
const handleClick = () => {
  if (!props.isDisabled) {
    emit("select", props.date);
  }
};
</script>

<style scoped lang="scss">
.date-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 60px;
  max-width: 60px;
  height: 80px;
  padding: 8px 12px;
  border-radius: 4px;
  background: #ffffff;
  border: 1px solid #e2f1ff;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #1d1e20;
  position: relative;

  &:last-child {
    margin-right: 0;
  }

  .date-label {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 10px;
    line-height: 1;
  }

  .date-number {
    font-size: 13px;
    line-height: 1;
  }

  .selected-indicator {
    color: var(--color-primary);
    position: absolute;
    bottom: 4px;
    right: 4px;

    i {
      font-size: 12px;
    }
  }

  &.active {
    background: linear-gradient(180deg, #eef7ff 0%, #ffffff 100%);
    color: var(--color-primary);

    .date-label,
    .date-number {
      color: var(--color-primary);
    }
  }

  &.disabled {
    background: #f5f5f5;
    border-color: #e5e5e5;
    cursor: not-allowed;

    .date-label,
    .date-number {
      color: #cccccc;
    }

    .selected-indicator {
      background: #cccccc;
    }
  }

  &:not(.disabled):not(.active):hover {
    background: #f0f8ff;
    border-color: var(--color-primary);
  }
}
</style>
