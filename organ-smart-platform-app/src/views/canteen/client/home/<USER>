<template>
  <div class="home-page">
    <div class="home-content">
      <!-- 头部背景区域 -->
      <div class="header-section">
        <div class="header-content">
          <h1 class="page-title">跨区就餐预约</h1>
          <p class="page-subtitle">美好时光，从选择开始</p>
        </div>
      </div>

      <!-- 食堂列表区域 -->
      <div class="content-section">
        <div class="canteen-list">
          <div
            v-for="canteen in canteenList"
            :key="canteen.id"
            class="canteen-item"
            @click.stop="handleCardClick(canteen)"
          >
            <div class="left">
              <div class="canteen-name">{{ canteen.canteenName }}</div>
              <div class="canteen-address">
                <i class="el-icon-location-outline"></i>
                {{ canteen.canteenAddress.repeat(20) }}
              </div>
            </div>
            <div class="right">
              <div
                v-for="tag in canteen.statusList"
                :style="tag.config"
                class="status-tag"
              >
                {{ tag.text }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <TabBar />
  </div>
</template>

<script setup lang="ts">
import TabBar from "@/components/TabBar/index.vue";
import { ICanteenInfo } from "@/api/canteen/client/types/canteenManage";
import { getCanteenList } from "@/api/canteen/client/canteenManage";

/** 状态配置枚举 */
enum StatusTagEnum {
  /** 可预约 */
  OPEN = "OPEN",
  /** 部分预约 */
  PARTIAL = "PARTIAL",
  /** 已约满 */
  FULL = "FULL",
  /** 预约未开始 */
  NOT_STARTED = "NOT_STARTED",
  /** 一键催发 */
  URGED = "URGED",
}

/** 状态配置类型 */
interface IStatusTagConfig {
  text: string;
  /** 状态类型  */
  type: StatusTagEnum;
  config: {
    backgroundColor: string;
    textColor: string;
  };
}

/** 食堂可预约状态默认配置 */
const defaultCanteenStatusConfig = {
  [StatusTagEnum.OPEN]: {
    text: "可预约",
    type: StatusTagEnum.OPEN,
    config: {
      backgroundColor: "#E8F5E8",
      textColor: "#52C41A",
    },
  },
  [StatusTagEnum.PARTIAL]: {
    text: "部分可约",
    type: StatusTagEnum.PARTIAL,
    config: {
      backgroundColor: "#FFF7E6",
      textColor: "#FA8C16",
    },
  },
  [StatusTagEnum.FULL]: {
    text: "已约满",
    type: StatusTagEnum.FULL,
    config: {
      backgroundColor: "#FFF2F0",
      textColor: "#FF4D4F",
    },
  },
  [StatusTagEnum.NOT_STARTED]: {
    text: "预约未开始",
    type: StatusTagEnum.NOT_STARTED,
    config: {
      backgroundColor: "#E6F7FF",
      textColor: "#1890FF",
    },
  },
  [StatusTagEnum.URGED]: {
    text: "一键催发",
    type: StatusTagEnum.URGED,
    config: {
      backgroundColor: "#E6F7FF",
      textColor: "#1890FF",
    },
  },
};

/** 食堂数据列表VO */
interface ICanteenItemVO extends ICanteenInfo {
  statusList: IStatusTagConfig[];
}

// 食堂列表数据
const canteenList = ref<ICanteenItemVO[]>([]);

/** loading 态 */
const loading = ref(false);

/** 获取食堂列表数据 */
const fetchCanteenList = async () => {
  try {
    if (loading.value) return;
    loading.value = true;
    const list = await getCanteenList();
    canteenList.value = list.map((item) => {
      return {
        ...item,
        statusList: getCanteenStatus(item),
      };
    });
  } catch (error) {
    console.error("获取食堂列表数据失败:", error);
  } finally {
    loading.value = false;
  }
};

/** 获取食堂状态 */
const getCanteenStatus = (canteen: ICanteenInfo) => {
  const statusList = [];
  // TODO: dev模拟数据
  const status = Object.keys(defaultCanteenStatusConfig);
  // 随机取一个状态添加，如果是已约满状态，则再添加一个一键催发
  const randomStatus = status[Math.floor(Math.random() * status.length)];
  const randomStatusConfig = defaultCanteenStatusConfig[randomStatus];
  statusList.push(randomStatusConfig);
  if (randomStatus === StatusTagEnum.URGED) {
    statusList.unshift(defaultCanteenStatusConfig[StatusTagEnum.FULL]);
  }
  if (randomStatus === StatusTagEnum.FULL) {
    statusList.push(defaultCanteenStatusConfig[StatusTagEnum.URGED]);
  }

  return statusList;
};

/**
 * 处理食堂卡片点击事件
 * @param canteen 被点击的食堂数据
 */
const handleCardClick = (canteen: ICanteenInfo) => {
  console.log("食堂卡片被点击:", canteen);
  // 这里可以跳转到食堂详情页面
};

fetchCanteenList();
</script>

<style scoped lang="scss">
.home-page {
  width: 100%;
  min-height: 100vh;
  padding-bottom: var(--tabbar-height);
  background: linear-gradient(180deg, #3a88f5 0%, #f2f1f6 50%);
  overflow-x: hidden;

  .home-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-image: url("@/assets/images/mobile/canteen.png");
    background-position: center 50px;
    background-repeat: no-repeat;
    background-size: 100% auto;
  }

  .header-section {
    padding: 30px 16px 80px;
    color: white;
    overflow: hidden;
  }

  .page-title {
    font-size: 24px;
    font-weight: 600;
    margin: 0 0 8px 0;
    line-height: 1.2;
  }

  .page-subtitle {
    font-size: 14px;
    opacity: 0.9;
    margin: 0;
    line-height: 1.4;
  }

  .content-section {
    .canteen-item {
      width: 100%;
      border-radius: 9px;
      background: linear-gradient(
        180deg,
        rgba(230, 243, 255, 1) 0%,
        rgba(255, 255, 255, 1) 100%
      );
      margin-bottom: 10px;
      border: 4px solid rgba(255, 255, 255, 1);
      padding: 20px 10px 20px 20px;
      display: flex;
      justify-content: space-between;

      cursor: pointer;
      // &:hover,
      // &:active {
      //   background: rgba(255, 255, 255, 0.9);
      //   border-radius: 9px;
      // }

      .left {
        flex: 1;
        flex-shrink: 0;
        max-width: 60%;

        .canteen-name {
          font-size: 22px;
          font-weight: 500;
          line-height: 1.2;
        }

        .canteen-address {
          font-size: 14px;
          color: #666666;
          line-height: 1.4;

          // 超出宽度范围的文本换行
          word-wrap: break-word;
          overflow: hidden;

          margin-top: 16px;
        }
      }

      .right {
        flex-shrink: 0;
      }
    }

    .status-tag {
      text-align: center;
      font-size: 14px;
      color: #ffffff;
      background-color: var(--color-primary);
      border-radius: 9999px;
      padding: 8px 16px;
      line-height: 1.2;
      margin-bottom: 10px;
    }
  }

  // 食堂列表 - 网格布局
  .canteen-list {
    // display: grid;
    // grid-template-columns: 1fr 1fr;
    gap: 12px;
  }
}

// 移动端适配
@media (max-width: 768px) {
  .header-section {
    padding: 16px 12px 32px;

    .header-content {
      .page-title {
        font-size: 22px;
      }

      .page-subtitle {
        font-size: 13px;
      }
    }
  }

  .content-section {
    padding: 16px 12px 16px;
    margin-top: -16px;
    border-radius: 16px 16px 0 0;
  }

  .canteen-list {
    gap: 10px;
  }
}

// 超小屏幕适配
@media (max-width: 375px) {
  .header-section {
    padding: 14px 10px 28px;

    .header-content {
      .page-title {
        font-size: 20px;
      }

      .page-subtitle {
        font-size: 12px;
      }
    }
  }

  .content-section {
    padding: 14px 10px 14px;
    margin-top: -14px;
    border-radius: 14px 14px 0 0;
  }

  .canteen-list {
    gap: 8px;
  }
}

// 状态栏适配（iPhone X及以上）
@supports (padding-top: env(safe-area-inset-top)) {
  .header-section {
    padding-top: calc(20px + env(safe-area-inset-top));
  }

  @media (max-width: 768px) {
    .header-section {
      padding-top: calc(16px + env(safe-area-inset-top));
    }
  }

  @media (max-width: 375px) {
    .header-section {
      padding-top: calc(14px + env(safe-area-inset-top));
    }
  }
}
</style>
