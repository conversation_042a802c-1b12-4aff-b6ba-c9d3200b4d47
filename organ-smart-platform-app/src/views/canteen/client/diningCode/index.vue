<template>
  <div class="dining-code-page">
    <div class="dining-code-content">
      <!-- 头部背景区域 -->
      <div class="header-section">
        <div class="header-content">
          <h1 class="page-title">就餐码</h1>
          <p class="page-subtitle">出示就餐码，享受美味时光</p>
        </div>
      </div>

      <!-- 预约码卡片区域 -->
      <div
        v-if="startingSoonBooking"
        class="canteen-content-pd booking-code-card"
      >
        <QrCodeInfoCard
          :bookingInfo="startingSoonBooking"
          @refresh="handleRefreshQRCode"
          @view-detail="handleViewBookingDetail"
        />
      </div>

      <!-- 无预约信息卡片区域 -->
      <div v-else class="canteen-content-pd">
        <NoBookingInfoCard />
      </div>

      <!-- 预约信息卡片区域(带遮罩层的弹框) -->
      <div class="booking-info-card">
        <BookingInfoCard
          v-model="showBookingInfo"
          :bookingInfo="startingSoonBooking"
        />
      </div>
    </div>

    <TabBar />
  </div>
</template>

<script setup lang="ts">
import TabBar from "@/components/TabBar/index.vue";
import QrCodeInfoCard from "./_comp/qrCodeInfoCard.vue";
import BookingInfoCard from "./_comp/bookingInfoCard.vue";
import NoBookingInfoCard from "./_comp/noBookingIfnfoCard.vue";
import { IUserBooking } from "@/api/canteen/client/types/booking";
import { getStartingSoonBooking } from "@/api/canteen/client/booking";
import { useToast } from "@/components/Toast";

/** 最近待使用的预约信息 */
const startingSoonBooking = ref<IUserBooking | null>(null);

/** 获取最近待使用的预约信息loadingng */
const loadingBookingInfo = ref(false);

/** 获取最近待使用的预约信息 */
const fetchStartingSoonBooking = async () => {
  try {
    if (loadingBookingInfo.value) return;
    loadingBookingInfo.value = true;
    const booking = await getStartingSoonBooking();
    startingSoonBooking.value = booking;
  } catch (error) {
    console.error("获取最近待使用的预约信息失败:", error);
  } finally {
    loadingBookingInfo.value = false;
  }
};

/** 刷新二维码 */
const handleRefreshQRCode = () => {
  fetchStartingSoonBooking();
};

/** 是否显示预约信息弹窗 */
const showBookingInfo = ref(false);

/** 查看预约详情 */
const handleViewBookingDetail = () => {
  showBookingInfo.value = true;
};

/** 页面是否在加载中 */
const pageLoading = computed(() => {
  return loadingBookingInfo.value;
});

/** 页面loadingng显示 */
const toastHook = useToast();
watch(
  () => pageLoading.value,
  (val) => {
    if (val) {
      toastHook.loading("", {
        overlay: true,
        forbidClick: true,
        duration: 0,
      });
    } else {
      toastHook.clear();
    }
  }
);

/** 初始化数据 */
const initPageData = async () => {
  try {
    await fetchStartingSoonBooking();
  } catch (error) {
    console.error("初始化页面数据失败:", error);
  }
};
initPageData();
</script>

<style scoped lang="scss">
.dining-code-page {
  width: 100%;
  min-height: 100vh;
  padding-bottom: var(--tabbar-height);
  background: linear-gradient(180deg, #3b89f5 0%, #f2f1f6 50%);
  overflow-x: hidden;

  .dining-code-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    // background-image: url("@/assets/images/mobile/canteen.png");
    // background-position: right 40px;
    // background-repeat: no-repeat;
    // background-size: auto 100px;
  }

  .header-section {
    padding: 60px 16px 10px;
    color: white;
    overflow: hidden;
    background-image: url("@/assets/images/mobile/canteen.png");
    background-position: right 30px;
    background-repeat: no-repeat;
    background-size: auto 100px;
  }

  .page-title {
    font-size: 24px;
    font-weight: 600;
    margin: 0 0 8px 0;
    line-height: 1.2;
  }

  .page-subtitle {
    font-size: 14px;
    opacity: 0.9;
    margin: 0;
    line-height: 1.4;
  }
}
</style>
