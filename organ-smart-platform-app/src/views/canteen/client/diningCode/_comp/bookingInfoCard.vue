<template>
  <div v-if="visible" class="booking-info-overlay" @click="handleOverlayClick">
    <div class="booking-info-dialog" @click.stop>
      <!-- 预约信息 -->
      <div class="info-content">
        <!-- 顶部 渐变背景+图片 -->
        <div class="header-section">
          <span class="info-title">预约信息</span>
          <img class="header-img" src="@/assets/images/mobile/canteen.png" />
        </div>

        <!-- 预约信息卡片 -->
        <div class="booking-info-card">
          <div class="card-content">
            <div class="info-item" v-for="info in bookingInfoList">
              <span class="label">{{ info.label }}</span>
              <span
                class="value"
                :class="{ 'canteen-tag': info.isTag, plain: info.isTag }"
                >{{ info.value }}</span
              >
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { IUserBooking } from "@/api/canteen/client/types/booking";
import dayjs from "dayjs";

/**
 * 预约信息弹窗组件Props接口
 */
interface IProps {
  /** 是否显示弹窗 */
  value: boolean;
  /** 预约信息数据 */
  bookingInfo: IUserBooking;
}

/**
 * 组件事件接口
 */
interface IEmits {
  (e: "input", visible: boolean): void;
}

const props = withDefaults(defineProps<IProps>(), {
  value: false,
});
const emit = defineEmits<IEmits>();

/** 是否显示弹窗 */
const visible = computed({
  get() {
    return props.value;
  },
  set(value) {
    emit("input", value);
  },
});

/** 需要显示的预约信息列表 */
const bookingInfoList = computed(() => {
  if (!props.bookingInfo) return [];

  return [
    { label: "餐厅", value: props.bookingInfo.canteenName },
    {
      label: "预约日期",
      value: dayjs(props.bookingInfo.bookingDate).format("M/D"),
    },
    {
      label: "用餐时间",
      value: `${props.bookingInfo.mealName} ${props.bookingInfo.mealTime}`,
    },
    { label: "预约状态", value: props.bookingInfo.statusLabel, isTag: true },
  ];
});

/**
 * 处理遮罩层点击事件
 */
const handleOverlayClick = () => {
  emit("input", false);
};
</script>

<style scoped lang="scss">
.booking-info-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;

  .booking-info-dialog {
    background: #ffffff;
    border-radius: 10px;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;

    .info-content {
      overflow: hidden;

      .header-section {
        height: 90px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        overflow: hidden;
        background: linear-gradient(180deg, #b1ccf6ff 0%, #ffffff 100%);

        .info-title {
          font-size: 18px;
          font-weight: 600;
          color: var(--color-primary);
          opacity: 1;
          padding-left: 20px;
        }

        .header-img {
          width: auto;
          height: 100%;
          object-fit: cover;
          opacity: 0.75;
        }
      }

      // 预约信息卡片
      .booking-info-card {
        width: 100%;
        z-index: 2;
        border-radius: 4px;
        padding: 20px;
        background-color: #ffffff;

        .info-item {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 6px 0;
          font-size: 16px;
          border-bottom: 1px solid #f0f8ff;
          margin-bottom: 10px;

          &:last-child {
            margin-bottom: 0;
          }

          .label {
            flex-shrink: 0;
            width: 80px;
            color: #808080;
          }

          .value {
            text-align: right;
            color: #1d1e20;

            &.canteen-tag {
              color: var(--color-primary);
            }
          }
        }
      }
    }
  }
}
</style>
