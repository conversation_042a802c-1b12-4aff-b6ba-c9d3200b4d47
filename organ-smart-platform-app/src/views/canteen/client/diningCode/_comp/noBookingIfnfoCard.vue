<template>
  <div class="canteen-card no-booking-info-card">
    <div class="card-header">
      <img class="info-icon" src="@/assets/images/canteen/info.png" />
      <div class="card-title">暂无有效预约</div>
    </div>
    <div class="card-content">
      <div class="no-booking-tips">
        <i class="icon el-icon-warning"></i>
        您未预约，请到首页预约食堂餐次
      </div>
    </div>
    <div class="card-footer">
      <div class="btn-group">
        <button
          class="canteen-btn btn-primary btn"
          @click.stop="handleImmediateBooking"
        >
          立即预约
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router/composables";
import { BIZ_MODULE_ROUTE_PREFIX } from "@/views/canteen/configs/constants";

const router = useRouter();

/** 立即预约 */
const handleImmediateBooking = () => {
  router.push({
    path: `${BIZ_MODULE_ROUTE_PREFIX}/client/home`,
  });
};
</script>

<style scoped lang="scss">
.no-booking-info-card {
  .card-header {
    text-align: center;
    width: 100%;
    margin-top: 0 auto 16px;

    .info-icon {
      width: 50%;
      height: auto;
      object-fit: cover;
      margin-bottom: 20px;
    }

    .card-title {
      font-size: 22px;
      font-weight: 500;
      color: var(--color-primary);
    }
  }

  .card-content {
    text-align: center;
    padding: 40px 0;
  }

  .no-booking-tips {
    font-size: 15px;
    font-weight: 400;
    color: #f5a629;
    margin-bottom: 20px;
    width: 100%;
    text-align: center;
    background-color: #fff7eb;
    padding: 6px 0;
    border: 1px solid #ffebcc;

    .icon {
      margin-right: 4px;
      rotate: 180deg;
    }
  }

  .card-footer {
    .btn-group {
      .btn {
        width: 100%;
        font-size: 18px;
        padding: 16px 0;
      }
    }
  }
}
</style>
