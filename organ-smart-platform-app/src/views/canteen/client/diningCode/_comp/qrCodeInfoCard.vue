<template>
  <div v-if="props.bookingInfo" class="qr-code-info-card">
    <div class="card-header">
      <div class="card-title">
        <span>{{ bookingInfo.canteenName || "" }}</span>
        <span class="date">{{ displayDate }}</span>
      </div>
      <div class="card-subtitle">
        <div class="tag">{{ props.bookingInfo.mealName || "" }}</div>
        <div>{{ props.bookingInfo.mealTime || "" }}</div>
      </div>
    </div>
    <div class="card-content">
      <div class="qr-code-section">
        <canvas ref="qrCodeCanvas" class="qr-code"></canvas>
        <div class="refresh-tips">{{ refreshCountdown }}s自动刷新</div>
      </div>
      <div class="tips">
        <i class="icon el-icon-warning"></i>
        请在就餐时间内向工作人员出示此码
      </div>
    </div>
    <div class="card-footer">
      <div class="btn-group">
        <button class="canteen-btn btn-secondary btn" @click="handleRefresh">
          刷新
        </button>
        <button class="canteen-btn btn-primary btn" @click="handleViewDetail">
          查看详情
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { IUserBooking } from "@/api/canteen/client/types/booking";
import dayjs from "dayjs";
import { withDefaults } from "vue";
import QRCode from "qrcode";

/** 传递进来的参数 */
interface IProps {
  /** 预约信息 */
  bookingInfo: IUserBooking;
}

/** 事件定义 */
interface IEmits {
  (e: "refresh"): void;
  (e: "view-detail"): void;
}

const props = withDefaults(defineProps<IProps>(), {});
const emits = defineEmits<IEmits>();

/** 挂载二维码的canvas */
const qrCodeCanvas = ref<HTMLCanvasElement | null>(null);

/** 展示日期 */
const displayDate = computed(() => {
  const [month, day] = dayjs(props.bookingInfo.bookingDate)
    .format("M/D")
    .split("/");
  return `${month}月${day}日`;
});

/** 生成二维码 */
const generateQRCode = (url: string) => {
  if (!qrCodeCanvas.value) {
    return;
  }
  try {
    const qrCodeSize = window.innerWidth < 768 ? 170 : 210;
    QRCode.toCanvas(qrCodeCanvas.value, url, {
      width: qrCodeSize,
      margin: 0,
      color: {
        dark: "#000000",
        light: "#FFFFFF",
      },
    });
  } catch (error) {
    console.error("生成二维码失败:", error);
  }
};

/** 刷新二维码 */
const handleRefresh = () => {
  emits("refresh");
};

/** 查看详情 */
const handleViewDetail = () => {
  emits("view-detail");
};

/** 定时器定时触发刷新 */
const refreshTimer = ref(null);
/** 刷新间隔 */
const refreshCountdownInitVal = 59;
/** 刷新倒计时 */
const refreshCountdown = ref(refreshCountdownInitVal);
/** 启动定时器 */
const startRefreshTimer = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value);
  }
  refreshCountdown.value = refreshCountdownInitVal;
  refreshTimer.value = setInterval(() => {
    refreshCountdown.value--;
    if (refreshCountdown.value <= 0) {
      clearInterval(refreshTimer.value);
      handleRefresh();
    }
  }, 1000);
};

/** 初始化页面数据 */
const initPageData = () => {
  generateQRCode(props.bookingInfo.bookingCode);
  startRefreshTimer();
};

/** 每次数据更新时都重新生成二维码并且启动定时器 */
watch(
  () => props.bookingInfo,
  (_newVal) => {
    initPageData();
  },
  { deep: true }
);

onMounted(() => {
  initPageData();
});

onUnmounted(() => {
  clearInterval(refreshTimer.value);
});
</script>

<style scoped lang="scss">
.qr-code-info-card {
  border-radius: 8px;
  padding: 20px;
  background: linear-gradient(180deg, #e6f3ff 0%, #ffffff 100%);
  border: 1px solid #ffffff;

  .card-header {
    margin-bottom: 16px;

    .card-title {
      font-size: 20px;
      font-weight: 500;
      color: #383838;
      margin: 0;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .date {
        font-weight: normal;
        font-size: 16px;
        color: #808080;
      }
    }

    .card-subtitle {
      font-size: 16px;
      color: var(--color-primary);
      margin: 0;
      display: flex;
      align-items: center;

      margin-top: 10px;

      .tag {
        color: #f0f8ff;
        background: var(--color-primary);
        padding: 6px 12px;
        border-radius: 9999px;
        font-size: 14px;
        line-height: 1;

        margin-right: 10px;
      }
    }
  }

  .card-content {
    width: 100%;

    .qr-code-section {
      border-radius: 8px;
      text-align: center;
      padding: 24px 24px 10px;
      background-color: #ffffff;
      margin: 0 auto 10px;
      width: fit-content;

      .qr-code {
        width: 210px;
        height: 210px;
        margin: 0 auto;
        background: #f0f0f0;
        border-radius: 8px;
      }

      .refresh-tips {
        font-size: 16px;
        color: #a6a6a6;
        margin-top: 8px;
      }
    }

    .tips {
      //   font-size: 12px;
      color: var(--color-primary);
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 400;
      width: 100%;
      padding: 4px 0;
      border-radius: 2px;
      background-color: var(--color-primary-plain-bg);
      margin-bottom: 10px;

      .icon {
        margin-right: 4px;
        rotate: 180deg;
      }
    }
  }

  .card-footer {
    .btn-group {
      display: flex;
      justify-content: space-between;
      column-gap: 20px;

      .btn {
        flex: 1;
        font-size: 16px;
      }
    }
  }
}
</style>
