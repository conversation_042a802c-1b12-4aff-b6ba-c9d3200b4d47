<script setup lang="ts">
import type { QueryOrderParameters } from '@/api/payment/records'
import { Button, DatePicker, Form, FormItem, Input, Option, Select } from 'element-ui'
import { onMounted, ref } from 'vue'
import { getPaymentOrderStats, getPaymentRecordsPage, MembershipType } from '@/api/payment/records'
import { defineCommonTablePropsColumn } from '@/components/CommonTable/hooks'
import CommonTable from '@/components/CommonTable/index.vue'
import { useResettableRef } from '@/hooks/useResettable'
import { formatDateTime } from '@/utils/date'
import { formatNumberWithCommas } from '@/utils/number'
import { removeEmptyValues } from '@/utils/object'

// 统计数据
const statsData = ref({
  timeRange: '20240112-20240412',
  totalAmount: 0,
  totalMembers: 0,
  totalOrders: 0,
})

// 搜索表单数据 - 根据接口入参字段定义
const [formData, resetFormData] = useResettableRef({
  /** 订单编号 */
  orderNo: '',
  /** 用户姓名 */
  userName: '',
  /** 用户手机号 */
  userPhone: '',
  /** 会员类型 */
  membershipType: '' as MembershipType | '',
  /** 支付时间 */
  paymentTime: [],
})

// 表单引用
const formRef = ref<InstanceType<typeof Form>>()

// 会员类型映射
const memberTypeMap = {
  [MembershipType.Basic]: '体验版',
  [MembershipType.Premium]: '一个月',
  [MembershipType.Vip]: '半年',
}

// 表格列配置
const tableColumns = defineCommonTablePropsColumn([
  {
    label: '订单编号',
    prop: 'orderNo',
    width: '300px',
  },
  {
    label: '姓名',
    prop: 'userName',
    width: '140px',
  },
  {
    label: '手机号',
    prop: 'userPhone',
  },
  {
    label: '会员类型',
    prop: 'membershipType',
    width: '100px',
    formatter: (_row: any, _column: any, cellValue: any) => {
      return memberTypeMap[cellValue] || cellValue
    },
  },
  {
    label: '支付日期',
    prop: 'paymentTime',
    formatter: (_row: any, _column: any, cellValue: any) => {
      return formatDateTime(cellValue)
    },
  },
  {
    label: '会员有效期',
    prop: 'validityEndTime',
    formatter: (_row: any, _column: any, cellValue: any) => {
      return formatDateTime(cellValue)
    },
  },
  {
    label: '支付金额（元）',
    prop: 'actualAmount',
  },
])

// CommonTable 引用
const tableRef = ref<InstanceType<typeof CommonTable>>()

// 获取统计数据
async function fetchStatsData() {
  try {
    const response = await getPaymentOrderStats()
    statsData.value = response
  }
  catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 搜索处理
function handleSearch(initRequest: (params?: any) => void) {
  // 构建查询参数
  const queryParams = {
    ...formData.value,
    paymentTime: undefined,
    paymentStartTime: formData.value.paymentTime?.[0],
    paymentEndTime: formData.value.paymentTime?.[1],
  }

  // 过滤空值（undefined、null、空字符串）
  const filteredParams = removeEmptyValues(queryParams)

  initRequest(filteredParams)
}

// 重置处理
function handleReset(initRequest: (params?: any) => void) {
  // 重置表单
  formRef.value?.resetFields()
  // 使用 resetFormData 函数重置表单数据
  resetFormData()
  // 如果传入了 initRequest 函数，优先使用它
  initRequest({})
}

// 导出加载状态
const exportLoading = ref(false)

// 导出处理
async function handleExport() {
  if (exportLoading.value)
    return

  exportLoading.value = true
  try {
    // 构建导出参数，使用当前的查询条件
    const exportParams = {
      orderNo: formData.value.orderNo,
      userName: formData.value.userName,
      userPhone: formData.value.userPhone,
      membershipType: formData.value.membershipType,
      paymentStartTime: formData.value.paymentTime?.[0],
      paymentEndTime: formData.value.paymentTime?.[1],
    }

    // 过滤空值
    const filteredParams = removeEmptyValues(exportParams) as QueryOrderParameters

    // 使用原生 fetch 调用导出接口
    const response = await fetch('/admin-api/membershipOrder/paymentRecords/export', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // 这里可能需要添加认证头，根据您的项目配置
        // 'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(filteredParams),
    })

    if (!response.ok) {
      throw new Error(`导出失败: ${response.status} ${response.statusText}`)
    }

    // 获取文件流
    const blob = await response.blob()

    // 生成文件名（包含当前时间）
    const now = new Date()
    const timestamp = now.toISOString().slice(0, 19).replace(/[:-]/g, '').replace('T', '_')
    const filename = `支付记录_${timestamp}.xlsx`

    // 创建下载链接并触发下载
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()

    // 清理
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  }
  catch (error) {
    console.error('导出失败:', error)
    // 这里可以添加错误提示，比如使用 Element UI 的 Message 组件
    alert('导出失败，请稍后重试')
  }
  finally {
    exportLoading.value = false
  }
}

// 页面初始化
onMounted(() => {
  fetchStatsData()
})
</script>

<template>
  <div class="records-page size-full flex-col">
    <!-- 头部数据统计 -->
    <div class="stats-header">
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon blue">
            <i class="el-icon-s-data" />
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ statsData.totalOrders.toLocaleString() }}</div>
            <div class="stat-label">支付订单量</div>
            <div class="stat-unit">次</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon purple">
            <i class="el-icon-user" />
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ statsData.totalMembers.toLocaleString() }}</div>
            <div class="stat-label">支付会员量</div>
            <div class="stat-unit">人</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon gray">
            <i class="el-icon-date" />
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ statsData.timeRange }}</div>
            <div class="stat-label">订单时间区间</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon orange">
            <i class="el-icon-money" />
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ statsData.totalAmount.toLocaleString() }}</div>
            <div class="stat-label">支付总金额</div>
            <div class="stat-unit">元</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="flex-1 overflow-hidden">
      <CommonTable
        ref="tableRef"
        :fetch="getPaymentRecordsPage"
        :columns="tableColumns"
        border
        stripe
      >
        <!-- 搜索表单放在 header 插槽中 -->
        <template #header="{ initializeRequest }">
          <div class="search-form">
            <Form
              ref="formRef"
              :model="formData"
              inline
              class="search-form-content"
            >
              <FormItem prop="orderNo">
                <Input
                  v-model="formData.orderNo"
                  placeholder="输入订单编号"
                  class="search-input"
                />
              </FormItem>

              <FormItem prop="userName">
                <Input
                  v-model="formData.userName"
                  placeholder="输入姓名"
                  class="search-input"
                />
              </FormItem>

              <FormItem prop="userPhone">
                <Input
                  v-model="formData.userPhone"
                  placeholder="输入手机号"
                  class="search-input"
                />
              </FormItem>

              <FormItem prop="membershipType">
                <Select
                  v-model="formData.membershipType"
                  placeholder="选择会员类型"
                  class="search-select"
                >
                  <Option value="" label="全部" />
                  <Option :value="MembershipType.Basic" label="体验版" />
                  <Option :value="MembershipType.Premium" label="一个月" />
                  <Option :value="MembershipType.Vip" label="半年" />
                </Select>
              </FormItem>

              <FormItem prop="paymentTime">
                <DatePicker
                  v-model="formData.paymentTime"
                  type="daterange"
                  value-format="yyyy-MM-dd"
                  range-separator="~"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  class="search-date"
                />
              </FormItem>

              <FormItem>
                <div class="form-actions">
                  <Button type="primary" @click="() => handleSearch(initializeRequest)">查询</Button>
                  <Button @click="() => handleReset(initializeRequest)">重置</Button>
                  <Button
                    type="primary"
                    :loading="exportLoading"
                    @click="handleExport"
                  >
                    {{ exportLoading ? '导出中...' : '导出' }}
                  </Button>
                </div>
              </FormItem>
            </Form>
          </div>
        </template>

        <!-- 支付金额插槽 -->
        <template #actualAmount="{ row }">
          <span class="amount-text">{{ formatNumberWithCommas(row.actualAmount, 2) || '0.00' }}</span>
        </template>
      </CommonTable>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.records-page {
  padding: 20px;
  background-color: #f5f7fa;
}

// 头部统计卡片
.stats-header {
  margin-bottom: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;

  &.blue {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  }

  &.purple {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
  }

  &.gray {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
  }

  &.orange {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    color: white;
  }
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 2px;
}

.stat-unit {
  font-size: 12px;
  color: #999;
}

// 搜索表单
.search-form {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  .search-form-content {
    :deep(.el-form-item) {
      margin-right: 16px;
      margin-bottom: 16px;

      .el-form-item__content {
        margin-left: 0 !important;
      }
    }
  }
}

.search-input,
.search-select,
.search-date {
  width: 240px;
}

.form-actions {
  display: flex;
  gap: 12px;
}

// 表格自定义样式
.time-detail {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

.amount {
  color: #f56c6c;
  font-weight: 500;
}

// 支付金额文本样式
.amount-text {
  color: #DD0000;
  font-weight: 600;
  font-size: 14px;
}
</style>
