import axios from 'axios'
import { Message } from 'element-ui'
import { ApiCode } from './config'

/**
 * 过滤对象中值为空字符串的属性
 * @param obj 要过滤的对象
 * @returns 过滤后的新对象
 */
function filterEmptyStrings(obj: any): any {
  if (!obj || typeof obj !== 'object' || Array.isArray(obj)) {
    return obj
  }

  const filtered: any = {}
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const value = obj[key]
      // 只过滤掉空字符串，保留其他 falsy 值（如 0, false, null, undefined）
      if (value !== '') {
        filtered[key] = value
      }
    }
  }
  return filtered
}

const service = axios.create({
  /** 1分钟 */
  timeout: 1000 * 60,
  baseURL: process.env.NODE_ENV === 'development' ? '/admin-api' : `${process.env.VUE_APP_BASE_API}/admin-api`,
})

/** 请求拦截 */
service.interceptors.request.use(
  (config) => {
    // 过滤 params 中的空字符串
    if (config.params && typeof config.params === 'object') {
      config.params = filterEmptyStrings(config.params)
    }

    // 过滤 data 中的空字符串
    if (config.data && typeof config.data === 'object') {
      config.data = filterEmptyStrings(config.data)
    }

    // TODO 测试代码，后续删除
    if (config.params) {
      config.params.userId = 1
    }
    if (config.data) {
      config.data.userId = 1
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

/** 响应拦截 */
service.interceptors.response.use(
  (response) => {
    const res: {
      code: number
      data: any
      msg: string
    } = response.data

    /** 正常对接 */
    if (res.code === ApiCode.Success) {
      return res.data
    }

    /** 登录失效 */
    if (ApiCode.Logout.includes(res.code)) {
      // 登录失效操作
      return Promise.reject(res)
    }

    /** 没有被拦截的，统一认为是错误，直接弹出错误信息 */
    Message.error(res.msg)

    return Promise.reject(res)
  },
  (error) => {
    return Promise.reject(error)
  },
)

export default service
