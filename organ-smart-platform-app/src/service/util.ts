import service from '@/service'

interface ListResponseData<T> {
  list: T[]
  total: string
}

/** 创建一个针对于列表的请求方法 */
export function createListRequest<T extends object = object, R = any>(path: string, method: 'get' | 'post' = 'get') {
  return (params: PaginatedRequestParams<T>) => service<PaginatedRequestParams<T>, ListResponseData<R>>({
    url: path,
    method,
    ...(method === 'get' ? { params } : { data: params }),
  }).then(transformToPaginationModel)
}

/** 创建一个针对列表的响应数据 */
export function transformToPaginationModel<T>(res: {
  list: T[]
  total: string
}): PaginatedResponseData<T> {
  return {
    list: res.list || [],
    total: Number(res.total || 0),
  }
}
