export default {
  presets: [
    [
      '@babel/preset-env',
      {
        // 根据 browserslist 配置自动确定需要的 polyfill
        useBuiltIns: 'usage',
        // 指定 core-js 版本
        corejs: 3,
        // 保留模块语法，让 webpack 处理
        modules: false,
        // 目标浏览器配置
        targets: {
          browsers: [
            '> 1%',
            'last 2 versions',
            'not dead',
            'not ie <= 11',
          ],
        },
      },
    ],
    [
      '@babel/preset-typescript',
      {
        // 允许所有 TypeScript 语法
        allExtensions: true,
        // 处理 .vue 文件中的 TypeScript
        isTSX: false,
      },
    ],
  ],
  plugins: [
    // Element UI 按需导入插件 - 修复表格组件渲染问题
    // [
    //   'component',
    //   {
    //     libraryName: 'element-ui',
    //     styleLibraryName: 'theme-chalk',
    //   },
    // ],
  ],
  // 环境特定配置
  env: {
    development: {
      // 开发环境保留函数名，便于调试
      compact: false,
    },
    production: {
      // 生产环境移除 console.log
      plugins: [
        [
          'transform-remove-console',
          {
            exclude: ['error', 'warn'],
          },
        ],
      ],
    },

  },
}
